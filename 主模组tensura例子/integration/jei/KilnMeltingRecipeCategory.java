package com.github.manasmods.tensura.integration.jei;

import com.github.manasmods.tensura.data.pack.KilnMoltenMaterial;
import com.github.manasmods.tensura.data.pack.TensuraData;
import com.github.manasmods.tensura.data.recipe.KilnMeltingRecipe;
import com.github.manasmods.tensura.data.recipe.KilnMixingRecipe;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.util.RenderUtils;
import com.mojang.blaze3d.vertex.PoseStack;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import mezz.jei.api.constants.VanillaTypes;
import mezz.jei.api.gui.builder.IRecipeLayoutBuilder;
import mezz.jei.api.gui.drawable.IDrawable;
import mezz.jei.api.gui.ingredient.IRecipeSlotsView;
import mezz.jei.api.helpers.IGuiHelper;
import mezz.jei.api.recipe.IFocusGroup;
import mezz.jei.api.recipe.RecipeIngredientRole;
import mezz.jei.api.recipe.RecipeType;
import mezz.jei.api.recipe.category.IRecipeCategory;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.item.Item;

public class KilnMeltingRecipeCategory implements IRecipeCategory<KilnMeltingRecipe> {
   private static final ResourceLocation TEXTURE = new ResourceLocation("tensura", "textures/gui/kiln/jei_melting.png");
   private static final Component TITLE = Component.m_237115_("tensura.jei.melting.title");
   static final ResourceLocation UID = new ResourceLocation("tensura", "kiln/melting");
   private final IDrawable background;
   private final IDrawable icon;

   public KilnMeltingRecipeCategory(IGuiHelper guiHelper) {
      this.icon = guiHelper.createDrawableIngredient(VanillaTypes.ITEM_STACK, ((Item)TensuraBlocks.Items.KILN.get()).m_7968_());
      this.background = guiHelper.createDrawable(TEXTURE, 0, 0, 177, 121);
   }

   public RecipeType<KilnMeltingRecipe> getRecipeType() {
      return TensuraJeiPlugin.KILN_MELTING_RECIPE;
   }

   public Component getTitle() {
      return TITLE;
   }

   public IDrawable getBackground() {
      return this.background;
   }

   public IDrawable getIcon() {
      return this.icon;
   }

   public void setRecipe(IRecipeLayoutBuilder builder, KilnMeltingRecipe recipe, IFocusGroup focuses) {
      builder.addSlot(RecipeIngredientRole.INPUT, 80, 32).addIngredients(recipe.getInput());
   }

   public void draw(KilnMeltingRecipe recipe, IRecipeSlotsView recipeSlotsView, PoseStack stack, double mouseX, double mouseY) {
      Iterator var8 = TensuraData.getKilnMoltenMaterials().iterator();

      while(var8.hasNext()) {
         KilnMoltenMaterial moltenMaterial = (KilnMoltenMaterial)var8.next();
         if (!recipe.getMoltenType().equals(KilnMixingRecipe.EMPTY) && moltenMaterial.getMoltenType().equals(recipe.getMoltenType())) {
            RenderUtils.renderMoltenMaterial(stack, moltenMaterial, recipe.getMoltenAmount(), 144);
         }

         if (!recipe.getSecondaryType().equals(KilnMixingRecipe.EMPTY) && moltenMaterial.getMoltenType().equals(recipe.getSecondaryType())) {
            RenderUtils.renderMoltenMaterial(stack, moltenMaterial, recipe.getSecondaryAmount(), 144);
         }
      }

   }

   public List<Component> getTooltipStrings(KilnMeltingRecipe recipe, IRecipeSlotsView recipeSlotsView, double mouseX, double mouseY) {
      ArrayList<Component> tooltip = new ArrayList();
      Iterator var8 = TensuraData.getKilnMoltenMaterials().iterator();

      while(var8.hasNext()) {
         KilnMoltenMaterial moltenMaterial = (KilnMoltenMaterial)var8.next();
         if (!recipe.getMoltenType().equals(KilnMixingRecipe.EMPTY)) {
            if (moltenMaterial.isRightBar()) {
               if (this.isHovering(145, 6, 13, 74, mouseX, mouseY) && !recipe.getMoltenType().equals(KilnMixingRecipe.EMPTY) && moltenMaterial.isRightBar()) {
                  if (moltenMaterial.getMoltenType().equals(recipe.getMoltenType())) {
                     tooltip.add(RenderUtils.toolTipFromMoltenMaterial(moltenMaterial, (float)recipe.getMoltenAmount() / 4.0F, 36));
                  }

                  if (moltenMaterial.getMoltenType().equals(recipe.getSecondaryType())) {
                     tooltip.add(RenderUtils.toolTipFromMoltenMaterial(moltenMaterial, (float)recipe.getSecondaryAmount() / 4.0F, 36));
                  }
               }
            } else if (this.isHovering(18, 6, 13, 74, mouseX, mouseY)) {
               if (moltenMaterial.getMoltenType().equals(recipe.getMoltenType())) {
                  tooltip.add(RenderUtils.toolTipFromMoltenMaterial(moltenMaterial, (float)recipe.getMoltenAmount(), 144));
               }

               if (moltenMaterial.getMoltenType().equals(recipe.getSecondaryType())) {
                  tooltip.add(RenderUtils.toolTipFromMoltenMaterial(moltenMaterial, (float)recipe.getSecondaryAmount(), 144));
               }
            }
         }
      }

      return tooltip;
   }

   protected boolean isHovering(int pX, int pY, int pWidth, int pHeight, double pMouseX, double pMouseY) {
      return pMouseX >= (double)(pX - 1) && pMouseX < (double)(pX + pWidth + 1) && pMouseY >= (double)(pY - 1) && pMouseY < (double)(pY + pHeight + 1);
   }
}
