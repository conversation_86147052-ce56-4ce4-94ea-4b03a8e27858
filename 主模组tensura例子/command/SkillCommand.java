package com.github.manasmods.tensura.command;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.ISpatialStorage;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.ability.magic.Magic;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.command.argument.SkillArgument;
import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.builder.LiteralArgumentBuilder;
import com.mojang.brigadier.suggestion.SuggestionProvider;
import java.util.Iterator;
import java.util.Optional;
import java.util.function.Predicate;
import net.minecraft.ChatFormatting;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.commands.SharedSuggestionProvider;
import net.minecraft.commands.arguments.EntityArgument;
import net.minecraft.network.chat.Component;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.event.RegisterCommandsEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class SkillCommand {
   private static final SuggestionProvider<CommandSourceStack> SLOT_SUGGEST = (context, builder) -> {
      return SharedSuggestionProvider.m_82967_(new String[]{"1", "2", "3"}, builder);
   };
   private static final SuggestionProvider<CommandSourceStack> PRESET_SUGGEST = (context, builder) -> {
      return SharedSuggestionProvider.m_82967_(new String[]{"1", "2", "3", "4", "5", "6", "7", "8", "9"}, builder);
   };

   @SubscribeEvent
   public static void register(RegisterCommandsEvent e) {
      e.getDispatcher().register((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("skill").then(((LiteralArgumentBuilder)Commands.m_82127_("checkFlight").requires((stack) -> {
         return stack.m_6761_(2);
      })).then(Commands.m_82129_("players", EntityArgument.m_91470_()).executes((context) -> {
         Iterator var1 = EntityArgument.m_91477_(context, "players").stream().toList().iterator();

         while(var1.hasNext()) {
            Player player = (Player)var1.next();
            if (SkillUtils.canFlyLegit(player)) {
               ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.skill.check_flight", new Object[]{player.m_7755_()}).m_130940_(ChatFormatting.DARK_GREEN), true);
            } else {
               ((CommandSourceStack)context.getSource()).m_81354_(Component.m_237110_("tensura.skill.check_flight.false", new Object[]{player.m_7755_()}).m_130940_(ChatFormatting.RED), true);
            }
         }

         return 1;
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("storage").then(Commands.m_82129_("skill", SkillArgument.skill()).suggests(SkillArgument.getObtainedSpatialStorageSuggestions()).executes((context) -> {
         return openSpatialStorage((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), SkillArgument.getSkill(context, "skill"));
      }))).then(Commands.m_82127_("add").then(Commands.m_82129_("skill", SkillArgument.skill()).suggests(SkillArgument.getObtainedSpatialStorageSuggestions()).executes((context) -> {
         return addToSpatialStorage((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), SkillArgument.getSkill(context, "skill"));
      }))))).then(Commands.m_82127_("set").then(Commands.m_82129_("skill", SkillArgument.skill()).suggests(SkillArgument.getObtainedSkillSuggestions()).then(Commands.m_82129_("slot", IntegerArgumentType.integer(1, 3)).suggests(SLOT_SUGGEST).executes((context) -> {
         return setSkill((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), SkillArgument.getSkill(context, "skill"), IntegerArgumentType.getInteger(context, "slot"));
      }))))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("toggle").then(Commands.m_82129_("ability", SkillArgument.skill()).suggests(SkillArgument.getObtainedSkillSuggestions()).executes((context) -> {
         return toggleSkill((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), SkillArgument.getSkill(context, "ability"));
      }))).then(((LiteralArgumentBuilder)Commands.m_82127_("everything").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), true, (skill) -> {
            return true;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), false, (skill) -> {
            return true;
         });
      })))).then(((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)((LiteralArgumentBuilder)Commands.m_82127_("skill").then(((LiteralArgumentBuilder)Commands.m_82127_("all").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), true, (skill) -> {
            return skill instanceof Skill;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), false, (skill) -> {
            return skill instanceof Skill;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("resistance").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), true, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.RESISTANCE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), false, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.RESISTANCE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("intrinsic").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), true, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.INTRINSIC)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), false, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.INTRINSIC)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("common").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), true, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.COMMON)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), false, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.COMMON)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("extra").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), true, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.EXTRA)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), false, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.EXTRA)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("unique").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), true, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.UNIQUE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), false, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.UNIQUE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("ultimate").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), true, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.ULTIMATE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), false, (manasSkill) -> {
            boolean var10000;
            if (manasSkill instanceof Skill) {
               Skill skill = (Skill)manasSkill;
               if (skill.getType().equals(Skill.SkillType.ULTIMATE)) {
                  var10000 = true;
                  return var10000;
               }
            }

            var10000 = false;
            return var10000;
         });
      }))))).then(((LiteralArgumentBuilder)Commands.m_82127_("magic").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), true, (manasSkill) -> {
            return manasSkill instanceof Magic;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), false, (manasSkill) -> {
            return manasSkill instanceof Magic;
         });
      })))).then(((LiteralArgumentBuilder)Commands.m_82127_("battlewill").then(Commands.m_82127_("on").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), true, (manasSkill) -> {
            return manasSkill instanceof Battewill;
         });
      }))).then(Commands.m_82127_("off").executes((context) -> {
         return toggleEverything((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), false, (manasSkill) -> {
            return manasSkill instanceof Battewill;
         });
      }))))).then(((LiteralArgumentBuilder)Commands.m_82127_("preset").then(Commands.m_82127_("set").then(Commands.m_82129_("number", IntegerArgumentType.integer(1, 9)).suggests(PRESET_SUGGEST).executes((context) -> {
         return setPreset((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), IntegerArgumentType.getInteger(context, "number"));
      })))).then(Commands.m_82127_("name").then(Commands.m_82129_("number", IntegerArgumentType.integer(1, 9)).suggests(PRESET_SUGGEST).then(Commands.m_82129_("name", StringArgumentType.word()).executes((context) -> {
         return setPresetName((CommandSourceStack)context.getSource(), ((CommandSourceStack)context.getSource()).m_81375_(), StringArgumentType.getString(context, "name"), IntegerArgumentType.getInteger(context, "number"));
      }))))));
   }

   private static int openSpatialStorage(CommandSourceStack stack, Player player, ManasSkill manasSkill) {
      if (manasSkill instanceof ISpatialStorage) {
         ISpatialStorage spatialStorage = (ISpatialStorage)manasSkill;
         SkillStorage var4 = SkillAPI.getSkillsFrom(player);
         Optional instance = var4.getSkill(manasSkill);
         if (instance.isEmpty()) {
            stack.m_81352_(Component.m_237110_("tensura.skill.do_not_have", new Object[]{player.m_7755_(), manasSkill.getName()}));
         } else {
            spatialStorage.openSpatialStorage(player, (ManasSkillInstance)instance.get());
            player.f_19853_.m_6263_((Player)null, player.m_20185_(), player.m_20186_(), player.m_20189_(), SoundEvents.f_11889_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

         return 1;
      } else {
         stack.m_81352_(Component.m_237110_("tensura.skill.spatial_storage.failed", new Object[]{manasSkill.getName()}));
         return 1;
      }
   }

   private static int addToSpatialStorage(CommandSourceStack stack, Player player, ManasSkill manasSkill) {
      if (manasSkill instanceof ISpatialStorage) {
         ISpatialStorage spatialStorage = (ISpatialStorage)manasSkill;
         SkillStorage var4 = SkillAPI.getSkillsFrom(player);
         Optional instance = var4.getSkill(manasSkill);
         if (instance.isEmpty()) {
            stack.m_81352_(Component.m_237110_("tensura.skill.do_not_have", new Object[]{player.m_7755_(), manasSkill.getName()}));
         } else if (spatialStorage.addItemToSpatialStorage((ManasSkillInstance)instance.get(), player, player.m_21205_())) {
            player.m_8061_(EquipmentSlot.MAINHAND, ItemStack.f_41583_);
            player.f_19853_.m_6263_((Player)null, player.m_20185_(), player.m_20186_(), player.m_20189_(), SoundEvents.f_11889_, SoundSource.PLAYERS, 1.0F, 1.0F);
         } else {
            stack.m_81352_(Component.m_237110_("tensura.skill.spatial_storage.full", new Object[]{manasSkill.getName()}));
         }

         return 1;
      } else {
         stack.m_81352_(Component.m_237110_("tensura.skill.spatial_storage.failed", new Object[]{manasSkill.getName()}));
         return 1;
      }
   }

   private static int setSkill(CommandSourceStack stack, Player player, ManasSkill manasSkill, int slot) {
      if (manasSkill.getRegistryName() != null && manasSkill instanceof TensuraSkill) {
         TensuraSkill skill = (TensuraSkill)manasSkill;
         SkillStorage var5 = SkillAPI.getSkillsFrom(player);
         Optional instance = var5.getSkill(manasSkill);
         if (!instance.isEmpty() && instance.get() instanceof TensuraSkillInstance) {
            if (!skill.canBeSlotted((ManasSkillInstance)instance.get())) {
               stack.m_81352_(Component.m_237110_("tensura.skill.skill_set.failed", new Object[]{manasSkill.getName()}));
               return 1;
            }

            TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
               cap.setInstanceInSlot((ManasSkillInstance)instance.get(), slot - 1);
               stack.m_81354_(Component.m_237110_("tensura.skill.skill_set", new Object[]{manasSkill.getName(), slot}), false);
               TensuraSkillCapability.sync(player);
            });
         } else {
            stack.m_81352_(Component.m_237110_("tensura.skill.do_not_have", new Object[]{player.m_7755_(), manasSkill.getName()}));
         }

         return 1;
      } else {
         stack.m_81352_(Component.m_237110_("tensura.skill.invalid", new Object[]{Component.m_237115_(manasSkill.toString())}));
         return 1;
      }
   }

   private static int toggleSkill(CommandSourceStack stack, Player player, ManasSkill skill) {
      if (skill.getRegistryName() == null) {
         stack.m_81352_(Component.m_237110_("tensura.skill.invalid", new Object[]{Component.m_237115_(skill.toString())}));
         return 1;
      } else {
         SkillStorage storage = SkillAPI.getSkillsFrom(player);
         Optional<ManasSkillInstance> instance = storage.getSkill(skill);
         if (!instance.isEmpty()) {
            Object var6 = instance.get();
            if (var6 instanceof TensuraSkillInstance) {
               TensuraSkillInstance skillInstance = (TensuraSkillInstance)var6;
               if (!skill.canBeToggled(skillInstance, player)) {
                  stack.m_81352_(Component.m_237110_("tensura.skill.toggle.failed", new Object[]{skill.getName()}));
                  return 1;
               }

               if (skillInstance.canInteractSkill(player)) {
                  if (skillInstance.isToggled()) {
                     skillInstance.setToggled(false);
                     skillInstance.onToggleOff(player);
                     stack.m_81354_(Component.m_237110_("tensura.skill.toggle_off", new Object[]{skill.getName(), player.m_7755_()}), false);
                  } else {
                     skillInstance.setToggled(true);
                     skillInstance.onToggleOn(player);
                     stack.m_81354_(Component.m_237110_("tensura.skill.toggle_on", new Object[]{skill.getName(), player.m_7755_()}), false);
                  }

                  storage.syncChanges();
               }

               return 1;
            }
         }

         stack.m_81352_(Component.m_237110_("tensura.skill.do_not_have", new Object[]{player.m_7755_(), skill.getName()}));
         return 1;
      }
   }

   private static int toggleEverything(CommandSourceStack stack, Player player, boolean toggle, Predicate<ManasSkill> predicate) {
      int i = 0;
      Iterator var5 = SkillAPI.getSkillsFrom(player).getLearnedSkills().iterator();

      while(var5.hasNext()) {
         ManasSkillInstance instance = (ManasSkillInstance)var5.next();
         if (predicate.test(instance.getSkill()) && instance.getSkill().canBeToggled(instance, player) && instance.getSkill().canInteractSkill(instance, player) && instance.isToggled() != toggle) {
            instance.setToggled(toggle);
            if (toggle) {
               instance.onToggleOn(player);
            } else {
               instance.onToggleOff(player);
            }

            ++i;
         }
      }

      String message = toggle ? "tensura.skill.toggle_all.on" : "tensura.skill.toggle_all.off";
      stack.m_81354_(Component.m_237110_(message, new Object[]{i, player.m_7755_()}), false);
      return 1;
   }

   private static int setPreset(CommandSourceStack stack, Player player, int number) {
      TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
         int newPreset = number - 1;
         if (cap.getActivePreset() != newPreset) {
            cap.setActivePreset(newPreset);
            stack.m_81354_(Component.m_237110_("tensura.skill.preset.changed", new Object[]{cap.getPresetName(newPreset)}), false);
         } else {
            stack.m_81352_(Component.m_237110_("tensura.skill.preset.no_change", new Object[]{cap.getPresetName(newPreset)}));
         }

         TensuraSkillCapability.sync(player);
      });
      return 1;
   }

   private static int setPresetName(CommandSourceStack stack, Player player, String name, int number) {
      TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
         int preset = number - 1;
         cap.setPresetName(preset, name);
         stack.m_81354_(Component.m_237110_("tensura.skill.preset.changed", new Object[]{name}), false);
         TensuraSkillCapability.sync(player);
      });
      return 1;
   }
}
