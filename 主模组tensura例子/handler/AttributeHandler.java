package com.github.manasmods.tensura.handler;

import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraftforge.event.entity.EntityAttributeModificationEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class AttributeHandler {
   public static void onAttributeModification(EntityAttributeModificationEvent e) {
      e.getTypes().forEach((type) -> {
         e.add(type, (Attribute)TensuraAttributeRegistry.BARRIER.get());
         e.add(type, (Attribute)TensuraAttributeRegistry.SIZE.get());
         e.add(type, (Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get());
         e.add(type, (Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get());
         e.add(type, (Attribute)TensuraAttributeRegistry.MAX_AURA.get());
      });
   }
}
