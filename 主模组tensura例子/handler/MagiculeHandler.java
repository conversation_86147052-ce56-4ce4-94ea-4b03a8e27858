package com.github.manasmods.tensura.handler;

import com.github.manasmods.tensura.api.magicule.MagiculeEvent;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.pack.BiomeMagiculeModifier;
import com.github.manasmods.tensura.data.pack.LevelMagiculeModifier;
import com.github.manasmods.tensura.registry.magicule.TensuraBiomeMagiculeModifier;
import com.github.manasmods.tensura.registry.magicule.TensuraLevelMagiculeModifier;
import com.google.common.util.concurrent.AtomicDouble;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import net.minecraft.core.Registry;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.biome.Biome;
import net.minecraft.world.level.chunk.LevelChunk;
import net.minecraft.world.level.chunk.LevelChunkSection;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.registries.ForgeRegistries;

@EventBusSubscriber
public class MagiculeHandler {
   @SubscribeEvent
   static void onMagiculeInit(MagiculeEvent.Initialization e) {
      Level world = e.level;
      if (!world.m_5776_()) {
         applyLevelModifier(e, world);
         applyBiomeModifier(e, world);
      }
   }

   private static void applyBiomeModifier(MagiculeEvent.Initialization e, Level world) {
      LevelChunk chunk = e.chunk;
      Map<Biome, Integer> biomeProbes = new HashMap();
      AtomicInteger totalProbes = new AtomicInteger(0);
      LevelChunkSection[] var5 = chunk.m_7103_();
      int var6 = var5.length;

      for(int var7 = 0; var7 < var6; ++var7) {
         LevelChunkSection chunkSection = var5[var7];
         chunkSection.m_187996_().m_196879_((biomeHolder) -> {
            Biome biome = (Biome)biomeHolder.get();
            biomeProbes.put(biome, (Integer)biomeProbes.getOrDefault(biome, 0) + 1);
            totalProbes.incrementAndGet();
         });
      }

      int maxProbes = totalProbes.get();
      if (maxProbes != 0) {
         Registry<BiomeMagiculeModifier> modifierRegistry = world.m_5962_().m_175515_(TensuraBiomeMagiculeModifier.REGISTRY_KEY);
         Registry<Biome> biomeRegistry = world.m_5962_().m_175515_(ForgeRegistries.BIOMES.getRegistryKey());
         AtomicDouble magiculeResult = new AtomicDouble(0.0D);
         AtomicDouble regenerationRateResult = new AtomicDouble(0.0D);
         biomeProbes.forEach((biome, probes) -> {
            ResourceLocation biomeId = biomeRegistry.m_7981_(biome);
            if (biomeId != null) {
               BiomeMagiculeModifier modifier = (BiomeMagiculeModifier)modifierRegistry.m_7745_(biomeId);
               double partialSize = 1.0D / (double)maxProbes * (double)probes;
               if (modifier == null) {
                  magiculeResult.addAndGet((Double)TensuraConfig.INSTANCE.magiculeConfig.baseMagicule.get() * partialSize);
                  regenerationRateResult.addAndGet((Double)TensuraConfig.INSTANCE.magiculeConfig.baseMagiculeRegeneration.get() * partialSize);
               } else {
                  magiculeResult.addAndGet(modifier.getMaxMagicule(e.getNewMaxMagicule()) * partialSize);
                  regenerationRateResult.addAndGet(modifier.getRegenerationRate(e.getNewRegenerationRate()) * partialSize);
               }
            }
         });
         e.setNewMaxMagicule(magiculeResult.get());
         e.setNewRegenerationRate(regenerationRateResult.get());
      }
   }

   private static void applyLevelModifier(MagiculeEvent.Initialization e, Level world) {
      Registry<LevelMagiculeModifier> registry = world.m_5962_().m_175515_(TensuraLevelMagiculeModifier.REGISTRY_KEY);
      LevelMagiculeModifier modifier = (LevelMagiculeModifier)registry.m_7745_(world.m_46472_().m_135782_());
      if (modifier != null) {
         e.setNewMaxMagicule(modifier.getMaxMagicule(e.getNewMaxMagicule()));
         e.setNewRegenerationRate(modifier.getRegenerationRate(e.getNewRegenerationRate()));
      }
   }
}
