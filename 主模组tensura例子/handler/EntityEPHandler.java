package com.github.manasmods.tensura.handler;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.api.entity.subclass.IRanking;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.data.pack.EntityEPCount;
import com.github.manasmods.tensura.data.pack.TensuraData;
import com.github.manasmods.tensura.event.NamingEvent;
import com.github.manasmods.tensura.event.SpiritLevelUpdateEvent;
import com.github.manasmods.tensura.event.UpdateEPEvent;
import com.github.manasmods.tensura.menu.RaceSelectionMenu;
import com.github.manasmods.tensura.network.play2server.RequestNamingGUIPacket;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.magic.SpiritualMagics;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.util.TensuraAdvancementsHelper;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.stats.Stats;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.event.entity.EntityJoinLevelEvent;
import net.minecraftforge.event.entity.player.PlayerWakeUpEvent;
import net.minecraftforge.eventbus.api.EventPriority;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class EntityEPHandler {
   @SubscribeEvent(
      priority = EventPriority.LOWEST
   )
   public static void initialEP(EntityJoinLevelEvent e) {
      if (!e.getLevel().m_5776_()) {
         Entity entity = e.getEntity();
         if (entity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)entity;
            if (!(TensuraEPCapability.getEP(living) > 0.0D)) {
               if (!(living instanceof Player)) {
                  RaceHelper.updateSpiritualHP(living);
                  TensuraEPCapability.getFrom(living).ifPresent((cap) -> {
                     if (living.m_6095_().m_204039_(TensuraTags.EntityTypes.MONSTER)) {
                        cap.setMajin(true);
                     }

                     Iterator var2 = TensuraData.getEntityEP().iterator();

                     while(var2.hasNext()) {
                        EntityEPCount entityEP = (EntityEPCount)var2.next();
                        if (entityEP.getEntity().equals(EntityType.m_20613_(living.m_6095_()))) {
                           int minEP = entityEP.getMinEP();
                           int maxEP = entityEP.getMaxEP() <= minEP ? minEP + 1 : entityEP.getMaxEP() + 1;
                           double energyx = (double)living.m_217043_().m_216339_(0, maxEP - minEP);
                           AttributeInstance magiculex = living.m_21051_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get());
                           if (magiculex != null) {
                              magiculex.m_22100_((double)((float)minEP / 2.0F));
                              energyx += magiculex.m_22135_();
                           }

                           AttributeInstance aurax = living.m_21051_((Attribute)TensuraAttributeRegistry.MAX_AURA.get());
                           if (aurax != null) {
                              aurax.m_22100_((double)((float)minEP / 2.0F));
                              energyx += aurax.m_22135_();
                           }

                           cap.setEP(living, energyx);
                           Iterator var10 = entityEP.getSkills().iterator();

                           while(var10.hasNext()) {
                              ResourceLocation skillID = (ResourceLocation)var10.next();
                              ManasSkill skill = (ManasSkill)SkillAPI.getSkillRegistry().getValue(skillID);
                              if (skill != null) {
                                 TensuraSkillInstance instance = new TensuraSkillInstance(skill);
                                 SkillAPI.getSkillsFrom(living).learnSkill(instance);
                                 if (instance.canBeToggled(living)) {
                                    instance.setToggled(true);
                                 }
                              }
                           }

                           RaceSelectionMenu.grantLearningResistance(living);
                           break;
                        }
                     }

                     if (cap.getEP() <= 0.0D) {
                        double energy = (double)(100.0F * living.m_21233_());
                        AttributeInstance magicule = living.m_21051_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get());
                        if (magicule != null) {
                           energy += magicule.m_22135_();
                        }

                        AttributeInstance aura = living.m_21051_((Attribute)TensuraAttributeRegistry.MAX_AURA.get());
                        if (aura != null) {
                           energy += aura.m_22135_();
                        }

                        cap.setEP(living, energy);
                     }

                  });
               }
            }
         }
      }
   }

   @SubscribeEvent
   public static void onUpdateEP(UpdateEPEvent e) {
      double oldEP = e.getOldEP();
      double newEP = e.getNewEP();
      if (!(oldEP >= newEP)) {
         LivingEntity entity = e.getEntity();
         Level level = entity.m_9236_();
         if (!level.m_5776_()) {
            int humanKill = (Integer)TensuraConfig.INSTANCE.awakeningConfig.massNamingHuman.get();
            if (!(entity instanceof ServerPlayer)) {
               if (oldEP != 0.0D) {
                  addStatOnEPGain(entity, newEP - oldEP);
               }

               if (TensuraEPCapability.getHumanKill(entity) >= humanKill && TensuraEPCapability.getName(entity) == null) {
                  RequestNamingGUIPacket.name(entity, (ServerPlayer)null, RequestNamingGUIPacket.NamingType.LOW, String.valueOf(entity.m_7755_()));
               }

            } else {
               ServerPlayer player = (ServerPlayer)entity;
               if (TensuraEPCapability.isNameable(player) && (player.m_8951_().m_13015_(Stats.f_12988_.m_12902_(Stats.f_12950_)) >= (Integer)TensuraConfig.INSTANCE.awakeningConfig.massNamingRaid.get() || TensuraEPCapability.getHumanKill(player) >= humanKill)) {
                  TensuraAdvancementsHelper.grant(player, TensuraAdvancementsHelper.Advancements.INFAMY_FAMOUS);
                  if (TensuraEPCapability.getName(player) == null) {
                     RequestNamingGUIPacket.name(player, (ServerPlayer)null, RequestNamingGUIPacket.NamingType.LOW, player.m_7755_().getString());
                  }
               }

               if (newEP >= 1000.0D) {
                  TensuraAdvancementsHelper.grant(player, TensuraAdvancementsHelper.Advancements.D_RANK);
               }

               if (newEP >= 3000.0D) {
                  TensuraAdvancementsHelper.grant(player, TensuraAdvancementsHelper.Advancements.C_RANK);
               }

               if (newEP >= 6000.0D) {
                  TensuraAdvancementsHelper.grant(player, TensuraAdvancementsHelper.Advancements.B_RANK);
               }

               if (newEP >= 10000.0D) {
                  TensuraAdvancementsHelper.grant(player, TensuraAdvancementsHelper.Advancements.A_RANK);
               }

               if (newEP >= 100000.0D) {
                  TensuraAdvancementsHelper.grant(player, TensuraAdvancementsHelper.Advancements.SA_RANK);
               }

               if (newEP >= 400000.0D) {
                  TensuraAdvancementsHelper.grant(player, TensuraAdvancementsHelper.Advancements.S_RANK);
               }

               if (newEP >= 800000.0D) {
                  TensuraAdvancementsHelper.grant(player, TensuraAdvancementsHelper.Advancements.SS_RANK);
               }

               if (!(newEP > TensuraPlayerCapability.getBaseEP(player))) {
                  TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                     if (TensuraEPCapability.isMajin(player) && !cap.isTrueHero() && !cap.isDemonLordSeed() && !cap.isTrueDemonLord() && newEP >= (double)level.m_46469_().m_46215_(TensuraGameRules.DEMON_LORD_SEED)) {
                        cap.setDemonLordSeed(true);
                        player.m_5661_(Component.m_237115_("tensura.evolve.demon_lord.seed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                        level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12496_, SoundSource.PLAYERS, 1.0F, 1.0F);
                     }

                  });
                  Iterator var9 = SkillAPI.getSkillRegistry().getValues().iterator();

                  while(var9.hasNext()) {
                     ManasSkill manasSkill = (ManasSkill)var9.next();
                     if (manasSkill instanceof TensuraSkill) {
                        TensuraSkill skill = (TensuraSkill)manasSkill;
                        SkillStorage storage = SkillAPI.getSkillsFrom(player);
                        if (!storage.getSkill(skill).isPresent() && skill.meetEPRequirement(player, newEP)) {
                           ManasSkillInstance instance = new TensuraSkillInstance(skill);
                           instance.setMastery(skill.getMasteryOnEPAcquirement(player));
                           if (storage.learnSkill(instance)) {
                              player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
                           }
                        }
                     }
                  }

               }
            }
         }
      }
   }

   @SubscribeEvent
   public static void mpRestoreOnWake(PlayerWakeUpEvent e) {
      Player player = e.getEntity();
      if (!player.m_9236_().m_5776_()) {
         if (!e.wakeImmediately() && !e.updateLevel()) {
            if (!player.m_9236_().m_46470_()) {
               if (player.m_21023_((MobEffect)TensuraMobEffects.RAMPAGE.get())) {
                  player.m_21195_((MobEffect)TensuraMobEffects.RAMPAGE.get());
               }

               TensuraEPCapability.setSpiritualHealth(player, player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get()));
               TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                  double maxMP = player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get());
                  if (cap.getMagicule() != maxMP) {
                     double newMagicule = cap.getMagicule() + (double)Math.round(maxMP / 6.0D);
                     cap.setMagicule(Math.min(newMagicule, maxMP));
                  }

               });
            }
         }
      }
   }

   private static void addStatOnEPGain(LivingEntity entity, double difference) {
      TensuraEPCapability.getFrom(entity).ifPresent((cap) -> {
         cap.setGainedEP(cap.getGainedEP() + difference);
         double gainedEP = cap.getGainedEP();
         double points = (double)((int)gainedEP / 10000);
         if (points > 0.0D) {
            cap.setGainedEP(gainedEP - points * 10000.0D);
            AttributeInstance armor = entity.m_21051_(Attributes.f_22284_);
            if (armor != null) {
               armor.m_22100_(armor.m_22115_() + points);
            }

            AttributeInstance attack = entity.m_21051_(Attributes.f_22281_);
            if (attack != null) {
               attack.m_22100_(attack.m_22115_() + points);
            }

            AttributeInstance HP = entity.m_21051_(Attributes.f_22276_);
            if (HP != null) {
               HP.m_22100_(HP.m_22115_() + points * 10.0D);
            }

            AttributeInstance SHP = entity.m_21051_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get());
            if (SHP != null) {
               SHP.m_22100_(SHP.m_22115_() + points * 20.0D);
            }

            entity.m_5634_((float)points * 10.0F);
         }

         TensuraEPCapability.sync(entity);
      });
   }

   @SubscribeEvent
   public static void onUpdateSpiritLevel(SpiritLevelUpdateEvent e) {
      Player player = e.getEntity();
      MagicElemental elemental = e.getElemental();
      SpiritualMagic.SpiritLevel level = e.getSpiritLevel();
      if (MagicElemental.getCommonElementals().contains(elemental)) {
         if (level.getId() >= 2) {
            SkillAPI.getSkillsFrom(player).learnSkill(new TensuraSkillInstance((ManasSkill)SpiritualMagics.SUMMON_MEDIUM_ELEMENTAL.get()));
         }

         if (level.getId() >= 3) {
            SkillAPI.getSkillsFrom(player).learnSkill(new TensuraSkillInstance((ManasSkill)SpiritualMagics.SUMMON_GREATER_ELEMENTAL.get()));
         }
      }

      int greaterLevel = (Integer)TensuraConfig.INSTANCE.awakeningConfig.heroSpiritLevel.get();
      TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
         int greater = 0;
         MagicElemental[] var6 = MagicElemental.values();
         int var7 = var6.length;

         for(int var8 = 0; var8 < var7; ++var8) {
            MagicElemental magicElemental = var6[var8];
            if (cap.getSpiritLevel(magicElemental.getId()) >= greaterLevel) {
               ++greater;
            } else if (magicElemental.getId() == elemental.getId() && level.getId() >= greaterLevel) {
               ++greater;
            }
         }

         if (greater >= 7 && player instanceof ServerPlayer) {
            ServerPlayer serverPlayer = (ServerPlayer)player;
            TensuraAdvancementsHelper.grant(serverPlayer, TensuraAdvancementsHelper.Advancements.BLESSED_ONE);
         }

         if (greater >= (Integer)TensuraConfig.INSTANCE.awakeningConfig.heroSpiritNumber.get()) {
            TensuraPlayerCapability.getFrom(player).ifPresent((playerCap) -> {
               Race race = playerCap.getRace();
               if (race != null) {
                  if (!race.isMajin() || TensuraEPCapability.isChaos(player)) {
                     if (!playerCap.isHeroEgg()) {
                        if (!playerCap.isTrueHero()) {
                           if (!playerCap.isTrueDemonLord()) {
                              playerCap.setHeroEgg(true);
                              player.m_5661_(Component.m_237115_("tensura.evolve.hero.egg").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                              player.m_9236_().m_6263_((Player)null, player.m_20185_(), player.m_20186_(), player.m_20189_(), SoundEvents.f_12496_, SoundSource.PLAYERS, 1.0F, 1.0F);
                              ManasSkill skill = (ManasSkill)IntrinsicSkills.EYE_OF_TRUTH.get();
                              if (SkillUtils.learnSkill(player, (ManasSkill)skill)) {
                                 playerCap.addIntrinsicSkill(skill);
                                 player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                              }

                              TensuraPlayerCapability.sync(player);
                           }
                        }
                     }
                  }
               }
            });
         }
      });
   }

   @SubscribeEvent
   public static void onNaming(NamingEvent e) {
      LivingEntity entity = e.getEntity();
      if (entity instanceof IRanking) {
         IRanking ranking = (IRanking)entity;
         ranking.onNamed(e);
      }

   }
}
