package com.github.manasmods.tensura.handler;

import com.github.manasmods.tensura.block.SmithingBenchBlock;
import com.github.manasmods.tensura.client.screen.widget.MenuTab;
import com.github.manasmods.tensura.network.play2server.RequestMenuSwitchPacket;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.client.gui.screens.inventory.AnvilScreen;
import net.minecraft.client.gui.screens.inventory.SmithingScreen;
import net.minecraft.network.chat.Component;
import net.minecraft.world.level.block.Blocks;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.ScreenEvent.Init.Post;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE,
   value = {Dist.CLIENT}
)
class SmithingBenchTabHandler {
   @SubscribeEvent
   public static void onVanillaSmithingMenu(Post e) {
      Screen var2 = e.getScreen();
      if (var2 instanceof SmithingScreen) {
         SmithingScreen screen = (SmithingScreen)var2;
         if (screen.m_96636_().equals(SmithingBenchBlock.TITLE)) {
            e.addListener(new MenuTab(screen.getGuiLeft() + 5, screen.getGuiTop() - 28, RequestMenuSwitchPacket.SwitchType.VANILLA_SMITHING_TO_SMITHING, ((SmithingBenchBlock)TensuraBlocks.SMITHING_BENCH.get()).m_5456_(), false, (pButton, pPoseStack, pMouseX, pMouseY) -> {
               screen.m_96602_(pPoseStack, Component.m_237115_("tensura.smithing_bench.menu.open"), pMouseX, pMouseY);
            }));
            e.addListener(new MenuTab(screen.getGuiLeft() + 33, screen.getGuiTop() - 28, (RequestMenuSwitchPacket.SwitchType)null, Blocks.f_50625_.m_5456_(), true, (pButton, pPoseStack, pMouseX, pMouseY) -> {
               screen.m_96602_(pPoseStack, Component.m_237115_("tensura.smithing_table.menu.open"), pMouseX, pMouseY);
            }));
            e.addListener(new MenuTab(screen.getGuiLeft() + 61, screen.getGuiTop() - 28, RequestMenuSwitchPacket.SwitchType.VANILLA_SMITHING_TO_ANVIL, Blocks.f_50322_.m_5456_(), false, (pButton, pPoseStack, pMouseX, pMouseY) -> {
               screen.m_96602_(pPoseStack, Component.m_237115_("tensura.anvil.menu.open"), pMouseX, pMouseY);
            }));
         }
      }
   }

   @SubscribeEvent
   public static void onVanillaAnvilMenu(Post e) {
      Screen var2 = e.getScreen();
      if (var2 instanceof AnvilScreen) {
         AnvilScreen screen = (AnvilScreen)var2;
         if (screen.m_96636_().equals(SmithingBenchBlock.TITLE)) {
            e.addListener(new MenuTab(screen.getGuiLeft() + 5, screen.getGuiTop() - 28, RequestMenuSwitchPacket.SwitchType.ANVIL_TO_SMITHING, ((SmithingBenchBlock)TensuraBlocks.SMITHING_BENCH.get()).m_5456_(), false, (pButton, pPoseStack, pMouseX, pMouseY) -> {
               screen.m_96602_(pPoseStack, Component.m_237115_("tensura.smithing_bench.menu.open"), pMouseX, pMouseY);
            }));
            e.addListener(new MenuTab(screen.getGuiLeft() + 33, screen.getGuiTop() - 28, RequestMenuSwitchPacket.SwitchType.ANVIL_TO_VANILLA_SMITHING, Blocks.f_50625_.m_5456_(), false, (pButton, pPoseStack, pMouseX, pMouseY) -> {
               screen.m_96602_(pPoseStack, Component.m_237115_("tensura.smithing_table.menu.open"), pMouseX, pMouseY);
            }));
            e.addListener(new MenuTab(screen.getGuiLeft() + 61, screen.getGuiTop() - 28, (RequestMenuSwitchPacket.SwitchType)null, Blocks.f_50322_.m_5456_(), true, (pButton, pPoseStack, pMouseX, pMouseY) -> {
               screen.m_96602_(pPoseStack, Component.m_237115_("tensura.anvil.menu.open"), pMouseX, pMouseY);
            }));
         }
      }
   }
}
