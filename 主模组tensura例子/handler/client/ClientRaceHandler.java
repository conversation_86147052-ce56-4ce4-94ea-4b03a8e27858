package com.github.manasmods.tensura.handler.client;

import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.core.client.AccessorEntityRenderer;
import com.github.manasmods.tensura.handler.RaceHandler;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2server.SlimeJumpChargePacket;
import com.github.manasmods.tensura.network.play2server.SlimeJumpReleasedPacket;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.race.slime.SlimeRace;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.MovementInputUpdateEvent;
import net.minecraftforge.client.event.RenderNameTagEvent;
import net.minecraftforge.client.event.RenderPlayerEvent.Post;
import net.minecraftforge.client.event.RenderPlayerEvent.Pre;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE,
   value = {Dist.CLIENT}
)
public class ClientRaceHandler {
   public static long jumpChargingTicks = 0L;

   @SubscribeEvent
   public static void onRenderPlayerPre(Pre e) {
      try {
         Player player = e.getEntity();
         float scale = RaceHelper.getSizeMultiplier(player);
         if (scale == 1.0F) {
            return;
         }

         e.getPoseStack().m_85836_();
         e.getPoseStack().m_85837_(0.0D, player.m_6047_() ? 0.125D - 0.125D * (double)scale : 0.0D, 0.0D);
         e.getPoseStack().m_85841_(scale, scale, scale);
         AccessorEntityRenderer renderer = (AccessorEntityRenderer)e.getRenderer();
         renderer.setShadowRadius(renderer.getShadowRadius() * 1.0F);
      } catch (Exception var4) {
         var4.printStackTrace();
      }

   }

   @SubscribeEvent
   public static void onRenderPlayerPost(Post e) {
      try {
         if (RaceHelper.getSizeMultiplier(e.getEntity()) != 1.0F) {
            e.getPoseStack().m_85849_();
         }
      } catch (Exception var2) {
         var2.printStackTrace();
      }

   }

   @SubscribeEvent
   public static void onRenderPlayerName(RenderNameTagEvent e) {
      Entity var2 = e.getEntity();
      if (var2 instanceof Player) {
         Player entity = (Player)var2;

         try {
            float scale = RaceHelper.getSizeMultiplier(entity);
            e.getPoseStack().m_85841_(1.0F / scale, 1.0F / scale, 1.0F / scale);
            if (e.getEntity().m_6047_() && scale < 0.2F) {
               e.getPoseStack().m_85837_(0.0D, -1.0D * (double)scale, 0.0D);
            }
         } catch (Exception var3) {
            var3.printStackTrace();
         }

      }
   }

   @SubscribeEvent
   public static void checkForSlimeJumpCharge(MovementInputUpdateEvent e) {
      Player player = e.getEntity();
      if (!player.m_5833_()) {
         if (!RaceHandler.isInFluid(player) && player.m_6047_() && (player.m_20096_() || !(player.f_19789_ > 1.0F))) {
            if (player.m_20096_()) {
               TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                  if (cap.getRace() != null) {
                     if (cap.getRace() instanceof SlimeRace) {
                        if (e.getInput().f_108572_) {
                           e.getInput().f_108572_ = false;
                           if (jumpChargingTicks == 0L && player.m_6047_()) {
                              TensuraNetwork.toServer(new SlimeJumpChargePacket());
                           }

                           if (jumpChargingTicks < 100L && player.m_6047_()) {
                              ++jumpChargingTicks;
                           } else if (!player.m_6047_()) {
                              SlimeJumpChargePacket.chargingPlayers.remove(player.m_20148_());
                              jumpChargingTicks = 0L;
                              e.getInput().f_108572_ = false;
                              player.m_6862_(false);
                              player.f_20899_ = false;
                              return;
                           }
                        } else if (jumpChargingTicks > 0L) {
                           TensuraNetwork.toServer(new SlimeJumpReleasedPacket());
                           jumpChargingTicks = 0L;
                        }
                     }

                  }
               });
            }
         } else {
            SlimeJumpChargePacket.chargingPlayers.remove(player.m_20148_());
            jumpChargingTicks = 0L;
         }
      }
   }
}
