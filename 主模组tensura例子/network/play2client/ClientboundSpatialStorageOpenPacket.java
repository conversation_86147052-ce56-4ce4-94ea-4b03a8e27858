package com.github.manasmods.tensura.network.play2client;

import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent.Context;

public class ClientboundSpatialStorageOpenPacket {
   protected final int userId;
   protected final int containerId;
   protected final int size;
   protected final int stackSize;
   protected final ResourceLocation skill;
   protected final byte containerType;

   public ClientboundSpatialStorageOpenPacket(int userId, int containerId, int size, int stackSize, ResourceLocation skill) {
      this.userId = userId;
      this.containerId = containerId;
      this.size = size;
      this.stackSize = stackSize;
      this.skill = skill;
      this.containerType = 0;
   }

   public ClientboundSpatialStorageOpenPacket(int userId, int containerId, int size, int stackSize, ResourceLocation skill, byte type) {
      this.userId = userId;
      this.containerId = containerId;
      this.size = size;
      this.stackSize = stackSize;
      this.skill = skill;
      this.containerType = type;
   }

   public ClientboundSpatialStorageOpenPacket(FriendlyByteBuf buffer) {
      this.userId = buffer.readInt();
      this.containerId = buffer.readByte();
      this.size = buffer.m_130242_();
      this.stackSize = buffer.readInt();
      this.skill = buffer.m_130281_();
      this.containerType = buffer.readByte();
   }

   public void toBytes(FriendlyByteBuf buffer) {
      buffer.writeInt(this.userId);
      buffer.writeByte(this.containerId);
      buffer.m_130130_(this.size);
      buffer.writeInt(this.stackSize);
      buffer.m_130085_(this.skill);
      buffer.writeByte(this.containerType);
   }

   public static void handle(ClientboundSpatialStorageOpenPacket message, Supplier<Context> context) {
      ((Context)context.get()).enqueueWork(() -> {
         DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> {
            return () -> {
               switch(message.containerType) {
               case 1:
                  ClientAccess.handleClientboundSpatialCraftingOpenPacket(message, context);
                  break;
               case 2:
                  ClientAccess.handleClientboundSpatialRefiningOpenPacket(message, context);
                  break;
               case 3:
                  ClientAccess.handleClientboundResearcherStorageOpenPacket(message, context);
                  break;
               default:
                  ClientAccess.handleClientboundSpatialStorageOpenPacket(message, context);
               }

            };
         });
      });
      ((Context)context.get()).setPacketHandled(true);
   }
}
