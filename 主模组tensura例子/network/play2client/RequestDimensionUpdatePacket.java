package com.github.manasmods.tensura.network.play2client;

import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestDimensionUpdatePacket {
   private final int id;

   public RequestDimensionUpdatePacket(FriendlyByteBuf buf) {
      this.id = buf.readInt();
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.writeInt(this.id);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> {
            return () -> {
               ClientAccess.updateDimension(this.id);
            };
         });
      });
      ((Context)ctx.get()).setPacketHandled(true);
   }

   public RequestDimensionUpdatePacket(int id) {
      this.id = id;
   }
}
