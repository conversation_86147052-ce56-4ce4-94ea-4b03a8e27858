package com.github.manasmods.tensura.network.play2client;

import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestClientSkillRemovePacket {
   private final ResourceLocation stack;
   private final int id;

   public RequestClientSkillRemovePacket(FriendlyByteBuf buf) {
      this.stack = buf.m_130281_();
      this.id = buf.readInt();
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.m_130085_(this.stack);
      buf.writeInt(this.id);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> {
            return () -> {
               ClientAccess.removeClientSkill(this.stack, this.id);
            };
         });
      });
      ((Context)ctx.get()).setPacketHandled(true);
   }

   public RequestClientSkillRemovePacket(ResourceLocation stack, int id) {
      this.stack = stack;
      this.id = id;
   }
}
