package com.github.manasmods.tensura.network.play2server.skill;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import java.util.Iterator;
import java.util.function.Supplier;
import net.minecraft.ChatFormatting;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestChangeModeButtonPacket {
   public final double presetChange;
   public final int slot;
   public boolean alternative;

   public RequestChangeModeButtonPacket(FriendlyByteBuf buf) {
      this.presetChange = buf.readDouble();
      this.slot = buf.readInt();
      this.alternative = buf.readBoolean();
   }

   public RequestChangeModeButtonPacket(double presetChange, boolean scroll) {
      this.presetChange = presetChange;
      this.slot = -1;
      this.alternative = scroll;
   }

   public RequestChangeModeButtonPacket(int slot, boolean reverse) {
      this.presetChange = 0.0D;
      this.slot = slot;
      this.alternative = reverse;
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.writeDouble(this.presetChange);
      buf.writeInt(this.slot);
      buf.writeBoolean(this.alternative);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ServerPlayer player = ((Context)ctx.get()).getSender();
         if (player != null) {
            if (this.slot == -1) {
               this.changePreset(player);
            } else {
               this.changeMode(player);
            }
         }

      });
      ((Context)ctx.get()).setPacketHandled(true);
   }

   public void changeMode(Player player) {
      TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
         SkillStorage storage = SkillAPI.getSkillsFrom(player);
         Iterator var4 = storage.getLearnedSkills().iterator();

         while(var4.hasNext()) {
            ManasSkillInstance instance = (ManasSkillInstance)var4.next();
            if (cap.isSkillInSpecificSlot(instance.getSkill(), this.slot) && instance instanceof TensuraSkillInstance) {
               TensuraSkillInstance skillInstance = (TensuraSkillInstance)instance;
               if (skillInstance.canInteractSkill(player)) {
                  TensuraSkill skill = (TensuraSkill)skillInstance.getSkill();
                  if (skill.modes() > 1) {
                     int nextMode = skill.nextMode(player, skillInstance, this.alternative);
                     if (nextMode == 0) {
                        player.m_5661_(Component.m_237110_("tensura.skill.mode.cannot_change", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
                     } else {
                        skillInstance.setMode(nextMode);
                        player.m_5661_(Component.m_237110_("tensura.skill.mode.changed", new Object[]{skill.getName(), skill.getModeName(nextMode)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
                        storage.syncChanges();
                     }
                  } else {
                     player.m_5661_(Component.m_237110_("tensura.skill.mode.no_mode", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
                  }
               }
            }
         }

      });
   }

   public void changePreset(Player player) {
      if (this.alternative) {
         TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
            int activePreset = cap.getActivePreset();
            int newPreset = activePreset + (int)this.presetChange;
            if (newPreset > 8) {
               newPreset = 0;
            }

            if (newPreset < 0) {
               newPreset = 8;
            }

            if (activePreset != newPreset) {
               cap.setActivePreset(newPreset);
               TensuraSkillCapability.sync(player);
            }

            player.m_5661_(Component.m_237110_("tensura.skill.preset.changed", new Object[]{cap.getPresetName(newPreset)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
         });
      } else if (this.presetChange < 10.0D && this.presetChange > 0.0D) {
         int key = (int)this.presetChange - 1;
         TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
            if (cap.getActivePreset() != key) {
               cap.setActivePreset(key);
               TensuraSkillCapability.sync(player);
               player.m_5661_(Component.m_237110_("tensura.skill.preset.changed", new Object[]{cap.getPresetName(key)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
            } else {
               player.m_5661_(Component.m_237110_("tensura.skill.preset.no_change", new Object[]{cap.getPresetName(key)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
            }

         });
      }

   }
}
