package com.github.manasmods.tensura.network.play2server.skill;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestSkillNumberKeyPressPacket {
   private final int keyNumber;
   private final List<ResourceLocation> skillList;

   public RequestSkillNumberKeyPressPacket(FriendlyByteBuf buf) {
      this.skillList = buf.m_236845_(FriendlyByteBuf::m_130281_);
      this.keyNumber = buf.readInt();
   }

   public RequestSkillNumberKeyPressPacket(List<ResourceLocation> skills, int keyNumber) {
      this.skillList = skills;
      this.keyNumber = keyNumber;
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.m_236828_(this.skillList, FriendlyByteBuf::m_130085_);
      buf.writeInt(this.keyNumber);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ServerPlayer player = ((Context)ctx.get()).getSender();
         if (player != null) {
            SkillStorage storage = SkillAPI.getSkillsFrom(player);
            Iterator var4 = this.skillList.iterator();

            while(var4.hasNext()) {
               ResourceLocation id = (ResourceLocation)var4.next();
               ManasSkill manasSkill = (ManasSkill)SkillAPI.getSkillRegistry().getValue(id);
               if (manasSkill != null) {
                  Optional<ManasSkillInstance> optional = storage.getSkill(manasSkill);
                  if (!optional.isEmpty()) {
                     ManasSkillInstance skillInstance = (ManasSkillInstance)optional.get();
                     if (skillInstance instanceof TensuraSkillInstance) {
                        TensuraSkillInstance instance = (TensuraSkillInstance)skillInstance;
                        if (!skillInstance.canInteractSkill(player)) {
                           return;
                        }

                        if (skillInstance.onCoolDown()) {
                           return;
                        }

                        instance.onNumberKeyPress(player, this.keyNumber);
                     }
                  }
               }
            }

            storage.syncChanges();
         }

      });
      ((Context)ctx.get()).setPacketHandled(true);
   }
}
