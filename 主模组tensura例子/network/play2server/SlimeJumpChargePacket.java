package com.github.manasmods.tensura.network.play2server;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.network.NetworkEvent.Context;

public class SlimeJumpChargePacket {
   public static final Map<UUID, Long> chargingPlayers = new ConcurrentHashMap();

   public SlimeJumpChargePacket() {
   }

   public SlimeJumpChargePacket(FriendlyByteBuf buf) {
   }

   public void toBytes(FriendlyByteBuf buf) {
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         chargingPlayers.put(((Context)ctx.get()).getSender().m_20148_(), System.currentTimeMillis());
      });
      ((Context)ctx.get()).setPacketHandled(true);
   }
}
