package com.github.manasmods.tensura.network.play2server;

import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import java.util.function.Consumer;
import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.NetworkEvent.Context;

public class InsanityActionPacket {
   private final InsanityActionPacket.Action action;

   public InsanityActionPacket(FriendlyByteBuf buf) {
      this.action = (InsanityActionPacket.Action)buf.m_130066_(InsanityActionPacket.Action.class);
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.m_130068_(this.action);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         this.action.contextConsumer.accept((Context)ctx.get());
      });
      ((Context)ctx.get()).setPacketHandled(true);
   }

   public InsanityActionPacket(InsanityActionPacket.Action action) {
      this.action = action;
   }

   public static enum Action {
      RESET_VALUE((context) -> {
         ServerPlayer player = context.getSender();
         if (player != null) {
            TensuraEffectsCapability.getFrom(player).ifPresent((cap) -> {
               cap.setInsanityFOV(0);
               cap.setInsanityNightmare(0);
               TensuraEffectsCapability.sync(player);
            });
         }
      });

      private final Consumer<Context> contextConsumer;

      private Action(Consumer<Context> contextConsumer) {
         this.contextConsumer = contextConsumer;
      }

      // $FF: synthetic method
      private static InsanityActionPacket.Action[] $values() {
         return new InsanityActionPacket.Action[]{RESET_VALUE};
      }
   }
}
