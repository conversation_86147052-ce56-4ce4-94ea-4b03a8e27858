package com.github.manasmods.tensura.network.play2server;

import com.github.manasmods.tensura.menu.DegenerateEnchantmentMenu;
import java.util.Objects;
import java.util.function.Supplier;
import net.minecraft.core.Registry;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestDisenchantPacket {
   private final ResourceLocation enchantment;
   private final int level;

   public RequestDisenchantPacket(FriendlyByteBuf buf) {
      this.level = buf.readInt();
      this.enchantment = buf.m_130281_();
   }

   public RequestDisenchantPacket(Enchantment enchantment, int level) {
      this.enchantment = Registry.f_122825_.m_7981_(enchantment);
      this.level = level;
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.writeInt(this.level);
      buf.m_130085_(this.enchantment);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ServerPlayer player = ((Context)ctx.get()).getSender();
         if (player != null) {
            AbstractContainerMenu patt1302$temp = player.f_36096_;
            if (patt1302$temp instanceof DegenerateEnchantmentMenu) {
               DegenerateEnchantmentMenu menu = (DegenerateEnchantmentMenu)patt1302$temp;
               menu.removeEnchantment((Enchantment)Objects.requireNonNull((Enchantment)Registry.f_122825_.m_7745_(this.enchantment)), this.level);
            }
         }

      });
      ((Context)ctx.get()).setPacketHandled(true);
   }
}
