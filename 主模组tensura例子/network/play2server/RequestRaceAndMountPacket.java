package com.github.manasmods.tensura.network.play2server;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.api.entity.subclass.ITensuraMount;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.race.Race;
import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestRaceAndMountPacket {
   public final double alter;

   public RequestRaceAndMountPacket(FriendlyByteBuf buf) {
      this.alter = buf.readDouble();
   }

   public RequestRaceAndMountPacket() {
      this.alter = 0.0D;
   }

   public RequestRaceAndMountPacket(double scrollChange) {
      this.alter = scrollChange;
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.writeDouble(this.alter);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ServerPlayer player = ((Context)ctx.get()).getSender();
         if (player != null) {
            Entity patt1247$temp = player.m_20202_();
            if (patt1247$temp instanceof ITensuraMount) {
               ITensuraMount mount = (ITensuraMount)patt1247$temp;
               if (mount instanceof LivingEntity) {
                  LivingEntity entity = (LivingEntity)mount;
                  if (SkillHelper.getSubordinateOwner(entity) == player) {
                     if (this.alter != 0.0D) {
                        mount.mountScrollAbility(player, this.alter);
                     } else {
                        mount.mountAbility(player);
                     }

                     return;
                  }
               }
            }

            Race race = TensuraPlayerCapability.getRace(player);
            if (race != null && race.canActivateAbility(player)) {
               race.raceAbility(player);
            }
         }

      });
      ((Context)ctx.get()).setPacketHandled(true);
   }
}
