package com.github.manasmods.tensura.network.play2server;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.event.NamingEvent;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.util.TensuraAdvancementsHelper;
import java.util.Iterator;
import java.util.function.Supplier;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.animal.horse.AbstractHorse;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.ForgeEventFactory;
import net.minecraftforge.network.NetworkEvent.Context;
import org.jetbrains.annotations.Nullable;

public class RequestNamingGUIPacket {
   private final int targetID;
   private final String name;
   private final RequestNamingGUIPacket.NamingType type;

   public RequestNamingGUIPacket(FriendlyByteBuf buf) {
      this.targetID = buf.readInt();
      this.name = buf.m_130277_();
      this.type = (RequestNamingGUIPacket.NamingType)buf.m_130066_(RequestNamingGUIPacket.NamingType.class);
   }

   public RequestNamingGUIPacket(int id, String name, RequestNamingGUIPacket.NamingType type) {
      this.targetID = id;
      this.name = name;
      this.type = type;
   }

   public void toBytes(FriendlyByteBuf buf) {
      buf.writeInt(this.targetID);
      buf.m_130070_(this.name);
      buf.m_130068_(this.type);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ServerPlayer owner = ((Context)ctx.get()).getSender();
         if (owner != null) {
            Entity entity = owner.f_19853_.m_6815_(this.targetID);
            if (entity instanceof LivingEntity) {
               LivingEntity sub = (LivingEntity)entity;
               name(sub, owner, this.type, this.name);
            }
         }

      });
      ((Context)ctx.get()).setPacketHandled(true);
   }

   public static void name(LivingEntity sub, @Nullable ServerPlayer owner, RequestNamingGUIPacket.NamingType type, String name) {
      if (owner == null || RequestNameKeyPacket.canName(owner, sub)) {
         double var10000;
         if (sub instanceof Player) {
            Player player = (Player)sub;
            var10000 = TensuraPlayerCapability.getBaseEP(player);
         } else {
            var10000 = TensuraEPCapability.getEP(sub);
         }

         double subEP = var10000;
         TensuraEPCapability.getFrom(sub).ifPresent((namingCap) -> {
            double var10000;
            switch(type) {
            case LOW:
               var10000 = subEP / 2.0D;
               break;
            case MEDIUM:
               var10000 = subEP * 1.5D;
               break;
            case HIGH:
               var10000 = subEP * 9.0D;
               break;
            default:
               throw new IncompatibleClassChangeError();
            }

            double originalCost = var10000;
            NamingEvent event = new NamingEvent(sub, owner, originalCost, type, name);
            if (!MinecraftForge.EVENT_BUS.post(event)) {
               originalCost = event.getOriginalCost();
               double cost = Math.min(event.getCalculatedCost(), (Double)TensuraConfig.INSTANCE.namingConfig.maxCost.get());
               if (owner != null && TensuraPlayerCapability.getMagicule(owner) < cost) {
                  owner.m_5661_(Component.m_237115_("tensura.skill.lack_magicule").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
               } else {
                  namingCap.setName(event.getName());
                  sub.m_6593_(Component.m_237113_(event.getName()));
                  if (owner != null) {
                     namingCap.setPermanentOwner(owner.m_20148_());
                     TensuraAdvancementsHelper.grant(owner, TensuraAdvancementsHelper.Advancements.NAME_A_MOB);
                  }

                  sub.f_19853_.m_6263_((Player)null, sub.m_20185_(), sub.m_20186_(), sub.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  sub.m_7292_(new MobEffectInstance(MobEffects.f_19619_, 40, 0, false, false, false));
                  TensuraParticleHelper.addServerParticlesAroundSelf(sub, ParticleTypes.f_123767_, 2.0D);
                  TensuraParticleHelper.addServerParticlesAroundSelf(sub, ParticleTypes.f_123747_, 1.0D);
                  if (sub instanceof Player) {
                     Player player = (Player)sub;
                     player.refreshDisplayName();
                     if (owner != null) {
                        player.m_5661_(Component.m_237110_("tensura.naming.name_success", new Object[]{event.getName(), owner.m_7755_()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                     } else {
                        player.m_5661_(Component.m_237110_("tensura.naming.name_success.no_namer", new Object[]{event.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                     }

                     TensuraPlayerCapability.setTrackedRace(player, (Race)null);
                     TensuraPlayerCapability.sync(player);
                     TensuraEPCapability.sync(player);
                  } else if (owner != null) {
                     label45: {
                        if (sub instanceof TamableAnimal) {
                           TamableAnimal tamable = (TamableAnimal)sub;
                           if (!ForgeEventFactory.onAnimalTame(tamable, owner)) {
                              tamable.m_21828_(owner);
                              break label45;
                           }
                        }

                        if (sub instanceof AbstractHorse) {
                           AbstractHorse horse = (AbstractHorse)sub;
                           if (!ForgeEventFactory.onAnimalTame(horse, owner)) {
                              horse.m_30637_(owner);
                           }
                        }
                     }
                  }

                  namingReward(sub, Math.min((Double)TensuraConfig.INSTANCE.namingConfig.maxEPGain.get(), originalCost), event.getType());
                  sub.m_5634_(sub.m_21233_());
                  SkillHelper.removePredicateEffect(sub, (effect) -> {
                     return effect.m_19483_().equals(MobEffectCategory.HARMFUL);
                  });
                  if (owner != null) {
                     TensuraPlayerCapability.getFrom(owner).ifPresent((cap) -> {
                        cap.setMagicule(cap.getMagicule() - cost);
                        if (shouldConsumeMax(event.getType(), owner)) {
                           cap.setBaseMagicule(cap.getBaseMagicule() - cost, owner);
                        }

                        TensuraPlayerCapability.sync(owner);
                     });
                  }

               }
            }
         });
      }
   }

   private static boolean shouldConsumeMax(RequestNamingGUIPacket.NamingType type, Player owner) {
      if (type == RequestNamingGUIPacket.NamingType.MEDIUM) {
         return owner.m_217043_().m_216339_(0, 100) <= 10;
      } else if (type == RequestNamingGUIPacket.NamingType.HIGH) {
         return owner.m_217043_().m_216339_(0, 100) <= 30;
      } else {
         return false;
      }
   }

   public static void namingReward(LivingEntity sub, double gainedEP, RequestNamingGUIPacket.NamingType type) {
      if (sub instanceof Player) {
         Player player = (Player)sub;
         TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
            cap.setBaseMagicule(cap.getBaseMagicule() + gainedEP / 2.0D, sub);
            cap.setBaseAura(cap.getBaseAura() + gainedEP / 2.0D, sub);
            cap.setMagicule(cap.getMagicule());
            cap.setAura(cap.getAura());
            TensuraPlayerCapability.sync(player);
            if (type != RequestNamingGUIPacket.NamingType.LOW) {
               Race race = cap.getRace();
               if (race != null) {
                  Race evolution = race.getHarvestFestivalEvolution(player);
                  if (evolution != null) {
                     RaceHelper.evolveRace(player, evolution, true);
                     sub.f_19853_.m_6263_((Player)null, sub.m_20185_(), sub.m_20186_(), sub.m_20189_(), SoundEvents.f_11887_, SoundSource.NEUTRAL, 1.0F, 1.0F);
                  }

                  if (type == RequestNamingGUIPacket.NamingType.HIGH) {
                     SkillStorage storage = SkillAPI.getSkillsFrom(player);
                     Iterator var9 = storage.getLearnedSkills().iterator();

                     while(true) {
                        ManasSkillInstance instance;
                        int newMastery;
                        while(true) {
                           int oldMastery;
                           do {
                              do {
                                 if (!var9.hasNext()) {
                                    storage.syncChanges();
                                    return;
                                 }

                                 instance = (ManasSkillInstance)var9.next();
                                 oldMastery = instance.getMastery();
                              } while(oldMastery >= 0);
                           } while(player.m_217043_().m_188499_());

                           int masteryChance = player.m_217043_().m_188503_(100);
                           newMastery = oldMastery + (masteryChance < 50 ? 25 : (masteryChance < 75 ? 50 : (masteryChance < 95 ? 75 : 100)));
                           if (newMastery < 0) {
                              break;
                           }

                           UnlockSkillEvent event = new UnlockSkillEvent(instance, player);
                           if (!MinecraftForge.EVENT_BUS.post(event)) {
                              player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{instance.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                              instance.onLearnSkill(player, event);
                              break;
                           }
                        }

                        instance.setMastery(Math.min(newMastery, 0));
                        instance.markDirty();
                     }
                  }
               }
            }
         });
      } else {
         TensuraEPCapability.getFrom(sub).ifPresent((cap) -> {
            cap.setEP(sub, cap.getEP() + gainedEP);
         });
         if (type != RequestNamingGUIPacket.NamingType.LOW) {
            RaceHelper.evolveMobs(sub);
         }

      }
   }

   public static enum NamingType {
      LOW,
      MEDIUM,
      HIGH;

      // $FF: synthetic method
      private static RequestNamingGUIPacket.NamingType[] $values() {
         return new RequestNamingGUIPacket.NamingType[]{LOW, MEDIUM, HIGH};
      }
   }
}
