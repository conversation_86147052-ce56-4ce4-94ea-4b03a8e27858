package com.github.manasmods.tensura.network.play2server;

import com.github.manasmods.tensura.Tensura;
import java.util.function.Supplier;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.NetworkEvent.Context;

public class RequestContainerButtonClickPacket {
   private final int containerId;
   private final int buttonId;

   public RequestContainerButtonClickPacket(int pContainerId, int pButtonId) {
      this.containerId = pContainerId;
      this.buttonId = pButtonId;
   }

   public RequestContainerButtonClickPacket(FriendlyByteBuf pBuffer) {
      this.containerId = pBuffer.readByte();
      this.buttonId = pBuffer.readInt();
   }

   public void write(FriendlyByteBuf pBuffer) {
      pBuffer.writeByte(this.containerId);
      pBuffer.writeInt(this.buttonId);
   }

   public void handle(Supplier<Context> ctx) {
      ((Context)ctx.get()).enqueueWork(() -> {
         ServerPlayer player = ((Context)ctx.get()).getSender();
         if (player != null) {
            player.m_9243_();
            if (player.f_36096_.f_38840_ == this.getContainerId()) {
               if (!player.m_5833_()) {
                  if (!player.f_36096_.m_6875_(player)) {
                     Tensura.getLogger().debug("Player {} interacted with invalid menu {}", player, player.f_36096_);
                  } else if (player.f_36096_.m_6366_(player, this.getButtonId())) {
                     player.f_36096_.m_38946_();
                  }

               }
            }
         }
      });
   }

   public int getContainerId() {
      return this.containerId;
   }

   public int getButtonId() {
      return this.buttonId;
   }
}
