package com.github.manasmods.tensura.ability.skill.common;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.entity.magic.field.GravityField;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class GravityFieldSkill extends Skill {
   public GravityFieldSkill() {
      super(Skill.SkillType.COMMON);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return newEP > 20000.0D;
   }

   public double learningCost() {
      return 50.0D;
   }

   public int modes() {
      return 3;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      int var10000;
      if (reverse) {
         switch(instance.getMode()) {
         case 1:
            var10000 = this.isMastered(instance, entity) ? 3 : 2;
            break;
         case 2:
            var10000 = 1;
            break;
         case 3:
            var10000 = 2;
            break;
         default:
            var10000 = 0;
         }

         return var10000;
      } else {
         switch(instance.getMode()) {
         case 1:
            var10000 = 2;
            break;
         case 2:
            var10000 = this.isMastered(instance, entity) ? 3 : 1;
            break;
         default:
            var10000 = 1;
         }

         return var10000;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.gravity_field.self");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.gravity_field.5");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.gravity_field.10");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 50.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!SkillHelper.outOfMagicule(entity, instance)) {
         this.addMasteryPoint(instance, entity);
         instance.setCoolDown(5);
         int fieldLife = this.isMastered(instance, entity) ? 2400 : 1200;
         GravityField sphere;
         switch(instance.getMode()) {
         case 1:
            entity.m_147207_(new MobEffectInstance(MobEffects.f_19596_, fieldLife, 1, false, true), entity);
            entity.m_147207_(new MobEffectInstance(MobEffects.f_19591_, fieldLife, 0, false, true), entity);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11868_, SoundSource.PLAYERS, 1.0F, 1.0F);
            break;
         case 2:
            sphere = new GravityField(entity.m_9236_(), entity);
            sphere.setLife(fieldLife);
            sphere.setRadius(6.0F);
            sphere.m_146884_(entity.m_20182_().m_82520_(0.0D, -5.0D, 0.0D));
            entity.m_9236_().m_7967_(sphere);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11868_, SoundSource.PLAYERS, 1.0F, 1.0F);
            break;
         case 3:
            sphere = new GravityField(entity.m_9236_(), entity);
            sphere.setLife(fieldLife);
            sphere.setRadius(10.0F);
            sphere.m_146884_(entity.m_20182_().m_82520_(0.0D, -10.0D, 0.0D));
            entity.m_9236_().m_7967_(sphere);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11868_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

      }
   }
}
