package com.github.manasmods.tensura.ability.skill.common;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.entity.magic.barrier.BarrierEntity;
import com.github.manasmods.tensura.entity.magic.barrier.BarrierPart;
import com.github.manasmods.tensura.entity.magic.barrier.RangedBarrierEntity;
import com.github.manasmods.tensura.registry.TensuraStats;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.stats.StatType;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Block;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;

public class RangedBarrierSkill extends Skill {
   public RangedBarrierSkill() {
      super(Skill.SkillType.COMMON);
   }

   public double learningCost() {
      return 100.0D;
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (entity instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)entity;
         if (player.m_8951_().m_13015_(((StatType)TensuraStats.BOSS_KILLED.get()).m_12902_((EntityType)TensuraEntityTypes.IFRIT.get())) <= 0) {
            return false;
         } else {
            return TensuraEPCapability.getEP(entity) >= 80000.0D;
         }
      } else {
         return false;
      }
   }

   public int modes() {
      return 3;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      int var10000;
      if (reverse) {
         switch(instance.getMode()) {
         case 1:
            var10000 = this.isMastered(instance, entity) ? 3 : 2;
            break;
         case 2:
            var10000 = 1;
            break;
         case 3:
            var10000 = 2;
            break;
         default:
            var10000 = 0;
         }

         return var10000;
      } else {
         switch(instance.getMode()) {
         case 1:
            var10000 = 2;
            break;
         case 2:
            var10000 = this.isMastered(instance, entity) ? 3 : 1;
            break;
         default:
            var10000 = 1;
         }

         return var10000;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.ranged_barrier.5");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.ranged_barrier.10");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.ranged_barrier.20");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return (double)(5 * this.getRadiusMode(instance.getMode()));
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      ManasSkill skill = (ManasSkill)ExtraSkills.MULTILAYER_BARRIER.get();
      ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
      manipulation.setMastery(-100);
      if (storage.learnSkill(manipulation) && entity instanceof Player) {
         Player player = (Player)entity;
         player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
      }

   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      Class<? extends Entity> targetClass = entity.m_6144_() ? BarrierPart.class : LivingEntity.class;
      Entity target = SkillHelper.getTargetingEntity(targetClass, entity, 30.0D, 0.1D, false, false);
      Vec3 pos;
      if (target != null) {
         pos = target.m_20182_().m_82520_(0.0D, (double)(target.m_20206_() / 2.0F), 0.0D);
         if (entity.m_6144_() && target instanceof BarrierPart) {
            BarrierPart part = (BarrierPart)target;
            BarrierEntity var9 = part.barrier;
            if (var9 instanceof RangedBarrierEntity) {
               RangedBarrierEntity barrier = (RangedBarrierEntity)var9;
               if (barrier.m_37282_() == entity) {
                  barrier.m_146870_();
                  level.m_6263_((Player)null, pos.m_7096_(), pos.m_7098_(), pos.m_7094_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  return;
               }
            }
         }
      } else {
         BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, Block.OUTLINE, 30.0D);
         pos = result.m_82450_().m_82520_(0.0D, 0.5D, 0.0D);
      }

      if (!SkillHelper.outOfMagicule(entity, instance)) {
         RangedBarrierEntity sphere = new RangedBarrierEntity(entity.m_9236_(), entity);
         sphere.m_146884_(pos);
         sphere.setMpCost(this.magiculeCost(entity, instance));
         sphere.setSkill(instance);
         sphere.setHealth(entity.m_21233_() / 2.0F);
         sphere.setLife(instance.isMastered(entity) ? 2400 : 1200);
         sphere.setRadius((float)this.getRadiusMode(instance.getMode()));
         entity.m_9236_().m_7967_(sphere);
         if (target != null) {
            this.addMasteryPoint(instance, entity);
         }

         instance.setCoolDown(instance.getMode());
         entity.m_21011_(InteractionHand.MAIN_HAND, true);
         level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11868_, SoundSource.PLAYERS, 0.5F, 1.0F);
      }
   }

   public int getRadiusMode(int mode) {
      byte var10000;
      switch(mode) {
      case 1:
         var10000 = 2;
         break;
      case 2:
         var10000 = 5;
         break;
      case 3:
         var10000 = 10;
         break;
      default:
         var10000 = 0;
      }

      return var10000;
   }
}
