package com.github.manasmods.tensura.ability.skill.common;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.extra.HakiSkill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.SimpleParticleType;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;

public class CoercionSkill extends Skill {
   public CoercionSkill() {
      super(Skill.SkillType.COMMON);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return newEP > 5000.0D;
   }

   public double learningCost() {
      return 30.0D;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 30.0D;
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      if (!(TensuraEPCapability.getEP(entity) < 100000.0D)) {
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         ManasSkill skill = (ManasSkill)ExtraSkills.HAKI.get();
         ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
         manipulation.setMastery(-100);
         if (storage.learnSkill(manipulation) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }

      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!SkillHelper.outOfMagicule(entity, instance)) {
         Level level = entity.m_9236_();
         Vec3 targetPos = entity.m_146892_().m_82549_(entity.m_20154_().m_82490_(14.0D));
         Vec3 source = entity.m_146892_().m_82549_(entity.m_20154_().m_82490_(2.0D));
         Vec3 offSetToTarget = targetPos.m_82546_(source);
         Vec3 normalizes = offSetToTarget.m_82541_();
         double scale = instance.getTag() == null ? 1.0D : Math.max(instance.getTag().m_128459_("scale"), 1.0D);
         double ownerEP = TensuraEPCapability.getEP(entity) * scale;
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_215771_, SoundSource.PLAYERS, 5.0F, 1.0F);
         boolean success = false;

         for(int particleIndex = 1; particleIndex < Mth.m_14107_(offSetToTarget.m_82553_()); ++particleIndex) {
            Vec3 particlePos = source.m_82549_(normalizes.m_82490_((double)particleIndex));
            ((ServerLevel)level).m_8767_((SimpleParticleType)TensuraParticles.SONIC_SOUND.get(), particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_, 1, 0.0D, 0.0D, 0.0D, 0.0D);
            AABB aabb = (new AABB(new BlockPos(particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_))).m_82400_(4.0D);
            List<LivingEntity> livingEntityList = level.m_6443_(LivingEntity.class, aabb, (entityData) -> {
               return !entityData.m_7306_(entity) && !entityData.m_7307_(entity);
            });
            if (!livingEntityList.isEmpty()) {
               success = true;
               Iterator var17 = livingEntityList.iterator();

               while(var17.hasNext()) {
                  LivingEntity target = (LivingEntity)var17.next();
                  double targetEP = TensuraEPCapability.getEP(target);
                  double difference = ownerEP / targetEP;
                  if (!(difference <= 2.0D)) {
                     int fearLevel = (int)(difference * 0.25D - 0.5D);
                     fearLevel = Math.min(fearLevel, (Integer)TensuraConfig.INSTANCE.mobEffectConfig.maxFear.get());
                     SkillHelper.checkThenAddEffectSource(target, entity, (MobEffect)TensuraMobEffects.FEAR.get(), 200, fearLevel);
                     target.m_147207_(new MobEffectInstance(MobEffects.f_19613_, 200, fearLevel, false, false), entity);
                     HakiSkill.hakiPush(target, entity, fearLevel);
                  }
               }
            }
         }

         if (success) {
            this.addMasteryPoint(instance, entity);
            instance.setCoolDown(instance.isMastered(entity) ? 1 : 2);
         }

      }
   }

   public void onScroll(ManasSkillInstance instance, LivingEntity entity, double delta) {
      HakiSkill.changeEPUsed(instance, entity, delta);
   }
}
