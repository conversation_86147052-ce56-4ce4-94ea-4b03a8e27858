package com.github.manasmods.tensura.ability.skill.common;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.phys.Vec3;

public class GravityFlightSkill extends Skill {
   public GravityFlightSkill() {
      super(Skill.SkillType.COMMON);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return newEP > 10000.0D;
   }

   public double learningCost() {
      return 50.0D;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 5.0D;
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      Player player;
      if (entity.m_21023_((MobEffect)TensuraMobEffects.MAGIC_INTERFERENCE.get())) {
         if (entity instanceof Player) {
            player = (Player)entity;
            player.m_5661_(Component.m_237115_("tensura.skill.magic_interference").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
         }

         return false;
      } else if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
         if (entity instanceof Player) {
            player = (Player)entity;
            CompoundTag tag = instance.getOrCreateTag();
            player.m_150110_().f_35935_ = tag.m_128471_("prevFly");
            player.m_150110_().m_35943_(tag.m_128457_("prevFlySpeed"));
            player.m_6885_();
         }

         return false;
      } else {
         if (heldTicks % 100 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         entity.m_183634_();
         this.spawnParticles(entity);
         Vec3 delta = entity.m_20184_();
         double dy = delta.f_82480_ <= 0.0D ? 0.11999999731779099D : delta.f_82480_ + 0.11999999731779099D;
         entity.m_20256_(new Vec3(delta.m_7096_(), dy, delta.m_7094_()));
         entity.f_19864_ = true;
         entity.f_19812_ = true;
         return true;
      }
   }

   private void spawnParticles(LivingEntity entity) {
      for(int i = 0; i < 5; ++i) {
         float random = (entity.m_217043_().m_188501_() - 0.5F) * 0.1F;
         TensuraParticleHelper.spawnServerParticles(entity.m_9236_(), ParticleTypes.f_123790_, entity.m_20208_(1.0D), entity.m_20187_(), entity.m_20262_(1.0D), 0, (double)random, -0.2D, (double)random, 1.0D, false);
         TensuraParticleHelper.spawnServerParticles(entity.m_9236_(), ParticleTypes.f_123783_, entity.m_20208_(1.0D), entity.m_20187_(), entity.m_20262_(1.0D), 0, (double)random, -0.2D, (double)random, 1.0D, false);
      }

   }
}
