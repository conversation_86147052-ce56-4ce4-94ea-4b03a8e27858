package com.github.manasmods.tensura.ability.skill.resist;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import javax.annotation.Nullable;
import net.minecraftforge.event.entity.living.LivingDamageEvent;

public class PainResistanceSkill extends ResistSkill {
   public void onTakenDamage(ManasSkillInstance instance, LivingDamageEvent event) {
      if (instance.getMastery() < 0) {
         if ((double)event.getAmount() > this.learningCost()) {
            this.addLearnPoint(instance, event.getEntity());
         }

      }
   }

   public double learningCost() {
      return 20.0D;
   }

   public int pointRequirement() {
      return 350;
   }

   @Nullable
   protected ManasSkill getNullificationForm() {
      return (ManasSkill)ResistanceSkills.PAIN_NULLIFICATION.get();
   }
}
