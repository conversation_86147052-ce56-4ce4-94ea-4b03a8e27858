package com.github.manasmods.tensura.ability.skill.resist;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.google.common.collect.ImmutableList;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import org.jetbrains.annotations.NotNull;

public class SpiritualAttackNullification extends ResistSkill {
   public static final ImmutableList<MobEffect> SPIRITUAL_NULL;

   public SpiritualAttackNullification() {
      super(ResistSkill.ResistType.NULLIFICATION);
   }

   public boolean isDamageResisted(DamageSource damageSource, ManasSkillInstance instance) {
      return DamageSourceHelper.isSpiritual(damageSource);
   }

   @NotNull
   public List<MobEffect> getImmuneEffects(ManasSkillInstance instance, LivingEntity entity) {
      return (List)(!instance.isToggled() ? new ArrayList() : SPIRITUAL_NULL);
   }

   static {
      SPIRITUAL_NULL = ImmutableList.of((MobEffect)TensuraMobEffects.INSANITY.get(), (MobEffect)TensuraMobEffects.FEAR.get());
   }
}
