package com.github.manasmods.tensura.ability.skill.resist;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import javax.annotation.Nullable;
import net.minecraft.world.damagesource.DamageSource;

public class PierceResistance extends ResistSkill {
   public boolean isDamageResisted(DamageSource damageSource, ManasSkillInstance instance) {
      return DamageSourceHelper.isPierce(damageSource);
   }

   public double learningCost() {
      return 20.0D;
   }

   public int pointRequirement() {
      return 350;
   }

   @Nullable
   protected ManasSkill getNullificationForm() {
      return (ManasSkill)ResistanceSkills.PIERCE_NULLIFICATION.get();
   }
}
