package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.entity.magic.skill.ReflectorEchoProjectile;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import java.text.DecimalFormat;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.event.entity.ProjectileImpactEvent;
import net.minecraftforge.event.entity.living.LivingAttackEvent;
import net.minecraftforge.event.entity.living.LivingDamageEvent;
import net.minecraftforge.event.entity.player.PlayerEvent.PlayerRespawnEvent;

public class ReflectorSkill extends Skill {
   protected static final String COUNTER = "d621dabb-6d0d-44d0-a343-d369878980ba";
   private final DecimalFormat decimalFormat = new DecimalFormat("#.#");

   public ReflectorSkill() {
      super(Skill.SkillType.UNIQUE);
      this.addHeldAttributeModifier(Attributes.f_22279_, "d621dabb-6d0d-44d0-a343-d369878980ba", -1.0D, Operation.MULTIPLY_TOTAL);
      this.addHeldAttributeModifier(Attributes.f_22281_, "d621dabb-6d0d-44d0-a343-d369878980ba", -1.0D, Operation.MULTIPLY_TOTAL);
      this.addHeldAttributeModifier(Attributes.f_22283_, "d621dabb-6d0d-44d0-a343-d369878980ba", -1.0D, Operation.MULTIPLY_TOTAL);
      this.addHeldAttributeModifier(Attributes.f_22288_, "d621dabb-6d0d-44d0-a343-d369878980ba", -1.0D, Operation.MULTIPLY_TOTAL);
      this.addHeldAttributeModifier((Attribute)ForgeMod.REACH_DISTANCE.get(), "d621dabb-6d0d-44d0-a343-d369878980ba", -1.0D, Operation.MULTIPLY_TOTAL);
      this.addHeldAttributeModifier((Attribute)ForgeMod.SWIM_SPEED.get(), "d621dabb-6d0d-44d0-a343-d369878980ba", -1.0D, Operation.MULTIPLY_TOTAL);
   }

   public double getObtainingEpCost() {
      return 30000.0D;
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.reflector.reflection");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.reflector.counter");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public static boolean hasFullCounter(LivingEntity entity) {
      AttributeInstance attributeInstance = entity.m_21051_(Attributes.f_22279_);
      if (attributeInstance == null) {
         return false;
      } else {
         return attributeInstance.m_22111_(UUID.fromString("d621dabb-6d0d-44d0-a343-d369878980ba")) != null;
      }
   }

   public void onProjectileHit(ManasSkillInstance instance, LivingEntity entity, ProjectileImpactEvent e) {
      if (!instance.onCoolDown()) {
         if (hasFullCounter(entity)) {
            Projectile projectile = e.getProjectile();
            Vec3 location = e.getRayTraceResult().m_82450_();
            projectile.m_146884_(location);
            Vec3 reverse = projectile.m_20184_().m_82548_().m_82490_(2.0D);
            projectile.m_20256_(reverse);
            projectile.m_5602_(entity);
            if (projectile instanceof TensuraProjectile) {
               TensuraProjectile tensuraProjectile = (TensuraProjectile)projectile;
               tensuraProjectile.setDamage(tensuraProjectile.getDamage() * 2.5F);
            }

            e.setCanceled(true);
            instance.setCoolDown(5);
            this.removeHeldAttributeModifiers(instance, entity);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.YELLOW_LIGHTNING_SPARK.get());
            TensuraParticleHelper.spawnServerParticles(entity.f_19853_, (ParticleOptions)TensuraParticles.YELLOW_LIGHTNING_SPARK.get(), entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.5D, true);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }
      }
   }

   public void onBeingDamaged(ManasSkillInstance instance, LivingAttackEvent e) {
      LivingEntity entity = e.getEntity();
      if (!instance.onCoolDown()) {
         if (hasFullCounter(entity)) {
            DamageSource damageSource = e.getSource();
            if (!damageSource.m_19378_()) {
               if (damageSource.m_7640_() != null) {
                  if (damageSource instanceof TensuraDamageSource) {
                     TensuraDamageSource source = (TensuraDamageSource)damageSource;
                     if (source.getIgnoreBarrier() >= 1.0F) {
                        return;
                     }
                  }

                  Entity sourceEntity = e.getSource().m_7639_();
                  if (sourceEntity != null) {
                     sourceEntity.f_19802_ = 0;
                     sourceEntity.m_6469_(DamageSourceHelper.turnTensura(e.getSource(), entity), e.getAmount() * 2.5F);
                  }

                  e.setCanceled(true);
                  instance.setCoolDown(5);
                  this.removeHeldAttributeModifiers(instance, entity);
                  TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.YELLOW_LIGHTNING_SPARK.get());
                  TensuraParticleHelper.spawnServerParticles(entity.f_19853_, (ParticleOptions)TensuraParticles.YELLOW_LIGHTNING_SPARK.get(), entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.5D, true);
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            }
         }
      }
   }

   public double getAttributeModifierAmplifier(ManasSkillInstance instance, LivingEntity entity, AttributeModifier modifier) {
      return modifier.m_22218_() * (double)(instance.isMastered(entity) ? 0 : 1);
   }

   public void addHeldAttributeModifiers(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() != 1 && !instance.onCoolDown()) {
         super.addHeldAttributeModifiers(instance, entity);
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() == 1) {
         return false;
      } else if (instance.onCoolDown()) {
         return false;
      } else {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (player.m_150110_().f_35935_) {
               player.m_150110_().f_35935_ = false;
               player.m_6885_();
            }
         }

         return true;
      }
   }

   public void onTakenDamage(ManasSkillInstance instance, LivingDamageEvent e) {
      CompoundTag tag = instance.getOrCreateTag();
      float newEcho = tag.m_128457_("echo") + e.getAmount();
      if (newEcho > this.echoCap(e.getEntity())) {
         newEcho = this.echoCap(e.getEntity());
      }

      tag.m_128350_("echo", newEcho);
      instance.markDirty();
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() != 2 && !entity.m_6144_()) {
         CompoundTag tag = instance.getOrCreateTag();
         float echoPoints = tag.m_128457_("echo");
         if (echoPoints > 0.0F) {
            this.addMasteryPoint(instance, entity);
            double scale = tag.m_128459_("scale");
            if (scale < 0.1D || echoPoints <= 1.0F) {
               scale = 1.0D;
            }

            ReflectorEchoProjectile echo = new ReflectorEchoProjectile(entity.m_9236_(), entity);
            echo.setSpeed(1.5F);
            echo.m_20242_(true);
            float baseDamage = echoPoints * (float)scale;
            float multiplier = instance.isMastered(entity) ? 5.0F : 3.0F;
            echo.setDamage(baseDamage * multiplier);
            echo.setMpCost(this.magiculeCost(entity, instance));
            echo.setSkill(instance);
            echo.setPosAndShoot(entity);
            entity.m_9236_().m_7967_(echo);
            tag.m_128350_("echo", echoPoints - baseDamage);
            if (entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237110_("tensura.skill.reflector.remaining_echo", new Object[]{this.decimalFormat.format((double)(echoPoints - baseDamage))}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
            }

            instance.markDirty();
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
         } else if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.reflector.remaining_echo", new Object[]{0}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
         }

      }
   }

   public float echoCap(LivingEntity living) {
      int bonus = (int)TensuraEPCapability.getEP(living) / 1000;
      return (float)(100 + bonus);
   }

   public void onRespawn(ManasSkillInstance instance, PlayerRespawnEvent event) {
      if (!event.isEndConquered()) {
         instance.getOrCreateTag().m_128350_("echo", 0.0F);
         instance.markDirty();
      }
   }

   public void onScroll(ManasSkillInstance instance, LivingEntity entity, double delta) {
      if (instance.getMode() == 1 && entity.m_6144_()) {
         CompoundTag tag = instance.getOrCreateTag();
         double newScale = tag.m_128459_("scale") + delta * 0.1D;
         if (newScale > 1.0D) {
            newScale = 0.1D;
         } else if (newScale < 0.1D) {
            newScale = 1.0D;
         }

         if (tag.m_128459_("scale") != newScale) {
            tag.m_128347_("scale", newScale);
            if (entity instanceof Player) {
               Player player = (Player)entity;
               Object[] var10002 = new Object[1];
               String var10005 = this.decimalFormat.format(newScale * 100.0D);
               var10002[0] = var10005 + "%";
               player.m_5661_(Component.m_237110_("tensura.skill.power_scale", var10002).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
            }

            instance.markDirty();
         }

      }
   }
}
