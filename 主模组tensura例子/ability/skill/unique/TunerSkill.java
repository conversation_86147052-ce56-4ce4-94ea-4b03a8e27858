package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import java.util.Iterator;
import java.util.function.Predicate;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.nbt.Tag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.ProjectileImpactEvent;
import net.minecraftforge.event.entity.living.LivingAttackEvent;
import net.minecraftforge.event.entity.living.LivingDeathEvent;

public class TunerSkill extends Skill {
   public TunerSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 50000.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isMastered(entity);
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      if (!instance.isToggled() && !this.isInSlot(entity)) {
         return false;
      } else {
         return entity.m_21223_() < entity.m_21233_() / 4.0F;
      }
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int level = instance.isMastered(entity) ? 1 : 0;
      entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FATE_CHANGE.get(), 240, level, false, false, false));
      if (level == 0) {
         int time = tag.m_128451_("activatedTimes");
         if (time % 10 == 0) {
            this.addMasteryPoint(instance, entity);
         }

         tag.m_128405_("activatedTimes", time + 1);
      }

   }

   public static void clearDeathTypes(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      if (tag.m_128441_("deaths")) {
         tag.m_128473_("deaths");
         instance.markDirty();
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 2.0F, 1.0F);
      }
   }

   private boolean checkDeathType(ManasSkillInstance instance, DamageSource source) {
      CompoundTag tag = instance.getOrCreateTag();
      if (!tag.m_128441_("deaths")) {
         return false;
      } else {
         ListTag deaths = (ListTag)tag.m_128423_("deaths");
         if (deaths == null) {
            return false;
         } else {
            Iterator var5 = deaths.iterator();

            while(var5.hasNext()) {
               Tag value = (Tag)var5.next();
               if (value instanceof CompoundTag) {
                  CompoundTag target = (CompoundTag)value;
                  if (target.m_128441_(source.m_19385_())) {
                     return true;
                  }
               }
            }

            return false;
         }
      }
   }

   private void addDeathType(ManasSkillInstance instance, DamageSource source) {
      CompoundTag tag = instance.getOrCreateTag();
      ListTag deaths;
      CompoundTag death;
      if (!tag.m_128441_("deaths")) {
         deaths = new ListTag();
         death = new CompoundTag();
         death.m_128379_(source.m_19385_(), true);
         deaths.add(death);
         tag.m_128365_("deaths", deaths);
      } else {
         deaths = (ListTag)tag.m_128423_("deaths");
         if (deaths == null) {
            return;
         }

         death = new CompoundTag();
         death.m_128379_(source.m_19385_(), true);
         deaths.add(death);
         tag.m_128365_("deaths", deaths);
      }

      instance.markDirty();
   }

   public void onBeingDamaged(ManasSkillInstance instance, LivingAttackEvent event) {
      if (!event.isCanceled()) {
         LivingEntity entity = event.getEntity();
         if (this.isInSlot(entity)) {
            if (!(entity.m_21223_() > entity.m_21233_())) {
               DamageSource damageSource = event.getSource();
               if (!damageSource.m_19378_()) {
                  if (damageSource.m_7640_() != null && damageSource.m_7640_() == damageSource.m_7639_()) {
                     if (!damageSource.m_19387_()) {
                        if (entity.m_217043_().m_188503_(4) == 2) {
                           TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123766_, 1.0D);
                           entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
                           event.setCanceled(true);
                           if (SkillUtils.canNegateDodge(entity, damageSource)) {
                              event.setCanceled(false);
                           }

                        }
                     }
                  }
               }
            }
         }
      }
   }

   public void onProjectileHit(ManasSkillInstance instance, LivingEntity entity, ProjectileImpactEvent event) {
      if (this.isInSlot(entity)) {
         if (!(entity.m_21223_() > entity.m_21233_())) {
            if (!SkillUtils.isProjectileAlwaysHit(event.getProjectile())) {
               boolean chance = instance.isMastered(entity) ? entity.m_217043_().m_188499_() : entity.m_217043_().m_188503_(4) != 2;
               if (!chance) {
                  TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123766_, 1.0D);
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
                  event.setCanceled(true);
               }
            }
         }
      }
   }

   public void onDeath(ManasSkillInstance instance, LivingDeathEvent event) {
      if (!event.isCanceled()) {
         DamageSource source = event.getSource();
         if (source != DamageSource.f_19317_) {
            if (source instanceof TensuraDamageSource) {
               TensuraDamageSource damageSource = (TensuraDamageSource)source;
               if (damageSource.getIgnoreBarrier() >= 3.0F) {
                  return;
               }
            }

            LivingEntity entity = event.getEntity();
            if (!entity.m_6084_()) {
               if (source.m_7639_() != null) {
                  if (source.m_7639_() == entity) {
                     return;
                  }

                  if (source.m_7639_() == SkillHelper.getSubordinateOwner(entity)) {
                     return;
                  }
               }

               if (!this.checkDeathType(instance, source)) {
                  this.addMasteryPoint(instance, entity);
                  entity.m_21153_(Math.max(entity.m_21223_(), entity.m_21233_()));
                  entity.f_19802_ = Math.max(60, entity.f_19802_);
                  Predicate<MobEffect> predicate = (effect) -> {
                     return effect.m_19483_() == MobEffectCategory.HARMFUL;
                  };
                  SkillHelper.removePredicateEffect(entity, predicate);
                  TensuraEffectsCapability.resetEverything(entity, false, false);
                  TensuraEPCapability.getFrom(entity).ifPresent((cap) -> {
                     if (cap.getEP() <= 0.0D) {
                        cap.setEP(entity, 100.0D, false);
                     } else if (cap.getCurrentEP() <= 0.0D) {
                        cap.setCurrentEP(entity, cap.getEP() * 0.5D);
                     }

                     double SHP = entity.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get());
                     if (cap.getSpiritualHealth() < SHP * 0.5D) {
                        cap.setSpiritualHealth(SHP * 0.5D);
                     }

                  });
                  TensuraEPCapability.sync(entity);
                  if (entity instanceof Player) {
                     Player player = (Player)entity;
                     TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                        float multiplier = instance.isMastered(entity) ? 0.75F : 0.5F;
                        cap.setMagicule(Math.max(cap.getMagicule(), player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get()) * (double)multiplier));
                        cap.setAura(Math.max(cap.getAura(), player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_AURA.get()) * (double)multiplier));
                        TensuraPlayerCapability.sync(player);
                     });
                  }

                  this.addDeathType(instance, source);
                  event.setCanceled(true);
                  if (!instance.onCoolDown()) {
                     instance.setCoolDown(1200);
                  }

                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12513_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123767_, 1.0D);
                  TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123767_, 2.0D);
                  TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123747_, 1.0D);
               }
            }
         }
      }
   }
}
