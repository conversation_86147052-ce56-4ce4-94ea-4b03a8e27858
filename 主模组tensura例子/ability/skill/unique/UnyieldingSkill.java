package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.human.CloneEntity;
import com.github.manasmods.tensura.event.SkillPlunderEvent;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.Level;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.living.LivingDeathEvent;

public class UnyieldingSkill extends Skill {
   public UnyieldingSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 30000.0D;
   }

   public double learningCost() {
      return 10000.0D;
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.unyielding.return");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.unyielding.backup");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return true;
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      Collection<String> uuidList = (Collection)entity.m_9236_().m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(30.0D), (target) -> {
         return this.isNamed(entity, target);
      }).stream().map(Entity::m_20149_).collect(Collectors.toList());
      CompoundTag tag = instance.getOrCreateTag();
      if (tag.m_128441_("UnyieldingList")) {
         CompoundTag list = (CompoundTag)tag.m_128423_("UnyieldingList");
         if (list == null) {
            return;
         }

         List<String> keyList = List.copyOf(list.m_128431_());
         Iterator var7;
         String key;
         if (!keyList.isEmpty()) {
            var7 = keyList.iterator();

            while(var7.hasNext()) {
               key = (String)var7.next();
               if (uuidList.contains(key)) {
                  list.m_128405_(key, list.m_128451_(key) + (instance.isMastered(entity) ? 2 : 1));
                  uuidList.remove(key);
               } else {
                  int point = list.m_128451_(key) - 1;
                  list.m_128405_(key, point);
                  if (point <= 0) {
                     list.m_128473_(key);
                  }
               }
            }
         }

         if (!uuidList.isEmpty()) {
            var7 = uuidList.iterator();

            while(var7.hasNext()) {
               key = (String)var7.next();
               list.m_128405_(key, Math.min(list.m_128451_(key) + 10, 1200));
            }
         }

         instance.markDirty();
      } else if (!uuidList.isEmpty()) {
         Iterator var10 = uuidList.iterator();

         while(var10.hasNext()) {
            String uuid = (String)var10.next();
            CompoundTag list = new CompoundTag();
            list.m_128405_(uuid, 1);
            tag.m_128365_("UnyieldingList", list);
         }

         instance.markDirty();
      }

   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() == 1) {
         LivingEntity target = SkillHelper.getTargetingEntity(entity, 6.0D, false);
         if (target != null && target.m_7307_(entity) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.mode.unyielding.check_point", new Object[]{target.m_7755_(), this.getUnyieldingPoint(instance, target)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.AQUA)), true);
         }

      } else {
         Level var4 = entity.m_9236_();
         if (var4 instanceof ServerLevel) {
            ServerLevel level = (ServerLevel)var4;
            CompoundTag tag = instance.getOrCreateTag();
            double maxMP = entity.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get());
            if (!tag.m_128441_("Backup")) {
               if (!SkillHelper.outOfMagicule(entity, maxMP * 0.25D)) {
                  this.spawnBackup(instance, entity, false);
                  instance.setCoolDown(instance.isMastered(entity) ? 3 : 5);
               }

            } else {
               UUID uuid = tag.m_128342_("Backup");
               Entity var9 = SkillHelper.getEntityFromUUID(level, uuid, (clone) -> {
                  return clone instanceof CloneEntity;
               });
               if (var9 instanceof CloneEntity) {
                  CloneEntity backup = (CloneEntity)var9;
                  Player player;
                  if (entity.m_6144_()) {
                     if (entity instanceof Player) {
                        player = (Player)entity;
                        player.m_5661_(Component.m_237115_("tensura.skill.mode.unyielding.backup_remove").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
                     }

                     backup.remove();
                  } else if (backup.m_9236_() != entity.m_9236_() && !instance.isMastered(entity)) {
                     if (entity instanceof Player) {
                        player = (Player)entity;
                        player.m_5661_(Component.m_237115_("tensura.skill.mode.unyielding.backup_different_dimension").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                     }

                  } else if (backup.m_6084_()) {
                     instance.setCoolDown(instance.isMastered(entity) ? 3 : 5);
                     level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
                     CloneEntity body = this.spawnBackup(instance, entity, true);
                     body.m_7311_(entity.m_20094_());
                     SkillHelper.moveAcrossDimensionTo(entity, backup);
                     backup.copyEquipmentsOntoOwner(entity, true);
                     CloneEntity.copyEffects(backup, entity);
                     entity.m_21153_(backup.m_21223_());
                     backup.remove();
                  }
               } else {
                  if (!SkillHelper.outOfMagicule(entity, maxMP * 0.25D)) {
                     this.spawnBackup(instance, entity, false);
                     instance.setCoolDown(instance.isMastered(entity) ? 3 : 5);
                  }

               }
            }
         }
      }
   }

   private CloneEntity spawnBackup(ManasSkillInstance instance, LivingEntity entity, boolean copyEquipment) {
      Level level = entity.m_9236_();
      CompoundTag tag = instance.getOrCreateTag();
      double EP = TensuraEPCapability.getEP(entity);
      EntityType<CloneEntity> type = entity.m_6144_() ? (EntityType)TensuraEntityTypes.CLONE_SLIM.get() : (EntityType)TensuraEntityTypes.CLONE_DEFAULT.get();
      CloneEntity clone = new CloneEntity(type, level);
      if (entity instanceof Player) {
         Player player = (Player)entity;
         clone.m_21828_(player);
      }

      clone.setSkill(this);
      clone.setImmobile(true);
      clone.setChunkLoader(true);
      clone.m_21153_(entity.m_21223_());
      if (copyEquipment) {
         clone.copyEquipments(entity);
      }

      clone.copyStatsAndSkills(entity, CloneEntity.CopySkill.NONE, true);
      CloneEntity.copyEffects(entity, clone);
      TensuraEPCapability.setLivingEP(clone, EP);
      clone.m_146884_(entity.m_20182_());
      clone.loadChunkHandler();
      CloneEntity.copyRotation(entity, clone);
      level.m_7967_(clone);
      tag.m_128362_("Backup", clone.m_20148_());
      instance.markDirty();
      return clone;
   }

   public void onDeath(ManasSkillInstance instance, LivingDeathEvent event) {
      if (!event.isCanceled()) {
         LivingEntity entity = event.getEntity();
         if (!entity.m_6084_() && !(entity instanceof CloneEntity)) {
            Level var5 = entity.m_9236_();
            if (var5 instanceof ServerLevel) {
               ServerLevel level = (ServerLevel)var5;
               CompoundTag tag = instance.getOrCreateTag();
               if (tag.m_128441_("Backup")) {
                  UUID uuid = tag.m_128342_("Backup");
                  Entity var8 = SkillHelper.getEntityFromUUID(level, uuid, (clonex) -> {
                     return clonex instanceof CloneEntity;
                  });
                  if (var8 instanceof CloneEntity) {
                     CloneEntity backup = (CloneEntity)var8;
                     this.addMasteryPoint(instance, entity);
                     level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
                     entity.f_19802_ = 60;
                     entity.m_21153_(backup.m_21223_());
                     entity.m_7311_(backup.m_20094_());
                     event.setCanceled(true);
                     TensuraEffectsCapability.resetEverything(entity, false, false);
                     TensuraEPCapability.getFrom(entity).ifPresent((cap) -> {
                        if (cap.getEP() <= 0.0D) {
                           cap.setEP(entity, 100.0D, false);
                        } else if (cap.getCurrentEP() <= 0.0D) {
                           cap.setCurrentEP(entity, cap.getEP() * 0.5D);
                        }

                        double SHP = entity.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get());
                        if (cap.getSpiritualHealth() < SHP * 0.5D) {
                           cap.setSpiritualHealth(SHP * 0.5D);
                        }

                     });
                     TensuraEPCapability.sync(entity);
                     CloneEntity clone = this.spawnBackup(instance, entity, true);
                     CloneEntity.copyEffects(entity, clone);
                     boolean keepInv = level.m_46469_().m_46207_(GameRules.f_46133_);
                     if (!keepInv) {
                        clone.m_6667_(event.getSource());
                        clone.remove();
                     }

                     SkillHelper.moveAcrossDimensionTo(entity, backup);
                     backup.copyEquipmentsOntoOwner(entity, true);
                     CloneEntity.copyEffects(backup, entity);
                     backup.remove();
                     if (keepInv) {
                        clone = (CloneEntity)SkillHelper.moveAcrossDimensionTo(clone, entity);
                        if (clone != null) {
                           clone.remove();
                        }
                     }

                  }
               }
            }
         }
      }
   }

   public void onSubordinateDeath(ManasSkillInstance instance, LivingEntity owner, LivingDeathEvent e) {
      LivingEntity entity = e.getEntity();
      if (e.getSource().m_7639_() != owner) {
         if (this.isNamed(owner, entity)) {
            int unyieldingPoint = Math.min(this.getUnyieldingPoint(instance, entity) / 120, 10);
            this.removeUnyielding(instance, entity);
            if (unyieldingPoint >= 1) {
               this.addMasteryPoint(instance, entity, unyieldingPoint);
               TensuraEPCapability.getFrom(entity).ifPresent((cap) -> {
                  double epGain = Math.min(cap.getEP() * 0.1D * (double)unyieldingPoint, (Double)TensuraConfig.INSTANCE.skillsConfig.maximumEPSteal.get());
                  SkillHelper.gainMP(owner, epGain / 2.0D, false);
                  SkillHelper.gainAP(owner, epGain / 2.0D, false);
                  if (owner instanceof Player) {
                     Player player = (Player)owner;
                     player.m_5661_(Component.m_237110_("tensura.ep.acquire_fallen", new Object[]{epGain, entity.m_7755_()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                     player.m_6330_(SoundEvents.f_12275_, SoundSource.PLAYERS, 0.5F, 1.0F);
                  }

                  boolean canGainMax = !(entity instanceof Player) || TensuraGameRules.canEpSteal(entity.m_9236_());
                  if (canGainMax) {
                     SkillHelper.gainMaxMP(owner, epGain / 2.0D);
                     SkillHelper.gainMaxAP(owner, epGain / 2.0D);
                     cap.setEP(entity, cap.getEP() - epGain);
                     cap.setSkipEPDrop(true);
                  }

               });
               if (!entity.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_SKILL_PLUNDER)) {
                  if (!(entity instanceof Player) || entity.m_9236_().m_6106_().m_5466_()) {
                     List<ManasSkillInstance> list = List.copyOf(SkillAPI.getSkillsFrom(entity).getLearnedSkills());
                     if (!list.isEmpty()) {
                        Iterator var7 = list.iterator();

                        while(var7.hasNext()) {
                           ManasSkillInstance targetInstance = (ManasSkillInstance)var7.next();
                           if (this.canGainSkill(targetInstance, unyieldingPoint)) {
                              SkillPlunderEvent event = new SkillPlunderEvent(entity, owner, true, targetInstance.getSkill());
                              if (!MinecraftForge.EVENT_BUS.post(event) && SkillUtils.learnSkill(owner, event.getSkill(), instance.getRemoveTime())) {
                                 SkillAPI.getSkillsFrom(entity).forgetSkill(event.getSkill());
                                 if (owner instanceof Player) {
                                    Player player = (Player)owner;
                                    player.m_5661_(Component.m_237110_("tensura.skill.acquire_fallen", new Object[]{event.getSkill().getName(), entity.m_7755_()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                                    player.m_6330_(SoundEvents.f_12275_, SoundSource.PLAYERS, 0.5F, 1.0F);
                                 }
                              }
                           }
                        }

                     }
                  }
               }
            }
         }
      }
   }

   private int getUnyieldingPoint(ManasSkillInstance instance, LivingEntity target) {
      CompoundTag tag = instance.getOrCreateTag();
      if (!tag.m_128441_("UnyieldingList")) {
         return 0;
      } else {
         CompoundTag list = (CompoundTag)tag.m_128423_("UnyieldingList");
         return list == null ? 0 : list.m_128451_(target.m_20149_());
      }
   }

   private void removeUnyielding(ManasSkillInstance instance, LivingEntity target) {
      CompoundTag tag = instance.getOrCreateTag();
      if (tag.m_128441_("UnyieldingList")) {
         CompoundTag list = (CompoundTag)tag.m_128423_("UnyieldingList");
         if (list == null) {
            return;
         }

         list.m_128473_(target.m_20149_());
      }

      instance.markDirty();
   }

   private boolean isNamed(LivingEntity owner, LivingEntity entity) {
      UUID permanentOwner = TensuraEPCapability.getPermanentOwner(entity);
      if (permanentOwner == null) {
         return false;
      } else if (!Objects.equals(permanentOwner, owner.m_20148_())) {
         return false;
      } else if (TensuraEPCapability.getName(entity) == null) {
         return false;
      } else {
         return !entity.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_EP_PLUNDER);
      }
   }

   private boolean canGainSkill(ManasSkillInstance targetInstance, int point) {
      if (targetInstance.getSkill() == this) {
         return false;
      } else if (!targetInstance.isTemporarySkill() && targetInstance.getMastery() >= 0) {
         ManasSkill var4 = targetInstance.getSkill();
         if (var4 instanceof Skill) {
            Skill skill = (Skill)var4;
            if (skill.getType().equals(Skill.SkillType.UNIQUE)) {
               return point >= 5;
            } else {
               return !skill.getType().equals(Skill.SkillType.ULTIMATE);
            }
         } else {
            return true;
         }
      } else {
         return false;
      }
   }
}
