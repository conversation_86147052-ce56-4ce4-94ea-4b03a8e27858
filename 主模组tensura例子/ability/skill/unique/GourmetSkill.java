package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.ISpatialStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.breath.PredatorMistProjectile;
import com.github.manasmods.tensura.event.SkillPlunderEvent;
import com.github.manasmods.tensura.menu.container.SpatialStorageContainer;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.network.PacketDistributor;
import org.jetbrains.annotations.NotNull;

public class GourmetSkill extends Skill implements ISpatialStorage {
   private static final String CORROSION = "0f805414-5baf-4526-80d7-552e2fc9b078";

   public GourmetSkill() {
      super(Skill.SkillType.UNIQUE);
      this.addHeldAttributeModifier(Attributes.f_22279_, "0f805414-5baf-4526-80d7-552e2fc9b078", -0.5D, Operation.MULTIPLY_TOTAL);
   }

   public double getObtainingEpCost() {
      return 50000.0D;
   }

   public double learningCost() {
      return 2000.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity entity) {
      return true;
   }

   public int modes() {
      return 5;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 5 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 5 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.predator.predation");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.starved.corrosion");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.predator.stomach");
         break;
      case 4:
         var10000 = Component.m_237115_("tensura.skill.mode.starved.receive");
         break;
      case 5:
         var10000 = Component.m_237115_("tensura.skill.mode.starved.provide");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = 200.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      return instance.getMode() == 1 && entity.m_6144_();
   }

   public void addHeldAttributeModifiers(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() == 2) {
         super.addHeldAttributeModifiers(instance, entity);
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      return instance.getMode() == 2 && !instance.onCoolDown() ? this.corrosion(instance, entity, heldTicks) : false;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      CompoundTag tag = instance.getOrCreateTag();
      switch(instance.getMode()) {
      case 1:
         if (entity.m_6144_() && entity instanceof Player) {
            Player player = (Player)entity;
            byte newMode;
            switch(tag.m_128451_("blockMode")) {
            case 1:
               newMode = 2;
               player.m_5661_(Component.m_237110_("tensura.skill.predator.block_mode.blocks", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
               break;
            case 2:
               newMode = 3;
               player.m_5661_(Component.m_237110_("tensura.skill.predator.block_mode.fluid", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
               break;
            case 3:
               newMode = 4;
               player.m_5661_(Component.m_237110_("tensura.skill.predator.block_mode.all", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
               break;
            default:
               newMode = 1;
               player.m_5661_(Component.m_237110_("tensura.skill.predator.block_mode.none", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
            }

            tag.m_128405_("blockMode", newMode);
            instance.markDirty();
         } else {
            PredatorMistProjectile breath = new PredatorMistProjectile(entity.m_9236_(), entity);
            breath.setLength(3.0F);
            breath.setBlockMode(instance.getOrCreateTag().m_128451_("blockMode"));
            if (instance.isMastered(entity)) {
               breath.setConsumeProjectile(true);
            }

            breath.setSkill(instance);
            breath.m_146884_(entity.m_20182_().m_82520_(0.0D, (double)entity.m_20192_() * 0.7D, 0.0D));
            entity.m_9236_().m_7967_(breath);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            this.addMasteryPoint(instance, entity);
            instance.setCoolDown(instance.isMastered(entity) ? 1 : 5);
         }
      case 2:
      default:
         break;
      case 3:
         this.openSpatialStorage(entity, instance);
         break;
      case 4:
         LivingEntity living = SkillHelper.getTargetingEntity(entity, 5.0D, false);
         if (living == null || !living.m_6084_()) {
            return;
         }

         if (!SkillHelper.isSubordinate(entity, living)) {
            return;
         }

         if (living.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_SKILL_PLUNDER)) {
            return;
         }

         List<ManasSkillInstance> collection = SkillAPI.getSkillsFrom(living).getLearnedSkills().stream().filter((sub) -> {
            return StarvedSkill.canGain(sub.getSkill());
         }).toList();
         Iterator var7 = collection.iterator();

         while(var7.hasNext()) {
            ManasSkillInstance targetInstance = (ManasSkillInstance)var7.next();
            if (!targetInstance.isTemporarySkill() && targetInstance.getMastery() >= 0 && targetInstance.getSkill() != this) {
               SkillPlunderEvent event = new SkillPlunderEvent(living, entity, false, targetInstance.getSkill());
               if (!MinecraftForge.EVENT_BUS.post(event) && SkillUtils.learnSkill(entity, event.getSkill(), instance.getRemoveTime())) {
                  if (entity instanceof Player) {
                     Player player = (Player)entity;
                     player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{event.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                  }

                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  ((ServerLevel)level).m_8767_(ParticleTypes.f_175828_, entity.m_20185_(), entity.m_20186_() + (double)entity.m_20206_() / 2.0D, entity.m_20189_(), 20, 0.08D, 0.08D, 0.08D, 0.15D);
               }
            }
         }

         return;
      case 5:
         SkillHelper.comingSoonMessage(entity, "Provide");
      }

   }

   private boolean corrosion(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
         return false;
      } else {
         if (heldTicks % 100 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         Level level = entity.m_9236_();
         level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12031_, SoundSource.PLAYERS, 1.0F, 1.0F);
         if (heldTicks % 10 == 0) {
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
               return entity;
            }), new RequestFxSpawningPacket(new ResourceLocation("tensura:starved_corrosion"), entity.m_19879_(), 0.0D, 1.0D, 0.0D, true));
         }

         if (heldTicks % 10 == 0) {
            List<LivingEntity> list = level.m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(5.0D), (living) -> {
               return !living.m_7306_(entity) && living.m_6084_() && !living.m_7307_(entity);
            });
            if (!list.isEmpty()) {
               Iterator var6 = list.iterator();

               while(true) {
                  CompoundTag tag;
                  double EP;
                  while(true) {
                     LivingEntity target;
                     do {
                        while(true) {
                           do {
                              do {
                                 do {
                                    if (!var6.hasNext()) {
                                       return true;
                                    }

                                    target = (LivingEntity)var6.next();
                                 } while(!target.m_6469_(this.sourceWithMP(TensuraDamageSources.corrosion(entity), entity, instance), 5.0F));
                              } while(!target.m_21224_());

                              if (!target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_SKILL_PLUNDER)) {
                                 List<ManasSkillInstance> targetSkills = SkillAPI.getSkillsFrom(target).getLearnedSkills().stream().filter((skillInstance) -> {
                                    return StarvedSkill.canGain(skillInstance.getSkill());
                                 }).toList();
                                 Iterator var9 = targetSkills.iterator();

                                 while(var9.hasNext()) {
                                    ManasSkillInstance targetInstance = (ManasSkillInstance)var9.next();
                                    if (!targetInstance.isTemporarySkill() && targetInstance.getMastery() >= 0 && targetInstance.getSkill() != this) {
                                       SkillPlunderEvent event = new SkillPlunderEvent(target, entity, false, targetInstance.getSkill());
                                       if (!MinecraftForge.EVENT_BUS.post(event) && SkillUtils.learnSkill(entity, event.getSkill(), instance.getRemoveTime()) && entity instanceof Player) {
                                          Player player = (Player)entity;
                                          player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{event.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                                       }
                                    }
                                 }
                              }
                           } while(instance.isTemporarySkill());

                           tag = instance.getOrCreateTag();
                           CompoundTag predationList;
                           if (tag.m_128441_("predationList")) {
                              predationList = (CompoundTag)tag.m_128423_("predationList");
                              if (predationList == null) {
                                 continue;
                              }

                              String targetID = EntityType.m_20613_(target.m_6095_()).toString();
                              if (predationList.m_128441_(targetID)) {
                                 continue;
                              }

                              predationList.m_128379_(targetID, true);
                              break;
                           }

                           predationList = new CompoundTag();
                           predationList.m_128379_(EntityType.m_20613_(target.m_6095_()).toString(), true);
                           tag.m_128365_("predationList", predationList);
                           break;
                        }
                     } while(target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_EP_PLUNDER));

                     EP = Math.min(SkillUtils.getEPGain(target, entity), (Double)TensuraConfig.INSTANCE.skillsConfig.maximumEPSteal.get());
                     if (target instanceof Player) {
                        Player playerTarget = (Player)target;
                        if (!TensuraGameRules.canEpSteal(target.m_9236_())) {
                           break;
                        }

                        int minEP = TensuraGameRules.getMinEp(level);
                        if (minEP > 0) {
                           EP -= (double)minEP;
                        }

                        if (EP <= 0.0D) {
                           continue;
                        }

                        SkillHelper.gainMaxMP(entity, EP / 4.0D);
                        SkillHelper.gainMaxAP(entity, EP / 4.0D);
                        TensuraEPCapability.setSkippingEPDrop(target, true);
                        SkillHelper.gainMP(entity, EP / 4.0D, false);
                        SkillHelper.gainAP(entity, EP / 4.0D, false);
                        TensuraPlayerCapability.getFrom(playerTarget).ifPresent((cap) -> {
                           cap.setBaseMagicule((double)minEP / 2.0D, playerTarget);
                           cap.setBaseAura((double)minEP / 2.0D, playerTarget);
                        });
                        TensuraPlayerCapability.sync(playerTarget);
                        break;
                     }

                     SkillHelper.gainMaxMP(entity, EP / 4.0D);
                     SkillHelper.gainMaxAP(entity, EP / 4.0D);
                     TensuraEPCapability.setSkippingEPDrop(target, true);
                     SkillHelper.gainMP(entity, EP / 4.0D, false);
                     SkillHelper.gainAP(entity, EP / 4.0D, false);
                     SkillHelper.reduceEP(target, entity, 1.0D, true, true);
                     break;
                  }

                  tag.m_128347_("storedMP", tag.m_128459_("storedMP") + EP / 4.0D);
                  tag.m_128347_("storedAP", tag.m_128459_("storedAP") + EP / 4.0D);
                  instance.markDirty();
               }
            }
         }

         return true;
      }
   }

   @NotNull
   public SpatialStorageContainer getSpatialStorage(ManasSkillInstance instance) {
      SpatialStorageContainer container = new SpatialStorageContainer(63, 200);
      container.m_7797_(instance.getOrCreateTag().m_128437_("SpatialStorage", 10));
      return container;
   }
}
