package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.intrinsic.CharmSkill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.event.entity.living.LivingHurtEvent;
import net.minecraftforge.network.PacketDistributor;

public class SlothSkill extends Skill {
   public SlothSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 100000.0D;
   }

   public int getMaxMastery() {
      return 1500;
   }

   public double learningCost() {
      return 5000.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public int modes() {
      return 6;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      int var10000;
      if (reverse) {
         switch(instance.getMode()) {
         case 1:
            var10000 = instance.isMastered(entity) ? 6 : 5;
            break;
         case 2:
            var10000 = 1;
            break;
         case 3:
            var10000 = 2;
            break;
         case 4:
            var10000 = 3;
            break;
         case 5:
            var10000 = 4;
            break;
         case 6:
            var10000 = 5;
            break;
         default:
            var10000 = 0;
         }

         return var10000;
      } else {
         switch(instance.getMode()) {
         case 1:
            var10000 = 2;
            break;
         case 2:
            var10000 = 3;
            break;
         case 3:
            var10000 = 4;
            break;
         case 4:
            var10000 = 5;
            break;
         case 5:
            var10000 = instance.isMastered(entity) ? 6 : 1;
            break;
         default:
            var10000 = 1;
         }

         return var10000;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.sloth.deep_hypno");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.sloth.fallen_hypno");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.sloth.deprive");
         break;
      case 4:
         var10000 = Component.m_237115_("tensura.skill.mode.sloth.rest");
         break;
      case 5:
         var10000 = Component.m_237115_("tensura.skill.mode.sloth.phantasmal_strike");
         break;
      case 6:
         var10000 = Component.m_237115_("tensura.skill.mode.sloth.fallen_strike");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 3:
         var10000 = 100.0D;
         break;
      case 6:
         var10000 = 5000.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public String modeLearningId(int mode) {
      String var10000;
      switch(mode) {
      case 5:
         var10000 = "phantasmalStrike";
         break;
      case 6:
         var10000 = "fallenStrike";
         break;
      default:
         var10000 = "None";
      }

      return var10000;
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMastery() < 0) {
         return false;
      } else {
         return instance.getMode() != 5 && instance.getMode() != 6;
      }
   }

   public boolean canInteractSkill(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_21023_((MobEffect)TensuraMobEffects.SLEEP_MODE.get())) {
         return false;
      } else if (entity.m_21023_((MobEffect)TensuraMobEffects.INFINITE_IMPRISONMENT.get())) {
         return false;
      } else {
         return entity.m_21023_((MobEffect)TensuraMobEffects.REST.get()) ? true : super.canInteractSkill(instance, entity);
      }
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity entity, LivingHurtEvent event) {
      if (instance.isToggled()) {
         if (instance.getOrCreateTag().m_128451_("phantasmalStrike") >= 100) {
            if (DamageSourceHelper.isPhysicalAttack(event.getSource())) {
               LivingEntity target = event.getEntity();
               double bonus = instance.isMastered(entity) ? target.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get()) * 0.01D : 0.0D;
               DamageSourceHelper.directSpiritualHurt(target, entity, 10.0F + (float)bonus);
               if (!target.m_6084_()) {
                  event.setCanceled(true);
               }
            }

         }
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() != 5 && instance.getMode() != 6) {
         if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
            return false;
         } else {
            Level level = entity.m_9236_();
            Player player;
            LivingEntity target;
            Player player;
            float healSHP;
            switch(instance.getMode()) {
            case 1:
               LivingEntity target = SkillHelper.getTargetingEntity(entity, 10.0D, false);
               if (target != null) {
                  if (target instanceof Player) {
                     player = (Player)target;
                     if (player.m_150110_().f_35934_) {
                        return false;
                     }
                  }

                  if (heldTicks % 100 == 0 && heldTicks > 0) {
                     this.addMasteryPoint(instance, entity);
                  }

                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12268_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  int i = 0;
                  int originalDuration = instance.isMastered(entity) ? 200 : 100;
                  MobEffectInstance drowsiness = target.m_21124_((MobEffect)TensuraMobEffects.DROWSINESS.get());
                  int duration;
                  if (drowsiness != null && heldTicks > 0) {
                     duration = drowsiness.m_19557_() + 2;
                     i = (duration - originalDuration) / (200 + 100 * MobEffectHelper.getSpiritualResistLevel(target));
                  } else {
                     duration = originalDuration;
                  }

                  SkillHelper.checkThenAddEffectSource(target, entity, (MobEffect)TensuraMobEffects.DROWSINESS.get(), duration, i, false, false, false, true);
               }
               break;
            case 2:
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_215771_, SoundSource.PLAYERS, 1.0F, 1.0F);
               if (heldTicks % 10 == 0) {
                  TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
                     return entity;
                  }), new RequestFxSpawningPacket(new ResourceLocation("tensura:fallen_hypno"), entity.m_19879_(), 0.0D, 1.0D, 0.0D, true));
               }

               healSHP = instance.isMastered(entity) ? 10.0F : 5.0F;
               List<LivingEntity> list = level.m_6443_(LivingEntity.class, entity.m_20191_().m_82400_((double)healSHP), (living) -> {
                  return !living.m_7306_(entity) && living.m_6084_() && !living.m_7307_(entity);
               });
               if (!list.isEmpty()) {
                  if (heldTicks % 100 == 0 && heldTicks > 0) {
                     this.addMasteryPoint(instance, entity);
                  }

                  Iterator var7 = list.iterator();

                  while(true) {
                     do {
                        if (!var7.hasNext()) {
                           return true;
                        }

                        target = (LivingEntity)var7.next();
                        if (!(target instanceof Player)) {
                           break;
                        }

                        player = (Player)target;
                     } while(player.m_150110_().f_35934_);

                     int i = 0;
                     int originalDuration = instance.isMastered(entity) ? 200 : 100;
                     MobEffectInstance drowsiness = target.m_21124_((MobEffect)TensuraMobEffects.DROWSINESS.get());
                     int duration;
                     if (drowsiness != null && heldTicks > 0) {
                        duration = drowsiness.m_19557_() + 2;
                        i = (duration - originalDuration) / (400 + 100 * MobEffectHelper.getSpiritualResistLevel(target));
                     } else {
                        duration = originalDuration;
                     }

                     SkillHelper.checkThenAddEffectSource(target, entity, (MobEffect)TensuraMobEffects.DROWSINESS.get(), duration, i, false, false, false, true);
                  }
               }
               break;
            case 3:
               boolean mastered = instance.isMastered(entity);
               double radius = mastered ? 15.0D : 5.0D;
               target = SkillHelper.getTargetingEntity(entity, radius, false);
               if (target != null && heldTicks % 20 == 0) {
                  if (target instanceof Player) {
                     player = (Player)target;
                     if (player.m_150110_().f_35934_) {
                        return false;
                     }
                  }

                  if (heldTicks > 0 && SkillHelper.drainMP(target, entity, 1000.0D, false)) {
                     if (mastered) {
                        SkillHelper.drainMP(target, entity, 0.003D, true);
                     }

                     double bonus = mastered ? target.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get()) * 0.01D : 0.0D;
                     DamageSourceHelper.directSpiritualHurt(target, entity, 10.0F + (float)bonus);
                     level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12053_, SoundSource.PLAYERS, 1.0F, 1.0F);
                     if (heldTicks % 200 == 0) {
                        this.addMasteryPoint(instance, entity);
                     }

                     if (target.m_6084_() && TensuraEPCapability.getSpiritualHealth(target) > 0.0D && TensuraEPCapability.getSpiritualHealth(target) < (double)(target.m_21233_() / 2.0F) && CharmSkill.canMindControl(target, level)) {
                        UUID uuid = entity.m_20148_();
                        TensuraEPCapability.getFrom(target).ifPresent((cap) -> {
                           if (!Objects.equals(cap.getTemporaryOwner(), uuid)) {
                              cap.setTemporaryOwner(uuid);
                              if (entity instanceof Player) {
                                 Player player = (Player)entity;
                                 if (target instanceof TamableAnimal) {
                                    TamableAnimal animal = (TamableAnimal)target;
                                    animal.m_21828_(player);
                                 } else if (target instanceof TensuraHorseEntity) {
                                    TensuraHorseEntity horse = (TensuraHorseEntity)target;
                                    horse.m_30637_(player);
                                 }
                              }

                              TensuraEPCapability.sync(target);
                              entity.m_21011_(InteractionHand.MAIN_HAND, true);
                              TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123750_);
                              level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 0.5F, 1.0F);
                           }

                        });
                     }
                  }
               }
               break;
            case 4:
               if (entity instanceof Player) {
                  Player player = (Player)entity;
                  if (player.m_36341_()) {
                     return false;
                  }
               }

               entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.REST.get(), 5, 0, false, false, false));
               if (heldTicks % 20 == 0) {
                  entity.m_5634_(instance.isMastered(entity) ? 10.0F + entity.m_21233_() * 0.01F : 10.0F);
                  healSHP = instance.isMastered(entity) ? 10.0F + (float)(entity.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get()) * 0.009999999776482582D) : 10.0F;
                  TensuraEPCapability.healSpiritualHealth(entity, (double)healSHP);
                  if (entity instanceof Player) {
                     player = (Player)entity;
                     TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                        double maxMP = player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get());
                        if (cap.getMagicule() >= maxMP) {
                           double mana = SkillHelper.mpRegen(player, maxMP, 2.0D);
                           CompoundTag tag = instance.getOrCreateTag();
                           tag.m_128347_("storedMagicule", tag.m_128459_("storedMagicule") + mana);
                           instance.markDirty();
                        }

                     });
                  }
               }
            }

            return true;
         }
      } else {
         return false;
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      CompoundTag tag = instance.getOrCreateTag();
      int learnPoint;
      Player player;
      switch(instance.getMode()) {
      case 4:
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (!player.m_36341_()) {
               return;
            }
         }

         List<LivingEntity> list = entity.m_9236_().m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(15.0D), (living) -> {
            return !living.m_7306_(entity) && living.m_6084_() && living.m_7307_(entity);
         });
         if (list.isEmpty()) {
            return;
         }

         if (this.outOfStoredMP(entity, instance, (double)(1000 * list.size()))) {
            return;
         }

         Iterator var12 = list.iterator();

         while(var12.hasNext()) {
            LivingEntity ally = (LivingEntity)var12.next();
            ally.m_5634_(50.0F);
            TensuraEPCapability.healSpiritualHealth(ally, 10.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(ally, ParticleTypes.f_123750_, 1.0D);
            if (ally instanceof Player) {
               Player player = (Player)ally;
               TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                  double newAura;
                  if (cap.getMagicule() != player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get())) {
                     newAura = cap.getMagicule() + 1000.0D;
                     cap.setMagicule(Math.min(newAura, player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get())));
                  }

                  if (cap.getAura() != player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_AURA.get())) {
                     newAura = cap.getAura() + 1000.0D;
                     cap.setAura(Math.min(newAura, player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_AURA.get())));
                  }

               });
               TensuraPlayerCapability.sync(player);
            }
         }

         return;
      case 5:
         learnPoint = tag.m_128451_("phantasmalStrike");
         if (learnPoint < 100) {
            tag.m_128405_("phantasmalStrike", learnPoint + SkillUtils.getEarningLearnPoint(instance, entity, true));
            if (entity instanceof Player) {
               player = (Player)entity;
               if (tag.m_128451_("phantasmalStrike") >= 100) {
                  player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{this.getModeName(5)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
               } else {
                  instance.setCoolDown(10);
                  SkillUtils.learningFailPenalty(entity);
                  player.m_5661_(Component.m_237110_("tensura.skill.learn_points_added", new Object[]{this.getModeName(5)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
               }

               player.m_6330_(SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }

            instance.markDirty();
         }
         break;
      case 6:
         if (SkillHelper.outOfMagicule(entity, instance)) {
            return;
         }

         learnPoint = tag.m_128451_("fallenStrike");
         if (learnPoint < 100) {
            tag.m_128405_("fallenStrike", learnPoint + SkillUtils.getEarningLearnPoint(instance, entity, true));
            if (entity instanceof Player) {
               player = (Player)entity;
               if (tag.m_128451_("fallenStrike") >= 100) {
                  player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{this.getModeName(6)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
               } else {
                  instance.setCoolDown(10);
                  SkillUtils.learningFailPenalty(entity);
                  player.m_5661_(Component.m_237110_("tensura.skill.learn_points_added", new Object[]{this.getModeName(6)}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
               }

               player.m_6330_(SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }

            instance.markDirty();
            return;
         }

         LivingEntity target = SkillHelper.getTargetingEntity(entity, 8.0D, false);
         if (target == null) {
            return;
         }

         if (target instanceof Player) {
            Player player = (Player)target;
            if (player.m_150110_().f_35934_) {
               return;
            }
         }

         DamageSourceHelper.directSpiritualHurt(target, entity, 500.0F, 0.0F);
         instance.setCoolDown(10);
         level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11913_, SoundSource.PLAYERS, 1.0F, 1.0F);
         TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123747_, 1.0D);
         TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123765_, 1.0D);
      }

   }

   private boolean outOfStoredMP(LivingEntity entity, ManasSkillInstance skillInstance, double cost) {
      CompoundTag tag = skillInstance.getOrCreateTag();
      double newStored = tag.m_128459_("storedMagicule") - cost;
      if (newStored >= 0.0D) {
         tag.m_128347_("storedMagicule", newStored);
         skillInstance.markDirty();
         entity.f_19853_.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
         return false;
      } else {
         entity.f_19853_.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 1.0F, 1.0F);
         return true;
      }
   }
}
