package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.entity.magic.skill.SniperGrenadeProjectile;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.Objects;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraftforge.event.entity.ProjectileImpactEvent;
import net.minecraftforge.event.entity.living.LivingAttackEvent;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class SniperSkill extends Skill {
   public SniperSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 25000.0D;
   }

   public double learningCost() {
      return 200.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.sniper.weapon");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.sniper.spatial");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = 100.0D;
         break;
      case 2:
         var10000 = 200.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      if (SkillHelper.outOfMagicule(entity, 25.0D)) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.lack_magicule.toggled_off", new Object[]{instance.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
         }

         instance.setToggled(false);
      } else {
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get(), 200, 1, false, false, false));
      }
   }

   public void onBeingDamaged(ManasSkillInstance instance, LivingAttackEvent event) {
      if (!event.isCanceled()) {
         LivingEntity entity = event.getEntity();
         if (this.isInSlot(entity)) {
            DamageSource damageSource = event.getSource();
            if (!damageSource.m_19378_() && !damageSource.m_19387_()) {
               if (damageSource.m_7640_() != null && damageSource.m_7640_() == damageSource.m_7639_()) {
                  if (!(entity.m_217043_().m_188501_() > 0.25F)) {
                     entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
                     event.setCanceled(true);
                     if (SkillUtils.canNegateDodge(entity, damageSource)) {
                        event.setCanceled(false);
                     }

                  }
               }
            }
         }
      }
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity living, LivingHurtEvent e) {
      if (instance.isToggled()) {
         if (!SkillUtils.isSkillToggled(living, (ManasSkill)ExtraSkills.SPATIAL_DOMINATION.get())) {
            if (!SkillUtils.isSkillToggled(living, (ManasSkill)ExtraSkills.SPATIAL_MANIPULATION.get())) {
               if (DamageSourceHelper.isSpatialDamage(e.getSource())) {
                  e.setAmount(e.getAmount() * 2.0F);
               }

            }
         }
      }
   }

   public void onProjectileHit(ManasSkillInstance instance, LivingEntity entity, ProjectileImpactEvent event) {
      if (this.isInSlot(entity)) {
         if (!SkillUtils.isProjectileAlwaysHit(event.getProjectile())) {
            if (!(entity.m_217043_().m_188501_() > 0.25F)) {
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
               event.setCanceled(true);
            }
         }
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      if (instance.getMode() == 1) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            boolean hadPistol = entity.m_21206_().m_150930_((Item)TensuraToolItems.SNIPER_PISTOL.get()) || entity.m_21205_().m_150930_((Item)TensuraToolItems.SNIPER_PISTOL.get());
            if (!hadPistol && SkillHelper.getTargetingEntity(entity, 30.0D, 0.0D, false, false) == null) {
               Player player;
               if (entity.m_21205_().m_41619_()) {
                  if (entity instanceof Player) {
                     player = (Player)entity;
                     player.m_36335_().m_41524_((Item)TensuraToolItems.SNIPER_PISTOL.get(), 10);
                  }

                  entity.m_21008_(InteractionHand.MAIN_HAND, new ItemStack((ItemLike)TensuraToolItems.SNIPER_PISTOL.get()));
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
               } else if (entity.m_21206_().m_41619_()) {
                  if (entity instanceof Player) {
                     player = (Player)entity;
                     player.m_36335_().m_41524_((Item)TensuraToolItems.SNIPER_PISTOL.get(), 10);
                  }

                  entity.m_21008_(InteractionHand.OFF_HAND, new ItemStack((ItemLike)TensuraToolItems.SNIPER_PISTOL.get()));
                  entity.m_21011_(InteractionHand.OFF_HAND, true);
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }

            } else {
               this.addMasteryPoint(instance, entity);
               instance.setCoolDown(instance.isMastered(entity) ? 1 : 3);
               SniperGrenadeProjectile grenade = new SniperGrenadeProjectile(entity.m_9236_(), entity);
               grenade.setSpeed(2.0F);
               grenade.setExplosionRadius(4.0F);
               grenade.setMpCost(this.magiculeCost(entity, instance));
               grenade.setSkill(instance);
               grenade.setPosAndShoot(entity);
               level.m_7967_(grenade);
               entity.m_21011_(InteractionHand.MAIN_HAND, true);
            }
         }
      }
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      this.onTick(instance, entity);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_21023_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get())) {
         int level = ((MobEffectInstance)Objects.requireNonNull(entity.m_21124_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get()))).m_19564_();
         if (level == 1) {
            entity.m_21195_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get());
         }
      }

   }
}
