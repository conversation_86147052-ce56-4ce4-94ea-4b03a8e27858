package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.menu.SkillCreatorMenu;
import java.util.List;
import java.util.Optional;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.SimpleMenuProvider;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.network.NetworkHooks;

public class CreatorSkill extends Skill {
   public CreatorSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 75000.0D;
   }

   public double learningCost() {
      return 1000.0D;
   }

   protected boolean canActivateInRaceLimit(ManasSkillInstance instance) {
      return instance.getMode() == 1;
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.creator.analytical_appraisal");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.creator.skill_creation");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      switch(instance.getMode()) {
      case 1:
         if (!(entity instanceof Player)) {
            return;
         }

         Player player = (Player)entity;
         TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
            int level;
            if (player.m_6047_()) {
               level = cap.getAnalysisMode();
               switch(level) {
               case 1:
                  cap.setAnalysisMode(2);
                  player.m_5661_(Component.m_237115_("tensura.skill.analytical.analyzing_mode.block").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
                  break;
               case 2:
                  cap.setAnalysisMode(0);
                  player.m_5661_(Component.m_237115_("tensura.skill.analytical.analyzing_mode.both").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
                  break;
               default:
                  cap.setAnalysisMode(1);
                  player.m_5661_(Component.m_237115_("tensura.skill.analytical.analyzing_mode.entity").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
               }

               player.m_6330_(SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
               TensuraSkillCapability.sync(player);
            } else {
               level = instance.isMastered(entity) ? 6 : 2;
               if (cap.getAnalysisLevel() != level) {
                  cap.setAnalysisLevel(level);
                  cap.setAnalysisDistance(instance.isMastered(entity) ? 10 : 5);
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
               } else {
                  cap.setAnalysisLevel(0);
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }

               TensuraSkillCapability.sync(player);
            }
         });
         break;
      case 2:
         if (entity instanceof ServerPlayer) {
            ServerPlayer serverPlayer = (ServerPlayer)entity;
            List<ResourceLocation> list = ((List)TensuraConfig.INSTANCE.skillsConfig.skillCreatorSkills.get()).stream().map(ResourceLocation::new).filter((location) -> {
               return this.canCreateSkill(location, serverPlayer, instance);
            }).toList();
            NetworkHooks.openScreen(serverPlayer, new SimpleMenuProvider(SkillCreatorMenu::new, Component.m_237119_()), (buf) -> {
               buf.m_236828_(list, FriendlyByteBuf::m_130085_);
            });
            serverPlayer.m_6330_(SoundEvents.f_11889_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }
      }

   }

   private boolean canCreateSkill(ResourceLocation location, ServerPlayer serverPlayer, ManasSkillInstance instance) {
      ManasSkill skill = (ManasSkill)SkillAPI.getSkillRegistry().getValue(location);
      if (skill == null) {
         return false;
      } else {
         Optional<ManasSkillInstance> optional = SkillAPI.getSkillsFrom(serverPlayer).getSkill(skill);
         if (instance.isMastered(serverPlayer)) {
            return (Boolean)optional.map(ManasSkillInstance::isTemporarySkill).orElse(true);
         } else {
            CompoundTag tag = instance.getTag();
            if (tag != null && tag.m_128441_("created_skill")) {
               ResourceLocation created = new ResourceLocation(tag.m_128461_("created_skill"));
               if (created.equals(location)) {
                  return false;
               }
            }

            return optional.isEmpty();
         }
      }
   }
}
