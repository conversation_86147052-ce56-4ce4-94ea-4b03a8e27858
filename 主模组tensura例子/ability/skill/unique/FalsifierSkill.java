package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.entity.human.CloneEntity;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.ClientboundMainScreenOpenPacket;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import java.util.Iterator;
import java.util.UUID;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.Level;
import net.minecraftforge.event.entity.living.LivingDamageEvent;
import net.minecraftforge.network.PacketDistributor;

public class FalsifierSkill extends Skill {
   protected static final String FAKE = "c4e41d0e-80d1-4dc9-bf63-db4abe99498b";

   public FalsifierSkill() {
      super(Skill.SkillType.UNIQUE);
      this.addHeldAttributeModifier(Attributes.f_22279_, "c4e41d0e-80d1-4dc9-bf63-db4abe99498b", -0.4000000059604645D, Operation.MULTIPLY_TOTAL);
   }

   public double getObtainingEpCost() {
      return 15000.0D;
   }

   public double learningCost() {
      return 2000.0D;
   }

   public int modes() {
      return 3;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 3 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 3 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.falsifier.concealment");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.falsifier.illusion");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.falsifier.fake_death");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 200.0D;
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      return entity.m_21023_((MobEffect)TensuraMobEffects.FALSIFIER.get());
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_21023_((MobEffect)TensuraMobEffects.FALSIFIER.get())) {
         entity.m_21195_((MobEffect)TensuraMobEffects.FALSIFIER.get());
      }

   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      switch(instance.getMode()) {
      case 1:
         if (entity.m_21023_((MobEffect)TensuraMobEffects.FALSIFIER.get())) {
            instance.setCoolDown(this.isMastered(instance, entity) ? 0 : 20);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11824_, SoundSource.PLAYERS, 1.0F, 1.0F);
            entity.m_21195_((MobEffect)TensuraMobEffects.FALSIFIER.get());
         } else {
            if (SkillHelper.outOfMagicule(entity, instance)) {
               return;
            }

            this.addMasteryPoint(instance, entity);
            instance.setCoolDown(this.isMastered(instance, entity) ? 120 : 140);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11767_, SoundSource.PLAYERS, 1.0F, 1.0F);
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FALSIFIER.get(), 2400, 0, false, false, false));
         }
         break;
      case 2:
         if (!(entity instanceof ServerPlayer)) {
            return;
         }

         ServerPlayer player = (ServerPlayer)entity;
         TensuraNetwork.INSTANCE.send(PacketDistributor.PLAYER.with(() -> {
            return player;
         }), new ClientboundMainScreenOpenPacket(9, player.m_19879_()));
         player.m_6330_(SoundEvents.f_11889_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }

   }

   public void onTakenDamage(ManasSkillInstance instance, LivingDamageEvent e) {
      if (instance.getMode() == 3) {
         LivingEntity entity = e.getEntity();
         if (isFakingDeath(entity)) {
            DamageSource source = e.getSource();
            if (!source.m_19378_()) {
               if (source instanceof TensuraDamageSource) {
                  TensuraDamageSource damageSource = (TensuraDamageSource)source;
                  if (damageSource.getIgnoreBarrier() >= 2.0F) {
                     return;
                  }
               }

               Level level = entity.m_9236_();
               EntityType<CloneEntity> type = entity.m_6144_() ? (EntityType)TensuraEntityTypes.CLONE_SLIM.get() : (EntityType)TensuraEntityTypes.CLONE_DEFAULT.get();
               CloneEntity clone = new CloneEntity(type, level);
               clone.setImmobile(true);
               if (entity instanceof Player) {
                  Player player = (Player)entity;
                  clone.m_21828_(player);
               }

               clone.setSkill(this);
               clone.copyStatsAndSkills(entity, CloneEntity.CopySkill.NONE, true);
               clone.m_21153_(entity.m_21223_());
               clone.m_146884_(entity.m_20182_());
               CloneEntity.copyRotation(entity, clone);
               level.m_7967_(clone);
               e.setAmount(e.getAmount() / 2.0F);
               clone.m_6469_(source.m_19381_(), clone.m_21233_() * 10.0F);
               entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FALSIFIER.get(), 60, 0, false, false, false));
               if (level.m_46469_().m_46207_(GameRules.f_46142_)) {
                  clone.m_21231_().m_19289_(source.m_19381_(), 1.0F, 1.0F);
                  Component deathMessage = clone.m_21231_().m_19293_();
                  Iterator var9 = level.m_6907_().iterator();

                  while(var9.hasNext()) {
                     Player everyone = (Player)var9.next();
                     if (everyone != entity) {
                        everyone.m_213846_(deathMessage);
                     }
                  }
               }

            }
         }
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() != 3) {
         return false;
      } else {
         if (entity.m_21023_((MobEffect)TensuraMobEffects.FALSIFIER.get())) {
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FALSIFIER.get(), 20, 0, false, false, false));
         }

         return heldTicks <= 600;
      }
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() == 3 && this.isHeld(entity)) {
         if (entity.m_21023_((MobEffect)TensuraMobEffects.FALSIFIER.get())) {
            instance.setCoolDown(this.isMastered(instance, entity) ? 5 : 10);
         }

      }
   }

   public double getAttributeModifierAmplifier(ManasSkillInstance instance, LivingEntity entity, AttributeModifier modifier) {
      return modifier.m_22218_() * (instance.isMastered(entity) ? 0.5D : 1.0D);
   }

   public void addHeldAttributeModifiers(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() == 3) {
         super.addHeldAttributeModifiers(instance, entity);
      }
   }

   public static boolean isFakingDeath(LivingEntity entity) {
      AttributeInstance attributeInstance = entity.m_21051_(Attributes.f_22279_);
      if (attributeInstance == null) {
         return false;
      } else {
         return attributeInstance.m_22111_(UUID.fromString("c4e41d0e-80d1-4dc9-bf63-db4abe99498b")) != null;
      }
   }
}
