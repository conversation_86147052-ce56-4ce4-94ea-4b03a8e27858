package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Block;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;

public class EngorgerSkill extends Skill {
   public EngorgerSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 30000.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.ENGORGEMENT.get(), 240, 0, false, false, false));
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      this.onTick(instance, entity);
      TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_175826_, 1.0D);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_21195_((MobEffect)TensuraMobEffects.ENGORGEMENT.get());
      TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_175826_, 1.0D);
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!instance.isToggled()) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.mode.need_toggle_on", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
         }

         if (!entity.m_6144_()) {
            return;
         }

         instance.setToggled(true);
         instance.onToggleOn(entity);
      }

      MobEffectInstance effectInstance = entity.m_21124_((MobEffect)TensuraMobEffects.ENGORGEMENT.get());
      if (effectInstance != null && effectInstance.m_19564_() != 0) {
         if (!entity.m_20096_() && !entity.m_20072_()) {
            return;
         }

         if (SkillHelper.outOfMagicule(entity, 150.0D)) {
            return;
         }

         this.addMasteryPoint(instance, entity);
         Level level = entity.m_9236_();
         double dist = entity.m_6047_() ? 7.5D : 15.0D;
         BlockHitResult result = SkillHelper.getPlayerPOVHitResultFromPos(level, entity, Fluid.NONE, Block.COLLIDER, dist, entity.m_20182_().m_82520_(0.0D, (double)(entity.m_20206_() / 2.0F), 0.0D));
         Vec3 source = entity.m_20182_().m_82520_(0.0D, (double)(entity.m_20206_() / 2.0F), 0.0D);
         Vec3 offSetToTarget = Vec3.m_82512_(result.m_82425_()).m_82546_(source);

         for(int particleIndex = 1; particleIndex < Mth.m_14107_(offSetToTarget.m_82553_()); ++particleIndex) {
            Vec3 particlePos = source.m_82549_(offSetToTarget.m_82541_().m_82490_((double)particleIndex));
            ((ServerLevel)level).m_8767_(ParticleTypes.f_175826_, particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_, 1, 0.0D, 0.0D, 0.0D, 0.0D);
            AABB aabb = (new AABB(new BlockPos(particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_))).m_82400_(1.0D);
            List<LivingEntity> livingEntityList = level.m_6443_(LivingEntity.class, aabb, (entityData) -> {
               return !entityData.m_7306_(entity);
            });
            if (!livingEntityList.isEmpty()) {
               float bonus = instance.isMastered(entity) ? 100.0F : 50.0F;
               float amount = (float)(entity.m_21133_(Attributes.f_22281_) + (double)bonus);

               for(Iterator var16 = livingEntityList.iterator(); var16.hasNext(); ((ServerLevel)level).m_8767_(ParticleTypes.f_123813_, particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_, 1, 0.0D, 0.0D, 0.0D, 0.0D)) {
                  LivingEntity pLivingEntity = (LivingEntity)var16.next();
                  if (pLivingEntity.m_6469_(this.sourceWithMP(DamageSource.m_19370_(entity), entity, instance), amount)) {
                     ItemStack stack = entity.m_21205_();
                     stack.m_41720_().m_7579_(stack, pLivingEntity, entity);
                     entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  }
               }
            }
         }

         entity.m_183634_();
         SkillHelper.riptidePush(entity, 3.0F);
         entity.f_19864_ = true;
      } else {
         if (SkillHelper.outOfMagicule(entity, 300.0D)) {
            return;
         }

         this.addMasteryPoint(instance, entity);
         TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_175826_, 1.0D);
         entity.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.ENGORGEMENT.get(), 2400, 1, false, false, false), entity);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12054_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }

   }
}
