package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.extra.SpatialMotionSkill;
import com.github.manasmods.tensura.ability.skill.extra.ThoughtAccelerationSkill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.event.ForcedTeleportationEvent;
import com.github.manasmods.tensura.menu.SpatialMenu;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.SimpleMenuProvider;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.EntityTeleportEvent;
import net.minecraftforge.network.NetworkHooks;

public class SuppressorSkill extends Skill {
   protected static final UUID ACCELERATION = UUID.fromString("48a488be-c57d-4dce-a166-08c0e9a27d6c");

   public SuppressorSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 20000.0D;
   }

   public double learningCost() {
      return 500.0D;
   }

   public int modes() {
      return 3;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      if (reverse) {
         return instance.getMode() == 1 ? 3 : instance.getMode() - 1;
      } else {
         return instance.getMode() == 3 ? 1 : instance.getMode() + 1;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.suppressor.blockade");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.suppressor.swap");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.suppressor.motion");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = 300.0D;
         break;
      case 2:
         var10000 = 100.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return instance.getMastery() >= 0;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      if (!SkillHelper.outOfMagicule(entity, instance)) {
         Player player;
         switch(instance.getMode()) {
         case 1:
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
            List<LivingEntity> list = level.m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(25.0D), (living) -> {
               return !living.m_7306_(living) && living.m_6084_() && !living.m_7307_(living);
            });
            if (list.isEmpty()) {
               return;
            }

            Iterator var14 = list.iterator();

            while(var14.hasNext()) {
               LivingEntity target = (LivingEntity)var14.next();
               target.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.SPATIAL_BLOCKADE.get(), 1200, 0, false, false, true), entity);
               TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123760_, 1.0D);
            }

            return;
         case 2:
            if (MobEffectHelper.noTeleportation(entity)) {
               if (entity instanceof Player) {
                  player = (Player)entity;
                  player.m_5661_(Component.m_237115_("tensura.skill.spatial_blockade").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
               }

               return;
            }

            Entity target = SkillHelper.getTargetingEntity(entity, 30.0D, 0.2D, false, false);
            if (target == null) {
               return;
            }

            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            Vec3 targetPos = new Vec3(target.m_20182_().m_7096_(), target.m_20182_().m_7098_(), target.m_20182_().m_7094_());
            Vec3 userPos = new Vec3(entity.m_20182_().m_7096_(), entity.m_20182_().m_7098_(), entity.m_20182_().m_7094_());
            ForcedTeleportationEvent event = new ForcedTeleportationEvent(target, entity, entity.m_20182_().m_7096_(), entity.m_20182_().m_7098_(), entity.m_20182_().m_7094_());
            if (MinecraftForge.EVENT_BUS.post(event)) {
               return;
            }

            EntityTeleportEvent teleportEvent = new EntityTeleportEvent(entity, target.m_20182_().m_7096_(), target.m_20182_().m_7098_(), target.m_20182_().m_7094_());
            if (MinecraftForge.EVENT_BUS.post(teleportEvent)) {
               return;
            }

            target.m_183634_();
            target.m_19877_();
            target.m_146884_(userPos);
            target.f_19812_ = true;
            target.f_19864_ = true;
            entity.m_183634_();
            entity.m_19877_();
            entity.m_20219_(targetPos);
            entity.f_19812_ = true;
            entity.f_19864_ = true;
            this.addMasteryPoint(instance, entity);
            TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123760_, 1.0D);
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123760_, 1.0D);
            level.m_6263_((Player)null, target.m_20185_(), target.m_20186_(), target.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
            break;
         case 3:
            if (MobEffectHelper.noTeleportation(entity)) {
               if (entity instanceof Player) {
                  player = (Player)entity;
                  player.m_5661_(Component.m_237115_("tensura.skill.spatial_blockade").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
               }

               return;
            }

            if (entity.m_6144_()) {
               if (entity instanceof ServerPlayer) {
                  ServerPlayer serverPlayer = (ServerPlayer)entity;
                  if (level.m_46472_() == TensuraDimensions.LABYRINTH) {
                     serverPlayer.m_6330_(SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
                     serverPlayer.m_5661_(Component.m_237115_("tensura.ability.activation_failed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
                  } else {
                     NetworkHooks.openScreen(serverPlayer, new SimpleMenuProvider(SpatialMenu::new, Component.m_237119_()), (buf) -> {
                        buf.writeBoolean(false);
                     });
                     serverPlayer.m_6330_(SoundEvents.f_11889_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  }
               }

               entity.m_21195_((MobEffect)TensuraMobEffects.WARPING.get());
               return;
            }

            BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, this.isMastered(instance, entity) ? 50.0D : 30.0D);
            BlockPos pos = result.m_82425_().m_121945_(result.m_82434_());
            if (level.m_8055_(pos).m_60713_((Block)TensuraBlocks.LABYRINTH_BARRIER_BLOCK.get())) {
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
               return;
            }

            if (SkillHelper.outOfMagicule(entity, 10.0D * Math.sqrt(entity.m_20275_((double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_())))) {
               return;
            }

            SpatialMotionSkill.warp(entity, (double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_());
            this.addMasteryPoint(instance, entity);
            instance.setCoolDown(instance.isMastered(entity) ? 2 : 5);
         }

      }
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      ThoughtAccelerationSkill.onToggle(instance, entity, ACCELERATION, true);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      ThoughtAccelerationSkill.onToggle(instance, entity, ACCELERATION, false);
   }
}
