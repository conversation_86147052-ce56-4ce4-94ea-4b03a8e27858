package com.github.manasmods.tensura.ability.skill.unique;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Optional;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class GourmandSkill extends Skill {
   public GourmandSkill() {
      super(Skill.SkillType.UNIQUE);
   }

   public double getObtainingEpCost() {
      return 50000.0D;
   }

   public void onTouchEntity(ManasSkillInstance instance, LivingEntity attacker, LivingHurtEvent event) {
      if (this.isInSlot(attacker)) {
         float chance = instance.isMastered(attacker) ? 0.75F : 0.5F;
         if (!(attacker.m_217043_().m_188501_() > chance)) {
            if (SkillHelper.drainMP(event.getEntity(), attacker, 0.01D, true) && attacker instanceof Player) {
               Player player = (Player)attacker;
               player.m_6330_(SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }

         }
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      LivingEntity target = SkillHelper.getTargetingEntity(entity, 5.0D, false);
      if (target != null) {
         if (target instanceof Player) {
            Player player = (Player)target;
            if (player.m_150110_().f_35934_) {
               return;
            }
         }

         double var10000;
         if (entity instanceof Player) {
            Player player = (Player)entity;
            var10000 = TensuraPlayerCapability.getCurrentEP(player);
         } else {
            var10000 = TensuraEPCapability.getEP(entity);
         }

         double EP = var10000;
         if (target instanceof Player) {
            Player player = (Player)target;
            var10000 = TensuraPlayerCapability.getCurrentEP(player);
         } else {
            var10000 = TensuraEPCapability.getEP(target);
         }

         double targetEP = var10000;
         MobEffectInstance fear = target.m_21124_((MobEffect)TensuraMobEffects.FEAR.get());
         if ((fear != null && fear.m_19564_() >= 4 || targetEP <= EP * 0.1D) && target.m_6469_(this.sourceWithMP(TensuraDamageSources.heartEat(entity), entity, instance), target.m_21233_() * 10.0F)) {
            this.addMasteryPoint(instance, entity);
            if (entity instanceof Player) {
               Player player = (Player)entity;
               TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
                  cap.setMagicule(cap.getMagicule() + targetEP / 2.0D);
                  cap.setAura(cap.getAura() + targetEP / 2.0D);
               });
               TensuraPlayerCapability.sync(player);
            }

            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11912_, SoundSource.PLAYERS, 1.0F, 1.0F);
            TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123765_, 1.0D);
         }

      }
   }

   public static float getGourmandBoost(Player player, boolean magicule, boolean majin) {
      TensuraSkill skill = (TensuraSkill)UniqueSkills.GOURMAND.get();
      if (!skill.isInSlot(player)) {
         return 0.0F;
      } else {
         Optional<ManasSkillInstance> optional = SkillAPI.getSkillsFrom(player).getSkill(skill);
         if (optional.isEmpty()) {
            return 0.0F;
         } else if (((ManasSkillInstance)optional.get()).isMastered(player)) {
            if (majin) {
               return magicule ? 0.08F : 0.09F;
            } else {
               return magicule ? 0.09F : 0.08F;
            }
         } else if (majin) {
            return magicule ? 0.03F : 0.04F;
         } else {
            return magicule ? 0.04F : 0.03F;
         }
      }
   }
}
