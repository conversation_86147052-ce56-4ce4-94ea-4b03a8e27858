package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import java.util.Iterator;
import java.util.List;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.network.PacketDistributor;

public class DemonLordHakiSkill extends Skill {
   private static final String HAKI = "2e430fc9-a0b6-46e2-8cd2-9c87262344ea";

   public DemonLordHakiSkill() {
      super(Skill.SkillType.EXTRA);
      this.addHeldAttributeModifier(Attributes.f_22279_, "2e430fc9-a0b6-46e2-8cd2-9c87262344ea", -0.949999988079071D, Operation.MULTIPLY_TOTAL);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      if (!TensuraEPCapability.isMajin(entity) && !TensuraEPCapability.isChaos(entity)) {
         return false;
      } else if (!SkillUtils.hasSkill(entity, (ManasSkill)ExtraSkills.HAKI.get())) {
         return false;
      } else {
         return newEP > 200000.0D;
      }
   }

   public double learningCost() {
      return 500.0D;
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.haki.release");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.haki.coat");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = 50.0D;
         break;
      case 2:
         var10000 = 100.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public double getAttributeModifierAmplifier(ManasSkillInstance instance, LivingEntity entity, AttributeModifier modifier) {
      return modifier.m_22218_() * (instance.isMastered(entity) ? 0.9473684210526315D : 1.0D);
   }

   public void addHeldAttributeModifiers(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() == 1) {
         super.addHeldAttributeModifiers(instance, entity);
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() != 1) {
         if (!entity.m_21023_((MobEffect)TensuraMobEffects.HAKI_COAT.get())) {
            if (SkillHelper.outOfMagicule(entity, instance)) {
               return;
            }

            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.HAKI_COAT.get(), 2400, 0, false, false, false));
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12052_, SoundSource.PLAYERS, 1.0F, 1.0F);
         } else {
            entity.m_21195_((MobEffect)TensuraMobEffects.HAKI_COAT.get());
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12052_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() != 1) {
         return false;
      } else if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
         return false;
      } else {
         if (heldTicks % 100 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         activateDemonLordHaki(instance, entity, heldTicks);
         return true;
      }
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() == 1) {
         if (this.hasAttributeApplied(entity, Attributes.f_22279_, "2e430fc9-a0b6-46e2-8cd2-9c87262344ea")) {
            instance.setCoolDown(instance.isMastered(entity) ? 3 : 5);
         }
      }
   }

   public static void activateDemonLordHaki(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_215771_, SoundSource.PLAYERS, 1.0F, 1.0F);
      if (heldTicks % 2 == 0) {
         TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
            return entity;
         }), new RequestFxSpawningPacket(new ResourceLocation("tensura:demon_lord_haki"), entity.m_19879_(), 0.0D, 1.0D, 0.0D, true));
      }

      List<LivingEntity> list = entity.m_9236_().m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(15.0D), (living) -> {
         return !living.m_7306_(entity) && living.m_6084_() && !living.m_7307_(entity);
      });
      if (!list.isEmpty()) {
         double scale = instance.getTag() == null ? 0.0D : instance.getTag().m_128459_("scale");
         double multiplier = scale == 0.0D ? 1.0D : Math.min(scale, 1.0D);
         double ownerEP = TensuraEPCapability.getEP(entity) * multiplier;
         Iterator var10 = list.iterator();

         while(true) {
            LivingEntity target;
            Player player;
            do {
               if (!var10.hasNext()) {
                  return;
               }

               target = (LivingEntity)var10.next();
               if (!(target instanceof Player)) {
                  break;
               }

               player = (Player)target;
            } while(player.m_150110_().f_35934_);

            double targetEP = TensuraEPCapability.getEP(target);
            double difference = ownerEP / targetEP;
            if (!(difference <= 2.0D)) {
               int fearLevel = (int)(difference * 0.5D - 1.0D);
               fearLevel = Math.min(fearLevel, (Integer)TensuraConfig.INSTANCE.mobEffectConfig.maxFear.get());
               SkillHelper.checkThenAddEffectSource(target, entity, (MobEffect)TensuraMobEffects.FEAR.get(), 200, fearLevel);
               HakiSkill.hakiPush(target, entity, fearLevel);
            }
         }
      }
   }

   public void onScroll(ManasSkillInstance instance, LivingEntity entity, double delta) {
      if (instance.getMode() == 1) {
         HakiSkill.changeEPUsed(instance, entity, delta);
      }
   }
}
