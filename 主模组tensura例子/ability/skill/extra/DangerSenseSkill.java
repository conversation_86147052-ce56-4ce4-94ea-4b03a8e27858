package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.network.protocol.game.ClientboundSoundPacket;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.event.entity.living.LivingChangeTargetEvent;

public class DangerSenseSkill extends Skill {
   public DangerSenseSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return newEP > 0.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public boolean canBeSlotted(ManasSkillInstance instance) {
      return instance.getMastery() < 0;
   }

   public void onLearnSkill(ManasSkillInstance instance, LivingEntity entity, UnlockSkillEvent event) {
      if (instance.getMastery() >= 0 && !instance.isTemporarySkill()) {
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         ManasSkill skill = (ManasSkill)ExtraSkills.MAGIC_SENSE.get();
         ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
         manipulation.setMastery(-100);
         if (storage.learnSkill(manipulation) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }

      }
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      if (!storage.getSkill((ManasSkill)ExtraSkills.MAGIC_SENSE.get()).isEmpty()) {
         ManasSkill skill = (ManasSkill)ExtraSkills.SENSE_SOUNDWAVE.get();
         ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
         manipulation.setMastery(-100);
         if (storage.learnSkill(manipulation) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }

      }
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }

   public void onBeingTargeted(ManasSkillInstance instance, LivingEntity target, LivingChangeTargetEvent event) {
      if (!SkillUtils.noInteractiveMode(target)) {
         if (instance.isToggled()) {
            if (target instanceof ServerPlayer) {
               ServerPlayer player = (ServerPlayer)target;
               LivingEntity var6 = event.getEntity();
               if (var6 instanceof Mob) {
                  Mob mob = (Mob)var6;
                  if (mob.m_5448_() == null || !target.m_7306_(mob.m_5448_())) {
                     if (player.m_217043_().m_188499_()) {
                        this.addMasteryPoint(instance, target);
                     }

                     this.sendSound(player, mob);
                  }

               }
            }
         }
      }
   }

   private void sendSound(ServerPlayer user, LivingEntity target) {
      Vec3 eyeVec = user.m_146892_();
      Vec3 soundPos = eyeVec.m_82549_(target.m_146892_().m_82546_(eyeVec).m_82541_().m_82490_(5.0D));
      user.f_8906_.m_9829_(new ClientboundSoundPacket(SoundEvents.f_11699_, SoundSource.HOSTILE, soundPos.m_7096_(), eyeVec.m_7098_(), soundPos.m_7094_(), 1.0F, 1.0F, user.m_217043_().m_188505_()));
   }
}
