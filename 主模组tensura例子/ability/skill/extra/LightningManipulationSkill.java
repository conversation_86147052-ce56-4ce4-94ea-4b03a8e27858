package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.entity.magic.lightning.LightningBolt;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.Iterator;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class LightningManipulationSkill extends Skill {
   public LightningManipulationSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public double learningCost() {
      return 100.0D;
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return TensuraSkillCapability.getSpiritLevel(entity, MagicElemental.WIND.getId()) >= 3;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 20.0D;
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      if (!(TensuraEPCapability.getEP(entity) < 400000.0D)) {
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         ManasSkill skill = (ManasSkill)ExtraSkills.LIGHTNING_DOMINATION.get();
         ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
         manipulation.setMastery(-100);
         if (storage.learnSkill(manipulation) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }

      }
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity living, LivingHurtEvent e) {
      if (instance.isToggled()) {
         if (!SkillUtils.isSkillToggled(living, (ManasSkill)ExtraSkills.LIGHTNING_DOMINATION.get())) {
            if (DamageSourceHelper.isLightningDamage(e.getSource())) {
               e.setAmount(e.getAmount() * 2.0F);
            }

         }
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      if (level.m_46470_()) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            this.addMasteryPoint(instance, entity);
            Entity target = SkillHelper.getTargetingEntity(entity, 30.0D, false, false);
            Vec3 pos;
            if (target != null) {
               pos = target.m_20182_();
            } else {
               BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, 30.0D);
               pos = result.m_82450_();
            }

            LightningBolt bolt = new LightningBolt(level, entity);
            ServerPlayer var10001;
            if (entity instanceof ServerPlayer) {
               ServerPlayer serverPlayer = (ServerPlayer)entity;
               var10001 = serverPlayer;
            } else {
               var10001 = null;
            }

            bolt.m_20879_(var10001);
            bolt.setMpCost(this.magiculeCost(entity, instance));
            bolt.setTensuraDamage(15.0F);
            bolt.setAdditionalVisual(2);
            bolt.setRadius(3.0F);
            bolt.setSkill(instance);
            bolt.m_146884_(pos);
            level.m_7967_(bolt);
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 0.5F, 1.0F);
         }
      }
   }

   public static void learnLightningManipulation(ManasSkillInstance instance, LivingEntity entity) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      int skills = isLightningSKill(instance) ? 1 : 0;
      Iterator var4 = storage.getLearnedSkills().iterator();

      while(var4.hasNext()) {
         ManasSkillInstance skill = (ManasSkillInstance)var4.next();
         if (isLightningSKill(skill)) {
            ++skills;
         }
      }

      if (skills >= 2) {
         ManasSkill skill = (ManasSkill)ExtraSkills.LIGHTNING_MANIPULATION.get();
         ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
         manipulation.setMastery(-100);
         if (storage.learnSkill(manipulation) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }

      }
   }

   private static boolean isLightningSKill(ManasSkillInstance instance) {
      if (!instance.isTemporarySkill() && instance.getMastery() >= 0) {
         if (instance.getSkill().equals(ExtraSkills.WIND_MANIPULATION.get())) {
            return true;
         } else if (instance.getSkill().equals(ExtraSkills.WIND_DOMINATION.get())) {
            return true;
         } else {
            return instance.getSkill().equals(ExtraSkills.BLACK_LIGHTNING.get()) ? true : instance.getSkill().equals(IntrinsicSkills.THUNDER_BREATH.get());
         }
      } else {
         return false;
      }
   }
}
