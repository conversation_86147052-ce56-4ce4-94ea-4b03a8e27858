package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.magic.Magic;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import java.util.Iterator;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class MagicAuraSkill extends Skill {
   public MagicAuraSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      int magics = 0;
      Iterator var5 = SkillAPI.getSkillsFrom(entity).getLearnedSkills().iterator();

      while(var5.hasNext()) {
         ManasSkillInstance skill = (ManasSkillInstance)var5.next();
         if (skill.getSkill() instanceof Magic && skill.isMastered(entity)) {
            ++magics;
         }
      }

      return magics >= 7;
   }

   public double learningCost() {
      return 1000.0D;
   }

   public int modes() {
      return 7;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      int var10000;
      if (reverse) {
         switch(instance.getMode()) {
         case 1:
            var10000 = entity.m_21023_((MobEffect)TensuraMobEffects.MAGIC_AURA.get()) ? 0 : 7;
            break;
         case 2:
            var10000 = entity.m_21023_((MobEffect)TensuraMobEffects.MAGIC_AURA.get()) ? 0 : 1;
            break;
         case 3:
            var10000 = 2;
            break;
         case 4:
            var10000 = 3;
            break;
         case 5:
            var10000 = 4;
            break;
         case 6:
            var10000 = 5;
            break;
         case 7:
            var10000 = 6;
            break;
         default:
            var10000 = 0;
         }

         return var10000;
      } else {
         switch(instance.getMode()) {
         case 1:
            var10000 = entity.m_21023_((MobEffect)TensuraMobEffects.MAGIC_AURA.get()) ? 0 : 2;
            break;
         case 2:
            var10000 = 3;
            break;
         case 3:
            var10000 = 4;
            break;
         case 4:
            var10000 = 5;
            break;
         case 5:
            var10000 = 6;
            break;
         case 6:
            var10000 = 7;
            break;
         case 7:
            var10000 = entity.m_21023_((MobEffect)TensuraMobEffects.MAGIC_AURA.get()) ? 0 : 1;
            break;
         default:
            var10000 = 0;
         }

         return var10000;
      }
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.magic_aura.default");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.magic_aura.holy");
         break;
      case 3:
         var10000 = Component.m_237115_("tensura.skill.mode.magic_aura.earth");
         break;
      case 4:
         var10000 = Component.m_237115_("tensura.skill.mode.magic_aura.fire");
         break;
      case 5:
         var10000 = Component.m_237115_("tensura.skill.mode.magic_aura.space");
         break;
      case 6:
         var10000 = Component.m_237115_("tensura.skill.mode.magic_aura.water");
         break;
      case 7:
         var10000 = Component.m_237115_("tensura.skill.mode.magic_aura.wind");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return instance.getMode() == 1 ? 500.0D : 1000.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!entity.m_21023_((MobEffect)TensuraMobEffects.MAGIC_AURA.get())) {
         if (SkillHelper.outOfMagicule(entity, instance)) {
            return;
         }

         this.addMasteryPoint(instance, entity);
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.MAGIC_AURA.get(), 6000, 0, false, false, false));
      } else {
         entity.m_21195_((MobEffect)TensuraMobEffects.MAGIC_AURA.get());
         entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 0.5F);
      }

   }
}
