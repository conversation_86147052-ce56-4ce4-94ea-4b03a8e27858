package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.entity.magic.skill.HeatSphereProjectile;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.network.PacketDistributor;

public class HeatWaveSkill extends Skill {
   public HeatWaveSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public double learningCost() {
      return 200.0D;
   }

   public int modes() {
      return 2;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.heat_wave.sphere");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.heat_wave.storm");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      double var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = 40.0D;
         break;
      case 2:
         var10000 = 20.0D;
         break;
      default:
         var10000 = 0.0D;
      }

      return var10000;
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() != 2) {
         return false;
      } else if (heldTicks % 15 == 0 && SkillHelper.outOfMagicule(entity, instance)) {
         return false;
      } else {
         if (heldTicks % 100 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         float radius = 5.0F;
         entity.f_19853_.m_45976_(LivingEntity.class, entity.m_20191_().m_82400_((double)radius)).stream().filter((target) -> {
            return !target.m_7306_(entity) && !target.m_5825_() && target.m_20270_(entity) <= radius;
         }).forEach((target) -> {
            target.m_6469_(this.sourceWithMP(TensuraDamageSources.heatWave(entity), entity, instance), 5.0F);
            target.m_20254_(10);
         });
         entity.f_19853_.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11936_, SoundSource.PLAYERS, 1.0F, 1.0F);
         TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
            return entity;
         }), new RequestFxSpawningPacket(new ResourceLocation("tensura:heat_storm"), entity.m_19879_(), 0.0D, 1.0D, 0.0D, true));
         return true;
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMode() == 1) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            this.addMasteryPoint(instance, entity);
            HeatSphereProjectile sphere = new HeatSphereProjectile(entity.m_9236_(), entity);
            sphere.setDamage(30.0F);
            sphere.setSpeed(1.0F);
            sphere.setEffectRange(2.5F);
            sphere.setBurnTicks(20);
            sphere.m_20242_(true);
            sphere.setMpCost(this.magiculeCost(entity, instance));
            sphere.setSkill(instance);
            sphere.setPosAndShoot(entity);
            entity.m_9236_().m_7967_(sphere);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11896_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }
      }
   }
}
