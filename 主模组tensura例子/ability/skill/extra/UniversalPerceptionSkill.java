package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import java.util.Objects;
import net.minecraft.ChatFormatting;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class UniversalPerceptionSkill extends Skill {
   public UniversalPerceptionSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public double learningCost() {
      return 250.0D;
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   protected boolean canActivateInRaceLimit(ManasSkillInstance instance) {
      return true;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 25.0D;
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      if (SkillHelper.outOfMagicule(entity, instance)) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.lack_magicule.toggled_off", new Object[]{instance.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
         }

         instance.setToggled(false);
      } else {
         CompoundTag tag = instance.getOrCreateTag();
         int time = tag.m_128451_("activatedTimes");
         if (time % 6 == 0) {
            this.addMasteryPoint(instance, entity);
         }

         tag.m_128405_("activatedTimes", time + 1);
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get(), 200, 1, false, false, false));
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.HEAT_SENSE.get(), 200, 0, false, false, false));
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.AUDITORY_SENSE.get(), 200, 0, false, false, false));
      }
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_21023_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get())) {
         int level = ((MobEffectInstance)Objects.requireNonNull(entity.m_21124_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get()))).m_19564_();
         if (level == 1) {
            entity.m_21195_((MobEffect)TensuraMobEffects.PRESENCE_SENSE.get());
         }
      }

      entity.m_21195_((MobEffect)TensuraMobEffects.HEAT_SENSE.get());
      entity.m_21195_((MobEffect)TensuraMobEffects.AUDITORY_SENSE.get());
   }
}
