package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.registry.magic.SpiritualMagics;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.Iterator;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class SoundManipulationSkill extends Skill {
   public SoundManipulationSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public double learningCost() {
      return 1000.0D;
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return learnSoundManipulation(entity);
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      if (!(TensuraEPCapability.getEP(entity) < 400000.0D)) {
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         ManasSkill skill = (ManasSkill)ExtraSkills.SOUND_DOMINATION.get();
         ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
         manipulation.setMastery(-100);
         if (storage.learnSkill(manipulation) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }

      }
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public void onDamageEntity(ManasSkillInstance instance, LivingEntity living, LivingHurtEvent e) {
      if (instance.isToggled()) {
         if (!SkillUtils.isSkillToggled(living, (ManasSkill)ExtraSkills.SOUND_DOMINATION.get())) {
            if (DamageSourceHelper.isSoundDamage(e.getSource())) {
               e.setAmount(e.getAmount() * 2.0F);
            }

            this.addMasteryPoint(instance, living);
         }
      }
   }

   public static boolean learnSoundManipulation(LivingEntity entity) {
      int skills = 0;
      Iterator var2 = SkillAPI.getSkillsFrom(entity).getLearnedSkills().iterator();

      while(var2.hasNext()) {
         ManasSkillInstance skill = (ManasSkillInstance)var2.next();
         if (isSoundSKill(skill, entity)) {
            ++skills;
         }
      }

      return skills >= 2;
   }

   private static boolean isSoundSKill(ManasSkillInstance instance, LivingEntity entity) {
      if (!instance.isMastered(entity)) {
         return false;
      } else if (instance.getSkill().equals(IntrinsicSkills.ULTRASONIC_WAVES.get())) {
         return true;
      } else if (instance.getSkill().equals(SpiritualMagics.WIND.get())) {
         return true;
      } else {
         return instance.getSkill().equals(SpiritualMagics.WIND_BLADE.get()) ? true : instance.getSkill().equals(SpiritualMagics.AERIAL_BLADE.get());
      }
   }
}
