package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import java.util.UUID;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingDamageEvent;

public class StrengthenBodySkill extends Skill {
   protected static final UUID STRENGTHEN = UUID.fromString("ad864d4e-4bd6-11ee-be56-0242ac120002");

   public StrengthenBodySkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public boolean canBeSlotted(ManasSkillInstance instance) {
      return instance.getMastery() < 0;
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return newEP > 15000.0D;
   }

   public void onTakenDamage(ManasSkillInstance instance, LivingDamageEvent e) {
      if (instance.isToggled()) {
         if (!e.getSource().m_19378_()) {
            DamageSource var4 = e.getSource();
            if (var4 instanceof TensuraDamageSource) {
               TensuraDamageSource tensuraSource = (TensuraDamageSource)var4;
               if (tensuraSource.getIgnoreBarrier() >= 2.0F) {
                  return;
               }
            }

            e.setAmount(e.getAmount() * 0.8F);
         }
      }
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11671_, SoundSource.PLAYERS, 0.5F, 0.5F);
      AttributeInstance armor = entity.m_21051_(Attributes.f_22284_);
      if (armor != null) {
         AttributeModifier armorModifier = new AttributeModifier(STRENGTHEN, "Strengthen Body", 5.0D, Operation.ADDITION);
         if (!armor.m_22109_(armorModifier)) {
            armor.m_22125_(armorModifier);
         }

      }
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11665_, SoundSource.PLAYERS, 0.5F, 0.5F);
      AttributeInstance armor = entity.m_21051_(Attributes.f_22284_);
      if (armor != null) {
         armor.m_22127_(STRENGTHEN);
      }

   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int time = tag.m_128451_("activatedTimes");
      if (time % 6 == 0) {
         this.addMasteryPoint(instance, entity);
      }

      tag.m_128405_("activatedTimes", time + 1);
   }
}
