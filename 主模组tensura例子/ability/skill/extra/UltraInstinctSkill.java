package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.ProjectileImpactEvent;
import net.minecraftforge.event.entity.living.LivingAttackEvent;

public class UltraInstinctSkill extends Skill {
   public UltraInstinctSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity living) {
      return true;
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }

   public void onBeingDamaged(ManasSkillInstance instance, LivingAttackEvent event) {
      if (!event.isCanceled()) {
         if (instance.isToggled()) {
            DamageSource damageSource = event.getSource();
            if (!damageSource.m_19378_()) {
               if (damageSource.m_7640_() != null && damageSource.m_7640_() == damageSource.m_7639_()) {
                  if (!damageSource.m_19387_()) {
                     LivingEntity entity = event.getEntity();
                     if (entity.m_217043_().m_188503_(5) == 1) {
                        TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123766_, 1.0D);
                        entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
                        event.setCanceled(true);
                        if (SkillUtils.canNegateDodge(entity, damageSource)) {
                           event.setCanceled(false);
                        } else {
                           CompoundTag tag = instance.getOrCreateTag();
                           int time = tag.m_128451_("activatedTimes");
                           if (time % 6 == 0) {
                              this.addMasteryPoint(instance, entity);
                           }

                           tag.m_128405_("activatedTimes", time + 1);
                        }

                     }
                  }
               }
            }
         }
      }
   }

   public void onProjectileHit(ManasSkillInstance instance, LivingEntity entity, ProjectileImpactEvent event) {
      if (instance.isToggled()) {
         if (!SkillUtils.isProjectileAlwaysHit(event.getProjectile())) {
            if (entity.m_217043_().m_188503_(5) == 1) {
               TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123766_, 1.0D);
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 2.0F, 1.0F);
               event.setCanceled(true);
            }
         }
      }
   }
}
