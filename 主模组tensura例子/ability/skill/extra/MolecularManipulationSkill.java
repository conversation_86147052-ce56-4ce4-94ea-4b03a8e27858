package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Block;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.SlabBlock;
import net.minecraft.world.level.block.entity.BeehiveBlockEntity;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.entity.ShulkerBoxBlockEntity;
import net.minecraft.world.level.block.entity.BeehiveBlockEntity.BeeReleaseStatus;
import net.minecraft.world.level.block.piston.PistonHeadBlock;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.Property;
import net.minecraft.world.level.block.state.properties.SlabType;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.common.MinecraftForge;

public class MolecularManipulationSkill extends Skill {
   public MolecularManipulationSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public double learningCost() {
      return 50.0D;
   }

   public int modes() {
      return 3;
   }

   public int nextMode(LivingEntity entity, TensuraSkillInstance instance, boolean reverse) {
      return instance.getMode() == 1 ? 2 : 1;
   }

   public Component getModeName(int mode) {
      MutableComponent var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("tensura.skill.mode.molecular_manipulation.block");
         break;
      case 2:
         var10000 = Component.m_237115_("tensura.skill.mode.molecular_manipulation.entity");
         break;
      default:
         var10000 = Component.m_237119_();
      }

      return var10000;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 5.0D;
   }

   public void onScroll(ManasSkillInstance instance, LivingEntity entity, double delta) {
      if (instance instanceof TensuraSkillInstance) {
         TensuraSkillInstance skillInstance = (TensuraSkillInstance)instance;
         CompoundTag tag = skillInstance.getOrCreateTag();
         double newRange = tag.m_128459_("range") + delta;
         if (newRange > 30.0D) {
            newRange = 30.0D;
         } else if (newRange < 0.0D) {
            newRange = 0.0D;
         }

         tag.m_128347_("range", newRange);
         skillInstance.markDirty();
      }

   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      switch(instance.getMode()) {
      case 1:
         this.breakBlock(entity, instance);
         break;
      case 2:
         Entity target = SkillHelper.getTargetingEntity(entity, 30.0D, 1.0D, false, true);
         if (target == null) {
            return;
         }

         if (target.m_6095_().m_204039_(TensuraTags.EntityTypes.FULL_GRAVITY_CONTROL)) {
            return;
         }

         label60: {
            if (entity instanceof Player) {
               Player player = (Player)entity;
               if (player.m_150110_().f_35934_) {
                  break label60;
               }
            }

            if (target instanceof Player) {
               Player player = (Player)target;
               if (player.m_150110_().f_35934_) {
                  return;
               }
            }

            if (SkillUtils.isSkillToggled(target, (ManasSkill)ExtraSkills.GRAVITY_DOMINATION.get()) || SkillUtils.isSkillToggled(target, (ManasSkill)ExtraSkills.GRAVITY_MANIPULATION.get())) {
               return;
            }
         }

         double maxSize = instance.isMastered(entity) ? 1.0D : 0.5D;
         if (SkillUtils.isSkillToggled(entity, (ManasSkill)ExtraSkills.GRAVITY_DOMINATION.get())) {
            maxSize += 5.0D;
         } else if (SkillUtils.isSkillToggled(entity, (ManasSkill)ExtraSkills.GRAVITY_MANIPULATION.get())) {
            maxSize += 3.0D;
         }

         if (target.m_20191_().m_82309_() > maxSize) {
            return;
         }

         CompoundTag tag = instance.getOrCreateTag();
         tag.m_128362_("target", target.m_20148_());
         double range = Math.min(30.0D, target.m_20182_().m_82546_(entity.m_146892_()).m_82553_());
         tag.m_128347_("range", (double)((int)range));
      }

   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.getMode() != 2) {
         return false;
      } else {
         Level var5 = entity.m_9236_();
         if (var5 instanceof ServerLevel) {
            ServerLevel level = (ServerLevel)var5;
            CompoundTag tag = instance.getOrCreateTag();
            double range = tag.m_128459_("range");
            if (entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237110_("tensura.skill.range", new Object[]{range}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_AQUA)), true);
            }

            if (tag.m_128441_("target")) {
               Entity target = level.m_8791_(tag.m_128342_("target"));
               if (target != null) {
                  Vec3 viewVector = entity.m_20252_(1.0F).m_82490_(range);
                  double x = entity.m_20185_() + viewVector.f_82479_;
                  double y = entity.m_20188_() + viewVector.f_82480_;
                  double z = entity.m_20189_() + viewVector.f_82481_;
                  Vec3 targetPos = new Vec3(x, y, z);
                  if (targetPos.m_82554_(target.m_20182_()) <= 6.0D) {
                     if (heldTicks % 60 == 0 && heldTicks > 0) {
                        this.addMasteryPoint(instance, entity);
                     }

                     TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123760_, 1.0D);
                     Vec3 vec3 = targetPos.m_82546_(target.m_20182_()).m_82541_().m_82490_(0.5D);
                     if (vec3.m_82553_() > 0.2D) {
                        target.m_20256_(vec3);
                     }

                     target.m_183634_();
                     target.f_19864_ = true;
                  }
               }
            }

            return true;
         } else {
            return false;
         }
      }
   }

   private void breakBlock(LivingEntity entity, ManasSkillInstance instance) {
      Level level = entity.m_9236_();
      if (TensuraGameRules.canSkillGrief(level)) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (!SkillHelper.outOfMagicule(entity, instance)) {
               BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, player, Fluid.NONE, Block.OUTLINE, 30.0D);
               if (result.m_6662_() == Type.BLOCK) {
                  BlockPos pos = result.m_82425_();
                  BlockState state = level.m_8055_(pos);
                  if (state.m_204336_(TensuraTags.Blocks.SKILL_UNOBTAINABLE)) {
                     return;
                  }

                  if (state.m_60734_().m_155943_() <= -1.0F) {
                     return;
                  }

                  SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(entity, instance, pos);
                  if (MinecraftForge.EVENT_BUS.post(preGrief)) {
                     return;
                  }

                  state.m_60734_().m_5707_(level, pos, state, player);
                  if (isMultiBlock(state)) {
                     level.m_46953_(pos, true, player);
                  } else {
                     label78: {
                        BlockEntity blockentity = level.m_7702_(pos);
                        if (blockentity instanceof BeehiveBlockEntity) {
                           BeehiveBlockEntity beehiveblockentity = (BeehiveBlockEntity)blockentity;
                           beehiveblockentity.m_58748_(player, state, BeeReleaseStatus.EMERGENCY);
                        }

                        if (blockentity instanceof ShulkerBoxBlockEntity) {
                           ShulkerBoxBlockEntity shulker = (ShulkerBoxBlockEntity)blockentity;
                           if (!shulker.m_7983_()) {
                              level.m_46953_(pos, !player.m_7500_(), player);
                              break label78;
                           }
                        }

                        ItemStack stack = new ItemStack(state.m_60734_());
                        if (!player.m_36356_(stack)) {
                           player.m_36176_(stack, false);
                        }

                        level.m_46953_(pos, false, player);
                     }
                  }

                  MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(entity, instance, pos));
                  List<ItemEntity> list = player.f_19853_.m_45976_(ItemEntity.class, AABB.m_165882_(Vec3.m_82512_(pos), 2.5D, 2.5D, 2.5D));
                  Iterator var14 = list.iterator();

                  while(var14.hasNext()) {
                     ItemEntity item = (ItemEntity)var14.next();
                     if (player.m_36356_(item.m_32055_())) {
                        item.m_146870_();
                     } else {
                        item.m_20219_(player.m_20182_());
                     }
                  }

                  player.m_21011_(InteractionHand.MAIN_HAND, true);
                  level.m_6263_(player, (double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_(), SoundEvents.f_11757_, SoundSource.PLAYERS, 0.5F, 1.0F);
                  ((ServerLevel)player.m_9236_()).m_8767_(ParticleTypes.f_123760_, (double)pos.m_123341_() + 0.5D, (double)pos.m_123342_() + 0.5D, (double)pos.m_123343_() + 0.5D, 10, 0.08D, 0.08D, 0.08D, 0.1D);
               }

            }
         }
      }
   }

   public static void learnMolecular(LivingEntity entity) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      int skills = 0;
      Iterator var3 = storage.getLearnedSkills().iterator();

      while(var3.hasNext()) {
         ManasSkillInstance skill = (ManasSkillInstance)var3.next();
         if (masteredManipulation(skill, entity)) {
            ++skills;
         }
      }

      if (skills >= 2) {
         ManasSkill skill = (ManasSkill)ExtraSkills.MOLECULAR_MANIPULATION.get();
         ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
         manipulation.setMastery(-100);
         if (storage.learnSkill(manipulation) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }

      }
   }

   private static boolean masteredManipulation(ManasSkillInstance instance, LivingEntity entity) {
      if (!instance.isMastered(entity)) {
         return false;
      } else if (instance.getSkill().equals(ExtraSkills.WIND_MANIPULATION.get())) {
         return true;
      } else if (instance.getSkill().equals(ExtraSkills.WATER_MANIPULATION.get())) {
         return true;
      } else if (instance.getSkill().equals(ExtraSkills.SPATIAL_MANIPULATION.get())) {
         return true;
      } else {
         return instance.getSkill().equals(ExtraSkills.FLAME_MANIPULATION.get()) ? true : instance.getSkill().equals(ExtraSkills.EARTH_MANIPULATION.get());
      }
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      tag.m_128473_("target");
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      CompoundTag tag = instance.getOrCreateTag();
      tag.m_128473_("target");
   }

   public static boolean isMultiBlock(BlockState state) {
      if (state.m_61138_(SlabBlock.f_56353_) && state.m_61143_(SlabBlock.f_56353_) == SlabType.DOUBLE) {
         return true;
      } else if (state.m_60734_() instanceof PistonHeadBlock) {
         return true;
      } else {
         Iterator var1 = state.m_61147_().iterator();

         Property property;
         do {
            if (!var1.hasNext()) {
               return false;
            }

            property = (Property)var1.next();
            if (property.m_61708_().equals("half")) {
               return true;
            }
         } while(!property.m_61708_().equals("part"));

         return true;
      }
   }
}
