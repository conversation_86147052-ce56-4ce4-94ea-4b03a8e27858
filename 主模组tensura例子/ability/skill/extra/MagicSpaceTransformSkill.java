package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;

public class MagicSpaceTransformSkill extends MagicElementalTransformSkill {
   protected MagicElemental getMagicElemental() {
      return MagicElemental.SPACE;
   }

   protected ManasSkill getElementalTransform() {
      return (ManasSkill)IntrinsicSkills.SPACE_TRANSFORM.get();
   }

   protected MobEffect getMagicElementalEffect() {
      return (MobEffect)TensuraMobEffects.MAGIC_SPACE.get();
   }

   protected void doVisualEffect(LivingEntity entity) {
      TensuraParticleHelper.spawnServerParticles(entity.f_19853_, ParticleTypes.f_123810_, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), 55, 0.08D, 0.08D, 0.08D, 0.5D, true);
   }
}
