package com.github.manasmods.tensura.ability.skill.extra;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.skill.Skill;
import java.util.Iterator;
import net.minecraft.world.entity.player.Player;

public class SageSkill extends Skill {
   public SageSkill() {
      super(Skill.SkillType.EXTRA);
   }

   public double learningCost() {
      return 1000.0D;
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      int magics = 0;
      Iterator var5 = SkillAPI.getSkillsFrom(entity).getLearnedSkills().iterator();

      while(var5.hasNext()) {
         ManasSkillInstance skill = (ManasSkillInstance)var5.next();
         if (!(skill.getSkill() instanceof Skill) && skill.isMastered(entity)) {
            ++magics;
         }
      }

      return magics >= 20;
   }
}
