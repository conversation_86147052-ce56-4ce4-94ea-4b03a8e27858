package com.github.manasmods.tensura.ability.skill.intrinsic;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.race.ITensuraPlayerCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.handler.CapabilityHandler;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class UnpredictabilitySkill extends Skill {
   public UnpredictabilitySkill() {
      super(Skill.SkillType.INTRINSIC);
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      ITensuraPlayerCapability cap = (ITensuraPlayerCapability)CapabilityHandler.getCapability(entity, TensuraPlayerCapability.CAPABILITY);
      return cap == null ? false : cap.isTrueHero();
   }

   public boolean canBeToggled(ManasSkillInstance instance, LivingEntity entity) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         return TensuraPlayerCapability.isTrueHero(player);
      } else {
         return true;
      }
   }

   public boolean canBeSlotted(ManasSkillInstance instance) {
      return instance.getMastery() < 0;
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isToggled();
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      CompoundTag tag = instance.getOrCreateTag();
      int time = tag.m_128451_("activatedTimes");
      if (time % 10 == 0) {
         this.addMasteryPoint(instance, entity);
      }

      tag.m_128405_("activatedTimes", time + 1);
   }
}
