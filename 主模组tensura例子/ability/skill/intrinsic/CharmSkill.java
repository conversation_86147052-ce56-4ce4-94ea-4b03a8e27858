package com.github.manasmods.tensura.ability.skill.intrinsic;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Objects;
import java.util.UUID;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;

public class CharmSkill extends Skill {
   public CharmSkill() {
      super(Skill.SkillType.INTRINSIC);
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 80.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      charm(instance, entity);
   }

   public static void charm(ManasSkillInstance instance, LivingEntity entity) {
      LivingEntity target = SkillHelper.getTargetingEntity(entity, 5.0D, false);
      if (target != null) {
         UUID uuid = entity.m_20148_();
         Level level = entity.m_9236_();
         if (entity.m_6144_()) {
            TensuraEPCapability.getFrom(target).ifPresent((cap) -> {
               boolean success = false;
               if (Objects.equals(cap.getTemporaryOwner(), uuid)) {
                  cap.setTemporaryOwner((UUID)null);
                  target.m_21195_((MobEffect)TensuraMobEffects.MIND_CONTROL.get());
                  success = true;
                  UUID owner = cap.getPermanentOwner();
                  if (target instanceof TensuraTamableEntity) {
                     TensuraTamableEntity tamable = (TensuraTamableEntity)target;
                     tamable.resetOwner(owner);
                  } else if (target instanceof TensuraHorseEntity) {
                     TensuraHorseEntity horse = (TensuraHorseEntity)target;
                     horse.resetOwner(owner);
                  } else if (target instanceof TamableAnimal) {
                     TamableAnimal animal = (TamableAnimal)target;
                     animal.m_21816_(owner);
                     if (owner == null) {
                        animal.m_7105_(false);
                     }
                  }
               }

               if (cap.isTargetNeutral(uuid)) {
                  cap.removeNeutralTarget(uuid);
                  success = true;
               }

               if (success) {
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  TensuraEPCapability.sync(target);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123792_);
                  level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12052_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }

            });
         } else if (!isMindControlFailed(entity, target, level)) {
            if (target.m_21023_((MobEffect)TensuraMobEffects.RAMPAGE.get()) && entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237115_("tensura.naming.insane").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
            } else {
               double EP = TensuraEPCapability.getEP(entity);
               double resisted;
               if (SkillUtils.hasSkill(target, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_RESISTANCE.get())) {
                  resisted = EP / 5.0D;
               } else {
                  resisted = 0.0D;
               }

               TensuraEPCapability.getFrom(target).ifPresent((cap) -> {
                  boolean success = false;
                  if (cap.getEP() <= EP / 4.0D - resisted) {
                     if (Objects.equals(cap.getPermanentOwner(), uuid)) {
                        return;
                     }

                     if (SkillHelper.outOfMagicule(entity, 80.0D + cap.getEP())) {
                        return;
                     }

                     int duration = instance.isMastered(entity) ? 12000 : 6000;
                     SkillHelper.checkThenAddEffectSource(target, entity, (MobEffect)TensuraMobEffects.MIND_CONTROL.get(), duration, 0, false, false, false, true);
                     if (!target.m_21023_((MobEffect)TensuraMobEffects.MIND_CONTROL.get())) {
                        return;
                     }

                     cap.setTemporaryOwner(uuid);
                     if (target instanceof Mob) {
                        Mob mob = (Mob)target;
                        SkillHelper.removeTarget(mob);
                     }

                     if (entity instanceof Player) {
                        Player playerx = (Player)entity;
                        if (target instanceof TamableAnimal) {
                           TamableAnimal animal = (TamableAnimal)target;
                           animal.m_21828_(playerx);
                        } else if (target instanceof TensuraHorseEntity) {
                           TensuraHorseEntity horse = (TensuraHorseEntity)target;
                           horse.m_30637_(playerx);
                        }
                     }

                     success = true;
                  } else if (cap.getEP() <= EP / 2.0D - resisted) {
                     if (SkillHelper.outOfMagicule(entity, 80.0D + cap.getEP())) {
                        return;
                     }

                     if (cap.isTargetNeutral(uuid)) {
                        return;
                     }

                     cap.addNeutralTarget(uuid);
                     if (target instanceof Mob) {
                        Mob mobx = (Mob)target;
                        SkillHelper.removeTarget(mobx);
                     }

                     success = true;
                  }

                  if (success) {
                     instance.addMasteryPoint(entity);
                     entity.m_21011_(InteractionHand.MAIN_HAND, true);
                     TensuraEPCapability.sync(target);
                     TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123750_);
                     level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12052_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  } else {
                     if (entity instanceof Player) {
                        Player player = (Player)entity;
                        player.m_5661_(Component.m_237115_("tensura.targeting.ep_not_meet").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                     }

                     level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12318_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  }

               });
            }
         }
      }
   }

   public static boolean isMindControlFailed(LivingEntity user, LivingEntity target, Level level) {
      boolean failed = !canMindControl(target, level);
      if (!failed && SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_NULLIFICATION.get())) {
         failed = true;
      }

      if (failed && user instanceof Player) {
         Player player = (Player)user;
         player.m_5661_(Component.m_237115_("tensura.ability.activation_failed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
      }

      return failed;
   }

   public static boolean canMindControl(LivingEntity target, Level level) {
      return canMindControl(target, level, false);
   }

   public static boolean canMindControl(LivingEntity target, Level level, boolean ignorePlayerGamerule) {
      if (target instanceof Player) {
         Player player = (Player)target;
         if (!ignorePlayerGamerule && TensuraGameRules.noPlayerMindControl(level)) {
            return false;
         }

         if (player.m_150110_().f_35934_) {
            return false;
         }
      }

      return !target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_MIND_CONTROL);
   }
}
