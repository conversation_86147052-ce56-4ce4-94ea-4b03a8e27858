package com.github.manasmods.tensura.ability.skill.intrinsic;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.effect.template.Transformation;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.BaseFireBlock;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraftforge.common.MinecraftForge;

public class FlameTransformSkill extends Skill implements Transformation {
   private static final String TRANSFORM = "145cf5e0-6f8b-43c0-8528-24969836b81d";

   public FlameTransformSkill() {
      super(Skill.SkillType.INTRINSIC);
      this.addHeldAttributeModifier(Attributes.f_22279_, "145cf5e0-6f8b-43c0-8528-24969836b81d", -0.5D, Operation.MULTIPLY_TOTAL);
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 30.0D;
   }

   public void onLearnSkill(ManasSkillInstance instance, LivingEntity living, UnlockSkillEvent event) {
      if (instance.getMastery() >= 0 && !instance.isTemporarySkill()) {
         Iterator var4 = SkillAPI.getSkillRegistry().getValues().iterator();

         while(var4.hasNext()) {
            ManasSkill manasSkill = (ManasSkill)var4.next();
            if (manasSkill instanceof SpiritualMagic) {
               SpiritualMagic skill = (SpiritualMagic)manasSkill;
               if (skill.getElemental() == MagicElemental.FLAME && skill.getLevel().getId() <= 2 && SkillUtils.learnSkill(living, (ManasSkill)skill) && living instanceof Player) {
                  Player player = (Player)living;
                  player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
               }
            }
         }

      }
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      if (!(TensuraEPCapability.getEP(entity) < 400000.0D)) {
         SkillStorage storage = SkillAPI.getSkillsFrom(entity);
         ManasSkill skill = (ManasSkill)ExtraSkills.MAGIC_FLAME_TRANSFORM.get();
         ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
         manipulation.setMastery(-100);
         if (storage.learnSkill(manipulation) && entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
         }

      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity living, int heldTicks) {
      if (this.failedToActivate(living, (MobEffect)null)) {
         return false;
      } else if (heldTicks % 20 == 0 && SkillHelper.outOfMagicule(living, instance)) {
         return false;
      } else {
         if (heldTicks % 60 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, living);
         }

         living.m_9236_().m_6263_((Player)null, living.m_20185_(), living.m_20186_(), living.m_20189_(), SoundEvents.f_11909_, SoundSource.PLAYERS, 1.0F, 1.0F);
         TensuraParticleHelper.addServerParticlesAroundSelf(living, (ParticleOptions)TensuraParticles.RED_FIRE.get(), 1.0D);
         ((ServerLevel)living.m_9236_()).m_8767_(ParticleTypes.f_123744_, living.m_20185_(), living.m_20186_() + (double)living.m_20206_() / 2.0D, living.m_20189_(), 10, 0.08D, 0.08D, 0.08D, 0.15D);
         List<LivingEntity> list = living.m_9236_().m_6443_(LivingEntity.class, living.m_20191_().m_82400_(5.0D), (entity) -> {
            return !entity.m_7306_(living) && entity.m_6084_() && !entity.m_7307_(living);
         });
         if (!list.isEmpty()) {
            DamageSource damageSource = TensuraDamageSources.elementalAttack("tensura.fire_attack", living, this.magiculeCost(living, instance), instance, true);
            Iterator var6 = list.iterator();

            while(var6.hasNext()) {
               LivingEntity target = (LivingEntity)var6.next();
               target.m_20254_(5);
               this.placeFire(instance, living, target.m_20097_());
               target.m_6469_(damageSource, 2.0F);
            }
         }

         return true;
      }
   }

   private void placeFire(ManasSkillInstance instance, LivingEntity entity, BlockPos onPos) {
      Level level = entity.m_9236_();
      if (!TensuraGameRules.canSkillGrief(level)) {
         BlockPos pos = onPos.m_7494_();
         BlockState blockState = level.m_8055_(pos);
         SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(entity, instance, pos);
         if (!MinecraftForge.EVENT_BUS.post(preGrief)) {
            if (blockState.m_60767_().m_76336_() && blockState.m_60819_().m_76178_()) {
               BlockState blockStateDown = level.m_8055_(onPos);
               if (blockStateDown.m_60783_(level, onPos, Direction.UP)) {
                  level.m_7471_(pos, true);
                  level.m_142346_(entity, GameEvent.f_157792_, pos);
               }
            }

            if (BaseFireBlock.m_49255_(level, pos, Direction.UP)) {
               level.m_46597_(pos, BaseFireBlock.m_49245_(level, pos));
               level.m_186460_(pos, blockState.m_60734_(), 20);
               level.m_142346_(entity, GameEvent.f_157792_, pos);
            }

            MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(entity, instance, pos));
         }
      }
   }
}
