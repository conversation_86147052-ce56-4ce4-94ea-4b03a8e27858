package com.github.manasmods.tensura.ability.skill.intrinsic;

import com.github.manasmods.manascore.api.attribute.AttributeModifierHelper;
import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.manascore.attribute.ManasCoreAttributes;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.human.CloneEntity;
import com.github.manasmods.tensura.event.PossessionEvent;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.attribute.TensuraAttributeModifierIds;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.Map.Entry;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraftforge.common.MinecraftForge;
import org.apache.commons.lang3.tuple.Triple;

public class PossessionSkill extends Skill {
   public PossessionSkill() {
      super(Skill.SkillType.INTRINSIC);
   }

   private boolean canPossess(LivingEntity target, Player player) {
      if (target.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_POSSESSION)) {
         return false;
      } else if (RaceHelper.isSpiritualLifeForm(target)) {
         return false;
      } else if (player.m_7500_()) {
         return true;
      } else {
         if (target instanceof CloneEntity) {
            CloneEntity clone = (CloneEntity)target;
            if (clone.getSkill() != this) {
               return false;
            }

            if (clone.m_21826_() == player) {
               return true;
            }
         }

         if (SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_NULLIFICATION.get())) {
            return false;
         } else {
            double amplifier = 1.0D;
            if (SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_RESISTANCE.get())) {
               amplifier = 0.5D;
            }

            if (target instanceof Player) {
               Player targetPlayer = (Player)target;
               if (!targetPlayer.m_7500_() && !target.m_5833_()) {
                  int requirement = 0;
                  if ((double)target.m_21223_() < (double)(target.m_21233_() * 0.1F) * amplifier) {
                     ++requirement;
                  }

                  if (TensuraEPCapability.getSpiritualHealth(target) < target.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get()) * 0.10000000149011612D * amplifier) {
                     ++requirement;
                  }

                  if (TensuraEPCapability.getEP(target) < TensuraEPCapability.getEP(player) * 0.25D * amplifier) {
                     ++requirement;
                  }

                  return requirement >= 2;
               }
            }

            if ((double)target.m_21223_() < (double)target.m_21233_() * 0.1D * amplifier) {
               return true;
            } else if (TensuraEPCapability.getSpiritualHealth(target) < target.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get()) * 0.10000000149011612D * amplifier) {
               return true;
            } else {
               return TensuraEPCapability.getEP(target) < TensuraEPCapability.getEP(player) * 0.25D * amplifier;
            }
         }
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity owner) {
      if (owner instanceof Player) {
         Player player = (Player)owner;
         Level level = owner.m_9236_();
         if (!level.m_46472_().equals(TensuraDimensions.HELL) && !level.m_46472_().equals(TensuraDimensions.LABYRINTH)) {
            TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
               if (cap.isSpiritualForm()) {
                  LivingEntity target = SkillHelper.getTargetingEntity(owner, 5.0D, false);
                  if (target == null || !target.m_6084_()) {
                     return;
                  }

                  if (!this.canPossess(target, player)) {
                     player.m_5661_(Component.m_237115_("tensura.targeting.not_allowed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
                     return;
                  }

                  PossessionEvent event = new PossessionEvent(target, player);
                  if (MinecraftForge.EVENT_BUS.post(event)) {
                     return;
                  }

                  ((ServerPlayer)player).m_8999_((ServerLevel)level, target.m_20182_().f_82479_, target.m_20182_().f_82480_, target.m_20182_().f_82481_, target.m_146908_(), target.m_146909_());
                  player.f_19864_ = true;
                  level.m_6263_((Player)null, target.m_20185_(), target.m_20186_(), target.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123765_, 2.0D, 20);
                  this.copyStatsAndSkills(target, player);
                  CloneEntity.copyEffects(target, player);
                  if (target instanceof CloneEntity) {
                     CloneEntity clone = (CloneEntity)target;
                     if (clone.m_21830_(player)) {
                        clone.copyEquipmentsOntoOwner(player, false);
                        clone.resetOwner((UUID)null);
                     }
                  }

                  if (target instanceof Player) {
                     Player targetPlayer = (Player)target;
                     TensuraPlayerCapability.getFrom(targetPlayer).ifPresent((targetCap) -> {
                        targetCap.setSpiritualForm(true);
                        targetCap.applyBaseAttributeModifiers(targetPlayer);
                        targetPlayer.m_150110_().f_35936_ = true;
                        targetPlayer.m_150110_().f_35935_ = true;
                        targetPlayer.m_6885_();
                        SkillStorage storage = SkillAPI.getSkillsFrom(targetPlayer);
                        Iterator var3 = List.copyOf(storage.getLearnedSkills()).iterator();

                        while(var3.hasNext()) {
                           ManasSkillInstance temp = (ManasSkillInstance)var3.next();
                           if (temp.isTemporarySkill()) {
                              storage.forgetSkill(temp);
                           }
                        }

                     });
                     TensuraPlayerCapability.sync(targetPlayer);
                  } else {
                     target.m_217045_();
                     if (!target.m_6469_(TensuraDamageSources.SOUL_SCATTER, target.m_21233_() * 10.0F)) {
                        target.m_6667_(TensuraDamageSources.SOUL_SCATTER);
                        target.m_146870_();
                     } else {
                        target.f_20919_ = 19;
                     }
                  }

                  cap.setSpiritualForm(false);
                  if (!player.m_7500_() && !player.m_5833_()) {
                     player.m_150110_().f_35936_ = false;
                     player.m_150110_().f_35935_ = false;
                     player.m_6885_();
                  }
               } else {
                  double EP = TensuraEPCapability.getEP(owner);
                  EntityType<CloneEntity> type = (EntityType)TensuraEntityTypes.CLONE_DEFAULT.get();
                  CloneEntity clonex = new CloneEntity(type, level);
                  clonex.setLife((Integer)TensuraConfig.INSTANCE.skillsConfig.bodyDespawnTick.get() * 20);
                  clonex.m_21828_(player);
                  clonex.setSkill(this);
                  clonex.setImmobile(true);
                  clonex.m_21153_(owner.m_21223_());
                  clonex.copyEquipments(owner);
                  EquipmentSlot[] var10 = EquipmentSlot.values();
                  int var11 = var10.length;

                  for(int var12 = 0; var12 < var11; ++var12) {
                     EquipmentSlot slot = var10[var12];
                     player.m_8061_(slot, ItemStack.f_41583_);
                  }

                  TensuraEPCapability.setLivingEP(clonex, Math.max(EP / 100.0D, 100.0D));
                  clonex.copyStatsAndSkills(owner, CloneEntity.CopySkill.INTRINSIC, true);
                  clonex.m_7311_(owner.m_20094_());
                  CloneEntity.copyEffects(player, clonex);
                  AttributeInstance cloneHP = clonex.m_21051_(Attributes.f_22276_);
                  Race race = TensuraPlayerCapability.getRace(player);
                  if (cloneHP != null && race != null) {
                     AttributeModifier cloneModifier = cloneHP.m_22111_(TensuraAttributeModifierIds.RACE_BASE_HEALTH_MODIFIER_ID);
                     if (cloneModifier != null) {
                        double raceHP = race.getBaseHealth() - player.m_21172_(Attributes.f_22276_);
                        if (cloneModifier.m_22218_() == raceHP) {
                           clonex.setLife(-1);
                           instance.getOrCreateTag().m_128362_("OriginalBody", clonex.m_20148_());
                           instance.markDirty();
                        }
                     }
                  }

                  clonex.m_7678_(owner.m_20182_().f_82479_, owner.m_20182_().f_82480_, owner.m_20182_().f_82481_, owner.m_146908_(), owner.m_146909_());
                  level.m_7967_(clonex);
                  cap.setSpiritualForm(true);
                  if (!player.m_7500_() && !player.m_5833_()) {
                     player.m_150110_().f_35936_ = true;
                     player.m_150110_().f_35935_ = true;
                     player.m_6885_();
                  }

                  cap.applyBaseAttributeModifiers(player);
                  level.m_6263_((Player)null, owner.m_20185_(), owner.m_20186_(), owner.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
                  SkillStorage storage = SkillAPI.getSkillsFrom(player);
                  Iterator var23 = List.copyOf(storage.getLearnedSkills()).iterator();

                  while(var23.hasNext()) {
                     ManasSkillInstance temp = (ManasSkillInstance)var23.next();
                     if (temp.isTemporarySkill()) {
                        storage.forgetSkill(temp);
                     }
                  }
               }

               TensuraPlayerCapability.sync(player);
            });
         } else {
            player.m_5661_(Component.m_237115_("tensura.ability.activation_failed").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
         }
      }
   }

   private boolean canCopySkill(ManasSkillInstance instance, LivingEntity target, boolean clone) {
      if (clone) {
         return instance.isTemporarySkill();
      } else if (target instanceof Player) {
         Player player = (Player)target;
         return instance.isTemporarySkill() ? true : TensuraPlayerCapability.getIntrinsicList(player).contains(SkillUtils.getSkillId(instance.getSkill()));
      } else {
         return true;
      }
   }

   public void copyStatsAndSkills(LivingEntity target, Player owner) {
      this.applyBaseAttributeModifiers(owner, target);
      owner.m_21153_(Math.max(target.m_21223_(), 0.0F));
      Iterator var3 = target.m_21220_().iterator();

      while(var3.hasNext()) {
         MobEffectInstance instance = (MobEffectInstance)var3.next();
         owner.m_7292_(new MobEffectInstance(instance));
      }

      boolean clone = target instanceof CloneEntity;
      Iterator var8 = List.copyOf(SkillAPI.getSkillsFrom(target).getLearnedSkills()).iterator();

      while(true) {
         ManasSkillInstance instance;
         do {
            do {
               if (!var8.hasNext()) {
                  return;
               }

               instance = (ManasSkillInstance)var8.next();
            } while(!this.canCopySkill(instance, target, clone));
         } while(instance.getMastery() < 0 && instance.getMastery() != -100);

         ManasSkillInstance copy = TensuraSkillInstance.fromNBT(instance.toNBT());
         if (!copy.isTemporarySkill()) {
            copy.setRemoveTime(-2);
         }

         if (SkillUtils.learnSkill(owner, (ManasSkillInstance)copy)) {
            owner.m_5661_(Component.m_237110_("tensura.skill.temporary.success_drain", new Object[]{copy.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), false);
         }
      }
   }

   public void applyBaseAttributeModifiers(LivingEntity owner, LivingEntity target) {
      Entry map;
      double value;
      for(Iterator var3 = this.getStatList().entrySet().iterator(); var3.hasNext(); AttributeModifierHelper.setModifier(owner, (Attribute)map.getKey(), new AttributeModifier((UUID)((Triple)map.getValue()).getLeft(), (String)((Triple)map.getValue()).getMiddle(), value - owner.m_21172_((Attribute)map.getKey()), Operation.ADDITION))) {
         map = (Entry)var3.next();
         AttributeInstance attribute = target.m_21051_((Attribute)map.getKey());
         if (attribute == null) {
            value = (Double)((Triple)map.getValue()).getRight();
         } else {
            value = target.m_21172_((Attribute)map.getKey());
            AttributeModifier raceStat = attribute.m_22111_((UUID)((Triple)map.getValue()).getLeft());
            if (raceStat != null) {
               value += raceStat.m_22218_();
            }
         }

         if (((Attribute)map.getKey()).equals(Attributes.f_22279_) && !(target instanceof Player)) {
            value = value / 0.23D * 0.1D;
         } else if (((Attribute)map.getKey()).equals(Attributes.f_22281_) && !this.isOwnClone(owner, target)) {
            value = Math.min(value, (Double)TensuraConfig.INSTANCE.racesConfig.maxAttackPossession.get());
         } else if (((Attribute)map.getKey()).equals(Attributes.f_22276_) && !this.isOwnClone(owner, target)) {
            value = Math.min(value, (Double)TensuraConfig.INSTANCE.racesConfig.maxHeathPossession.get());
         }
      }

      AttributeInstance jumpStrength = target.m_21051_(Attributes.f_22288_);
      double jump;
      if (jumpStrength == null) {
         AttributeInstance jumpPower = target.m_21051_((Attribute)ManasCoreAttributes.JUMP_POWER.get());
         if (jumpPower == null) {
            jump = 0.42D;
         } else {
            jump = target.m_21172_((Attribute)ManasCoreAttributes.JUMP_POWER.get());
            AttributeModifier raceStat = jumpPower.m_22111_(TensuraAttributeModifierIds.RACE_JUMP_HEIGHT_MODIFIER_ID);
            if (raceStat != null) {
               jump += raceStat.m_22218_();
            }
         }
      } else {
         jump = Math.max(target.m_21172_(Attributes.f_22288_) / 0.7D * 0.42D, 0.42D);
      }

      AttributeModifierHelper.setModifier(owner, (Attribute)ManasCoreAttributes.JUMP_POWER.get(), new AttributeModifier(TensuraAttributeModifierIds.RACE_JUMP_HEIGHT_MODIFIER_ID, "tensura:race_jump_power", jump - owner.m_21172_((Attribute)ManasCoreAttributes.JUMP_POWER.get()), Operation.ADDITION));
   }

   public boolean isOwnClone(LivingEntity owner, LivingEntity target) {
      boolean var10000;
      if (target instanceof CloneEntity) {
         CloneEntity clone = (CloneEntity)target;
         if (clone.m_21830_(owner)) {
            var10000 = true;
            return var10000;
         }
      }

      var10000 = false;
      return var10000;
   }

   private Map<Attribute, Triple<UUID, String, Double>> getStatList() {
      return Map.of(Attributes.f_22276_, Triple.of(TensuraAttributeModifierIds.RACE_BASE_HEALTH_MODIFIER_ID, "tensura:race_base_health", 1.0D), Attributes.f_22281_, Triple.of(TensuraAttributeModifierIds.RACE_ATTACK_DAMAGE_MODIFIER_ID, "tensura:race_attack_damage", 0.1D), Attributes.f_22278_, Triple.of(TensuraAttributeModifierIds.RACE_KNOCKBACK_RESISTANCE_MODIFIER_ID, "tensura:race_knockback_resistance", 0.0D), Attributes.f_22279_, Triple.of(TensuraAttributeModifierIds.RACE_MOVEMENT_SPEED_MODIFIER_ID, "tensura:race_movement_speed", 0.1D));
   }
}
