package com.github.manasmods.tensura.ability.battlewill.melee;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.registry.battlewill.MeleeArts;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;

public class HeavySlashArt extends Battewill {
   public double learningCost() {
      return 80.0D;
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return SkillUtils.isSkillMastered(entity, (ManasSkill)MeleeArts.AURA_SLASH.get());
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 80.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (!SkillHelper.outOfAura(entity, instance)) {
         LivingEntity target = SkillHelper.getTargetingEntity(entity, instance.isMastered(entity) ? 10.0D : 6.0D, false);
         Level level = entity.m_9236_();
         entity.m_21011_(InteractionHand.MAIN_HAND, true);
         double attack = entity.m_21133_(Attributes.f_22281_);
         if (target != null) {
            DamageSource source = DamageSourceHelper.addSkillAndCost(DamageSource.m_19370_(entity), 0.0D, instance);
            if (target.m_6469_(source, (float)(attack * 1.5D))) {
               SkillHelper.knockBack(entity, target, 2.0F);
               this.addMasteryPoint(instance, entity);
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.NEUTRAL, 1.0F, 1.0F);
            }
         } else {
            this.addMasteryPoint(instance, entity);
            this.slash(entity, level, 3.0F, instance);
         }

      }
   }

   private void slash(LivingEntity entity, Level level, float distance, ManasSkillInstance instance) {
      Vec3 target = entity.m_20182_().m_82549_(entity.m_20154_().m_82490_((double)distance));
      Vec3 source = entity.m_20182_().m_82520_(0.0D, (double)entity.m_20192_(), 0.0D);
      Vec3 sourceToTarget = target.m_82546_(source);
      Vec3 normalizes = sourceToTarget.m_82541_();
      level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_215778_, SoundSource.PLAYERS, 1.0F, 1.0F);

      for(int particleIndex = 1; particleIndex < Mth.m_14107_(sourceToTarget.m_82553_()); ++particleIndex) {
         Vec3 particlePos = source.m_82549_(normalizes.m_82490_((double)particleIndex));
         ((ServerLevel)level).m_8767_(ParticleTypes.f_123813_, particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_, 1, 0.0D, 0.0D, 0.0D, 0.0D);
         if (TensuraGameRules.canSkillGrief(level)) {
            SkillHelper.launchBlock(entity, particlePos, 2, 1, 0.3F, 0.2F, (blockState) -> {
               return entity.m_217043_().m_188503_(2) != 1 ? false : blockState.m_204336_(TensuraTags.Blocks.EARTH_MANIPULATING);
            }, (pos) -> {
               return !pos.equals(entity.m_20097_()) && !pos.equals(entity.m_20097_().m_7495_());
            }, instance);
         }

         AABB aabb = (new AABB(new BlockPos(particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_))).m_82400_(2.0D);
         List<LivingEntity> list = level.m_6443_(LivingEntity.class, aabb, (entityData) -> {
            return !entityData.m_7306_(entity);
         });
         if (!list.isEmpty()) {
            Iterator var13 = list.iterator();

            while(var13.hasNext()) {
               LivingEntity living = (LivingEntity)var13.next();
               DamageSource damageSource = DamageSourceHelper.addSkillAndCost(DamageSource.m_19370_(entity), 0.0D, instance);
               if (living.m_6469_(damageSource, (float)(entity.m_21133_(Attributes.f_22281_) * 0.5D))) {
                  TensuraParticleHelper.spawnServerGroundSlamParticle(living, 10, 2.0F);
                  living.m_20184_().m_82520_(0.0D, 0.3D, 0.0D);
               }
            }
         }
      }

   }
}
