package com.github.manasmods.tensura.ability.battlewill.melee;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraftforge.common.ForgeMod;

public class RoaringLionPunchArt extends Battewill {
   public double learningCost() {
      return 2000.0D;
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 10.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (entity.m_21205_().m_41619_()) {
         double auraCost = instance.isMastered(entity) ? 4000.0D : 2000.0D;
         if (entity instanceof Player) {
            Player player = (Player)entity;
            auraCost = Math.min(TensuraPlayerCapability.getAura(player) * 0.1D, auraCost);
         }

         if (!SkillHelper.outOfAura(entity, auraCost)) {
            double reach = 3.0D + entity.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get());
            LivingEntity target = SkillHelper.getTargetingEntity(entity, reach, false);
            if (target != null) {
               Level level = entity.m_9236_();
               float damage = (float)(auraCost / this.auraCost(entity, instance));
               DamageSource source = DamageSourceHelper.addSkillAndCost(DamageSource.m_19370_(entity), 0.0D, instance);
               target.m_6469_(source, damage);
               SkillHelper.knockBack(entity, target, 0.015F * damage);
               instance.addMasteryPoint(entity);
               entity.m_21011_(InteractionHand.MAIN_HAND, true);
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11913_, SoundSource.NEUTRAL, 1.0F, 1.0F);
            }
         }
      }
   }
}
