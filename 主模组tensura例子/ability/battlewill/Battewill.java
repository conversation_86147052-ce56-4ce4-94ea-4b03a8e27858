package com.github.manasmods.tensura.ability.battlewill;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkill;
import java.text.DecimalFormat;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.MinecraftForge;

public class Battewill extends TensuraSkill {
   protected final DecimalFormat roundDouble = new DecimalFormat("#.#");

   public int getMaxMastery() {
      return 400;
   }

   @Nullable
   public MutableComponent getColoredName() {
      MutableComponent name = super.getColoredName();
      return name == null ? null : name.m_130940_(ChatFormatting.RED);
   }

   @Nullable
   public ResourceLocation getSkillIcon() {
      ResourceLocation id = this.getRegistryName();
      if (id == null) {
         return new ResourceLocation("tensura", "textures/temp_textures/item/confused_rimuru.png");
      } else {
         String var10003 = id.m_135815_();
         return new ResourceLocation("tensura", "textures/battlewill/" + var10003.replace('/', '.') + ".png");
      }
   }

   public void addLearnPoint(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.getMastery() < 0) {
         if (SkillHelper.outOfAura(entity, this.learningCost())) {
            return;
         }

         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_6330_(SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

         instance.setCoolDown(10);
         int oldMastery = instance.getMastery();
         int newMastery = oldMastery + SkillUtils.getEarningLearnPoint(instance, entity, false);
         instance.setMastery(Math.min(newMastery, 0));
         instance.markDirty();
         if (oldMastery < 0 && newMastery >= 0) {
            UnlockSkillEvent event = new UnlockSkillEvent(instance, entity);
            if (MinecraftForge.EVENT_BUS.post(event)) {
               instance.setMastery(oldMastery);
               instance.markDirty();
               return;
            }

            if (entity instanceof Player) {
               Player player = (Player)entity;
               player.m_5661_(Component.m_237110_("tensura.skill.acquire_learning", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
            }

            instance.onLearnSkill(entity, event);
         } else if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.learn_points_added", new Object[]{this.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GREEN)), true);
         }
      }

   }
}
