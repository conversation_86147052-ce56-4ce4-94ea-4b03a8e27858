package com.github.manasmods.tensura.ability.battlewill.projectile;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.projectile.AuraBulletProjectile;
import com.github.manasmods.tensura.registry.battlewill.ProjectileArts;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;

public class MagicBulletArt extends Battewill {
   public double learningCost() {
      return 500.0D;
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 25.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      this.shootBullet(instance, entity);
      AuraBulletProjectile bullet = new AuraBulletProjectile(entity.m_9236_(), entity);
      bullet.f_19794_ = true;
      bullet.setSpeed(1.5F);
      bullet.setSize(0.25F);
      bullet.setSkill(instance);
      bullet.setLife(2400);
      bullet.m_20242_(true);
      bullet.m_146884_(this.getBulletPosition(entity, bullet));
      entity.m_9236_().m_7967_(bullet);
      CompoundTag tag = instance.getOrCreateTag();
      tag.m_128405_("BulletID", bullet.m_19879_());
      tag.m_128405_("PowerScale", 0);
      instance.markDirty();
   }

   private Vec3 getBulletPosition(LivingEntity entity, AuraBulletProjectile bullet) {
      double yOff = Math.min((double)(bullet.m_20206_() / 2.0F), (double)entity.m_20192_() - 0.5D);
      Vec3 look = entity.m_20154_().m_82490_((double)(1 + (int)(bullet.getSize() / 10.0F)));
      return entity.m_146892_().m_82549_(look).m_82520_(0.0D, -yOff, 0.0D);
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      Level level = entity.m_9236_();
      CompoundTag tag = instance.getOrCreateTag();
      int id = tag.m_128451_("BulletID");
      Entity idEntity = level.m_6815_(id);
      if (idEntity instanceof AuraBulletProjectile) {
         AuraBulletProjectile bullet = (AuraBulletProjectile)idEntity;
         int max = instance.isMastered(entity) ? 300 : 150;
         bullet.m_146884_(this.getBulletPosition(entity, bullet));
         boolean shouldIncrease = instance.isMastered(entity) ? heldTicks % 2 == 0 : heldTicks % 3 == 0;
         if (heldTicks > 0 && shouldIncrease && tag.m_128451_("PowerScale") < max) {
            tag.m_128405_("PowerScale", tag.m_128451_("PowerScale") + 1);
            instance.markDirty();
            int power = tag.m_128451_("PowerScale") / 10;
            if (power >= 1) {
               bullet.setApCost(this.auraCost(entity, instance) * (double)power);
               bullet.setSkill(instance);
               bullet.setSize(0.4F + 0.1F * (float)power);
               bullet.setColor(bullet.getColorBySize((float)power));
               bullet.setDamage(10.0F * (float)power);
               if (power > 4) {
                  bullet.setExplosionRadius((float)Math.max(power - 3, 8));
               }
            }
         }

         TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_175830_, 1.0D);
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.power_scale", new Object[]{(double)tag.m_128451_("PowerScale") / 10.0D}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), true);
         }

         return true;
      } else {
         tag.m_128405_("BulletID", 0);
         instance.markDirty();
         return false;
      }
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      this.shootBullet(instance, entity);
   }

   private void shootBullet(ManasSkillInstance instance, LivingEntity entity) {
      Level level = entity.m_9236_();
      CompoundTag tag = instance.getOrCreateTag();
      int id = tag.m_128451_("BulletID");
      Entity idEntity = level.m_6815_(id);
      if (idEntity instanceof AuraBulletProjectile) {
         AuraBulletProjectile bullet = (AuraBulletProjectile)idEntity;
         int power = tag.m_128451_("PowerScale") / 10;
         tag.m_128405_("PowerScale", 0);
         instance.markDirty();
         if (power < 1) {
            bullet.m_146870_();
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 3.0F, 1.0F);
         } else {
            double cost = this.auraCost(entity, instance) * (double)power;
            if (SkillHelper.outOfAura(entity, cost)) {
               bullet.m_146870_();
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 3.0F, 1.0F);
            } else {
               entity.m_21011_(InteractionHand.MAIN_HAND, true);
               if (power >= 2) {
                  this.addMasteryPoint(instance, entity);
               }

               bullet.f_19794_ = false;
               bullet.setApCost(cost);
               bullet.shootFromRot(entity.m_20154_());
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
         }
      } else {
         tag.m_128405_("BulletID", 0);
         instance.markDirty();
      }
   }

   public void onSkillMastered(ManasSkillInstance instance, LivingEntity entity) {
      SkillStorage storage = SkillAPI.getSkillsFrom(entity);
      ManasSkill skill = (ManasSkill)ProjectileArts.MAXIMUM_MAGIC_BULLET.get();
      ManasSkillInstance manipulation = new TensuraSkillInstance(skill);
      manipulation.setMastery(-100);
      if (storage.learnSkill(manipulation) && entity instanceof Player) {
         Player player = (Player)entity;
         player.m_5661_(Component.m_237110_("tensura.skill.learn_available", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.DARK_GREEN)), false);
      }

   }
}
