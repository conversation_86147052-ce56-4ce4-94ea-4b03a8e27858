package com.github.manasmods.tensura.ability.battlewill.utility;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.player.Player;

public class BattlewillArt extends Bat<PERSON>will {
   public double learningCost() {
      return 1000.0D;
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (heldTicks > 0 && heldTicks % 30 == 0 && entity instanceof Player) {
         Player player = (Player)entity;
         boolean[] off = new boolean[]{false};
         TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
            double maxMP = player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get());
            double convert = instance.isMastered(entity) ? maxMP * 0.019999999552965164D : maxMP * 0.009999999776482582D;
            if (cap.getMagicule() < convert) {
               player.m_5661_(Component.m_237115_("tensura.skill.lack_magicule").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
               off[0] = true;
            } else {
               cap.setMagicule(cap.getMagicule() - convert);
               double maxAP = player.m_21133_((Attribute)TensuraAttributeRegistry.MAX_AURA.get());
               cap.setAura(Math.min(cap.getAura() + convert, maxAP));
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 0.5F, 1.0F);
               if (cap.getAura() == maxAP) {
                  off[0] = true;
               }

            }
         });
         TensuraPlayerCapability.sync(player);
         if (off[0]) {
            return false;
         }
      }

      if (heldTicks % 60 == 0 && heldTicks > 0) {
         this.addMasteryPoint(instance, entity);
      }

      TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_175830_, 1.0D);
      return true;
   }
}
