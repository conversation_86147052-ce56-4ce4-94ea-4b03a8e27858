package com.github.manasmods.tensura.ability.battlewill.utility;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.battlewill.Battewill;
import com.github.manasmods.tensura.registry.battlewill.UtilityArts;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class HazeArt extends Battewill {
   public double learningCost() {
      return 200.0D;
   }

   public boolean meetEPRequirement(Player entity, double newEP) {
      return SkillUtils.isSkillMastered(entity, (ManasSkill)UtilityArts.FORMHIDE.get());
   }

   public double auraCost(LivingEntity entity, ManasSkillInstance instance) {
      return 20.0D;
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return !instance.isMastered(entity) ? false : instance.isToggled();
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      if (SkillHelper.outOfAura(entity, this.magiculeCost(entity, instance) * 5.0D)) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237110_("tensura.skill.lack_aura.toggled_off", new Object[]{instance.getSkill().getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
         }

         instance.setToggled(false);
      } else {
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.PRESENCE_CONCEALMENT.get(), 200, 1, false, false, false));
      }
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (instance.isMastered(entity)) {
         if (instance.isToggled()) {
            instance.setToggled(false);
            entity.m_21195_((MobEffect)TensuraMobEffects.PRESENCE_CONCEALMENT.get());
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11738_, SoundSource.PLAYERS, 0.5F, 1.0F);
         } else {
            instance.setToggled(true);
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.PRESENCE_CONCEALMENT.get(), 200, 1, false, false, false));
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11736_, SoundSource.PLAYERS, 0.5F, 1.0F);
         }

      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      if (instance.isMastered(entity)) {
         return false;
      } else if (heldTicks % 20 == 0 && SkillHelper.outOfAura(entity, instance)) {
         return false;
      } else {
         if (heldTicks % 100 == 0 && heldTicks > 0) {
            this.addMasteryPoint(instance, entity);
         }

         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.PRESENCE_CONCEALMENT.get(), 5, 1, false, false, false));
         return true;
      }
   }
}
