package com.github.manasmods.tensura.ability.magic.spiritual;

import com.github.manasmods.tensura.ability.magic.Magic;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import java.util.Arrays;
import java.util.Comparator;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;

public class SpiritualMagic extends Magic {
   private final MagicElemental elemental;
   private final SpiritualMagic.SpiritLevel level;

   public SpiritualMagic(MagicElemental elemental, SpiritualMagic.SpiritLevel level) {
      super(Magic.MagicType.SPIRITUAL);
      this.elemental = elemental;
      this.level = level;
   }

   @Nullable
   public MutableComponent getColoredName() {
      MutableComponent name = super.getColoredName();
      return name == null ? null : name.m_130940_(this.getElemental().getChatFormatting());
   }

   public int getMaxMastery() {
      short var10000;
      switch(this.getLevel()) {
      case LESSER:
         var10000 = 200;
         break;
      case MEDIUM:
         var10000 = 500;
         break;
      case GREATER:
         var10000 = 1000;
         break;
      case LORD:
         var10000 = 2000;
         break;
      default:
         throw new IncompatibleClassChangeError();
      }

      return var10000;
   }

   public MagicElemental getElemental() {
      return this.elemental;
   }

   public SpiritualMagic.SpiritLevel getLevel() {
      return this.level;
   }

   public static enum SpiritLevel {
      LESSER(1, "lesser", ChatFormatting.YELLOW),
      MEDIUM(2, "medium", ChatFormatting.GOLD),
      GREATER(3, "greater", ChatFormatting.RED),
      LORD(4, "lord", ChatFormatting.DARK_RED);

      private static final SpiritualMagic.SpiritLevel[] BY_ID = (SpiritualMagic.SpiritLevel[])Arrays.stream(values()).sorted(Comparator.comparingInt(SpiritualMagic.SpiritLevel::getId)).toArray((x$0) -> {
         return new SpiritualMagic.SpiritLevel[x$0];
      });
      private final int id;
      private final String namespace;
      private final ChatFormatting chatFormatting;

      public static SpiritualMagic.SpiritLevel byId(int id) {
         if (id % 4 == 0) {
            return LORD;
         } else if (id % 3 == 0) {
            return GREATER;
         } else {
            return id % 2 == 0 ? MEDIUM : LESSER;
         }
      }

      public MutableComponent getName() {
         return Component.m_237115_("tensura.magic.spiritual.level." + this.namespace);
      }

      public MutableComponent getSpiritName(MagicElemental elemental) {
         return this.equals(LORD) ? Component.m_237110_("tensura.magic.spiritual.spirit_name.lord", new Object[]{this.getName(), elemental.getName()}) : Component.m_237110_("tensura.magic.spiritual.spirit_name", new Object[]{this.getName(), elemental.getName()});
      }

      public int getId() {
         return this.id;
      }

      public String getNamespace() {
         return this.namespace;
      }

      public ChatFormatting getChatFormatting() {
         return this.chatFormatting;
      }

      private SpiritLevel(int id, String namespace, ChatFormatting chatFormatting) {
         this.id = id;
         this.namespace = namespace;
         this.chatFormatting = chatFormatting;
      }

      // $FF: synthetic method
      private static SpiritualMagic.SpiritLevel[] $values() {
         return new SpiritualMagic.SpiritLevel[]{LESSER, MEDIUM, GREATER, LORD};
      }
   }
}
