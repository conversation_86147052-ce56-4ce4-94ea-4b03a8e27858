package com.github.manasmods.tensura.ability.magic.spiritual.darkness;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class ShadowBindMagic extends SpiritualMagic {
   public ShadowBindMagic() {
      super(MagicElemental.DARKNESS, SpiritualMagic.SpiritLevel.MEDIUM);
   }

   public int defaultCast() {
      return 40;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 500.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            LivingEntity target = SkillHelper.getTargetingEntity(entity, 10.0D, false, true);
            if (target != null) {
               entity.m_21011_(InteractionHand.MAIN_HAND, true);
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12054_, SoundSource.PLAYERS, 1.0F, 1.0F);
               TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123765_);
               if (this.inShadow(target)) {
                  if (!SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.DARKNESS_ATTACK_NULLIFICATION.get())) {
                     this.addMasteryPoint(instance, entity);
                     int bindLevel = 1;
                     if (SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.DARKNESS_ATTACK_RESISTANCE.get())) {
                        bindLevel = 0;
                     }

                     if (instance.isMastered(entity)) {
                        bindLevel += 2;
                     }

                     SkillHelper.checkThenAddEffectSource(target, entity, new MobEffectInstance((MobEffect)TensuraMobEffects.SHADOW_BIND.get(), 200, bindLevel, true, true, false));
                  }
               }
            }
         }
      }
   }

   private boolean inShadow(LivingEntity entity) {
      if (!entity.m_6084_()) {
         return false;
      } else {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (player.m_150110_().f_35934_) {
               return false;
            }
         }

         if (entity.f_19853_.m_46462_()) {
            return true;
         } else {
            return entity.m_213856_() < 0.5F || !entity.f_19853_.m_45527_(new BlockPos(entity.m_146892_()));
         }
      }
   }
}
