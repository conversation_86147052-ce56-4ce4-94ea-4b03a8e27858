package com.github.manasmods.tensura.ability.magic.spiritual.space;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.ability.skill.extra.SpatialMotionSkill;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Block;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.BlockHitResult;

public class TeleportMagic extends SpiritualMagic {
   public TeleportMagic() {
      super(MagicElemental.SPACE, SpiritualMagic.SpiritLevel.MEDIUM);
   }

   public int defaultCast() {
      return 5;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 150.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (MobEffectHelper.noTeleportation(entity)) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237115_("tensura.skill.spatial_blockade").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
         }

      } else if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         Level level = entity.m_9236_();
         BlockHitResult result = SkillHelper.getPlayerPOVHitResult(level, entity, Fluid.NONE, Block.COLLIDER, this.isMastered(instance, entity) ? 50.0D : 30.0D);
         BlockPos pos = result.m_82425_().m_121945_(result.m_82434_());
         if (level.m_8055_(pos).m_60713_((net.minecraft.world.level.block.Block)TensuraBlocks.LABYRINTH_BARRIER_BLOCK.get())) {
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
         } else if (!SkillHelper.outOfMagicule(entity, this.magiculeCost(entity, instance) + 10.0D * Math.sqrt(entity.m_20275_((double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_())))) {
            SpatialMotionSkill.warp(entity, (double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_());
            this.addMasteryPoint(instance, entity);
            instance.setCoolDown(instance.isMastered(entity) ? 1 : 3);
            instance.getOrCreateTag().m_128405_("HeldTicks", 0);
            instance.markDirty();
         }
      }
   }
}
