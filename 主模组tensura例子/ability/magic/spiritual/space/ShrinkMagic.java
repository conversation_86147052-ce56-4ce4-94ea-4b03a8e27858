package com.github.manasmods.tensura.ability.magic.spiritual.space;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;

public class ShrinkMagic extends SpiritualMagic {
   protected static final UUID SHRINK = UUID.fromString("bdfb0968-3db1-11ee-be56-0242ac120002");
   private final List<Attribute> attributeList;

   public ShrinkMagic() {
      super(MagicElemental.SPACE, SpiritualMagic.SpiritLevel.MEDIUM);
      this.attributeList = List.of(Attributes.f_22281_, Attributes.f_22278_, Attributes.f_22284_, Attributes.f_22285_);
   }

   public int defaultCast() {
      return 60;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 300.0D;
   }

   private boolean isShrunk(LivingEntity entity) {
      AttributeInstance size = entity.m_21051_((Attribute)TensuraAttributeRegistry.SIZE.get());
      if (size == null) {
         return false;
      } else {
         return size.m_22111_(SHRINK) != null;
      }
   }

   public boolean canTick(ManasSkillInstance instance, LivingEntity entity) {
      return super.canTick(instance, entity) ? true : this.isShrunk(entity);
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      return this.isShrunk(entity) ? false : super.onHeld(instance, entity, heldTicks);
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.isShrunk(entity)) {
         instance.onToggleOff(entity);
      } else if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            instance.onToggleOn(entity);
         }
      }
   }

   public void onTick(ManasSkillInstance instance, LivingEntity entity) {
      super.onTick(instance, entity);
      if (this.isShrunk(entity)) {
         entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FRAGILITY.get(), 200, instance.isMastered(entity) ? 2 : 4, false, false, false));
         CompoundTag tag = instance.getOrCreateTag();
         int shrunkTime = tag.m_128451_("ShrunkTime");
         tag.m_128405_("ShrunkTime", shrunkTime + 5);
         if (shrunkTime + 5 >= (instance.isMastered(entity) ? 120 : 25)) {
            instance.onToggleOff(entity);
         }

         instance.markDirty();
      }
   }

   public void onToggleOn(ManasSkillInstance instance, LivingEntity entity) {
      float size = 0.2F;
      float sizeMultiplier = RaceHelper.getSizeMultiplier(entity);
      size = (float)(Math.max((double)(size * sizeMultiplier), (Double)TensuraConfig.INSTANCE.attributeConfig.minimumSize.get()) / (double)sizeMultiplier);
      AttributeModifier sizeModifier = new AttributeModifier(SHRINK, "Shrink Size", (double)(size - 1.0F), Operation.MULTIPLY_TOTAL);
      AttributeInstance sizeInstance = entity.m_21051_((Attribute)TensuraAttributeRegistry.SIZE.get());
      if (sizeInstance != null && !sizeInstance.m_22109_(sizeModifier)) {
         sizeInstance.m_22125_(sizeModifier);
      }

      AttributeModifier modifier = new AttributeModifier(SHRINK, "Shrink Size", -0.8D, Operation.MULTIPLY_TOTAL);
      Iterator var8 = this.attributeList.iterator();

      while(var8.hasNext()) {
         Attribute attribute = (Attribute)var8.next();
         AttributeInstance attributeInstance = entity.m_21051_(attribute);
         if (attributeInstance != null && !attributeInstance.m_22109_(modifier)) {
            attributeInstance.m_22125_(modifier);
         }
      }

      this.addMasteryPoint(instance, entity);
      entity.f_19853_.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 0.5F, 1.0F);
   }

   public void onToggleOff(ManasSkillInstance instance, LivingEntity entity) {
      AttributeInstance sizeInstance = entity.m_21051_((Attribute)TensuraAttributeRegistry.SIZE.get());
      if (sizeInstance != null) {
         sizeInstance.m_22120_(SHRINK);
      }

      Iterator var4 = this.attributeList.iterator();

      while(var4.hasNext()) {
         Attribute attribute = (Attribute)var4.next();
         AttributeInstance attributeInstance = entity.m_21051_(attribute);
         if (attributeInstance != null) {
            attributeInstance.m_22120_(SHRINK);
         }
      }

      instance.getOrCreateTag().m_128405_("ShrunkTime", 0);
      instance.markDirty();
      entity.f_19853_.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 0.5F, 1.0F);
   }
}
