package com.github.manasmods.tensura.ability.magic.spiritual.space;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;

public class SwipeMagic extends SpiritualMagic {
   public SwipeMagic() {
      super(MagicElemental.SPACE, SpiritualMagic.SpiritLevel.GREATER);
   }

   public int defaultCast() {
      return 60;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 5000.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (MobEffectHelper.noTeleportation(entity)) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            player.m_5661_(Component.m_237115_("tensura.skill.spatial_blockade").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
         }

      } else if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            this.addMasteryPoint(instance, entity);
            Level level = entity.m_9236_();
            int radius = instance.isMastered(entity) ? 20 : 15;
            Vec3 target = entity.m_20182_().m_82549_(entity.m_20154_().m_82490_((double)radius));
            Vec3 source = entity.m_146892_();
            Vec3 offSetToTarget = target.m_82546_(source);
            Vec3 normalizes = offSetToTarget.m_82541_();
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11852_, SoundSource.PLAYERS, 0.5F, 1.0F);
            boolean touchedEntity = false;
            float damage = instance.isMastered(entity) ? 500.0F : 200.0F;

            for(int i = 1; i < Mth.m_14107_(offSetToTarget.m_82553_()); ++i) {
               Vec3 particlePos = source.m_82549_(normalizes.m_82490_((double)i));
               AABB aabb = (new AABB(new BlockPos(particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_))).m_82400_(0.5D);
               List<LivingEntity> list = level.m_6443_(LivingEntity.class, aabb, (entityData) -> {
                  return !entityData.m_7306_(entity);
               });
               if (!list.isEmpty()) {
                  Iterator var16 = list.iterator();

                  while(var16.hasNext()) {
                     LivingEntity living = (LivingEntity)var16.next();
                     DamageSource damagesource = TensuraDamageSources.elementalAttack("tensura.space_attack", entity, true);
                     if (living.m_6469_(this.sourceWithMP(damagesource, entity, instance).setSpatial(), damage)) {
                        living.m_19877_();
                        living.m_20219_(entity.m_20182_().m_82549_(normalizes.m_82490_(2.0D)));
                        entity.f_19812_ = true;
                        entity.f_19864_ = true;
                        TensuraParticleHelper.addServerParticlesAroundSelf(living, ParticleTypes.f_123789_);
                        TensuraParticleHelper.addServerParticlesAroundSelf(living, ParticleTypes.f_123810_);
                        touchedEntity = true;
                     }
                  }
               }
            }

            if (!touchedEntity) {
               entity.m_183634_();
               entity.m_19877_();
               entity.m_20219_(target);
               entity.f_19812_ = true;
               level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
               TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123789_);
               TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123810_);
            }

         }
      }
   }
}
