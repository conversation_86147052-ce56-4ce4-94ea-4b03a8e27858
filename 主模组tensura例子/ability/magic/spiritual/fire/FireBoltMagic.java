package com.github.manasmods.tensura.ability.magic.spiritual.fire;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.entity.magic.projectile.FireBallProjectile;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class FireBoltMagic extends SpiritualMagic {
   public FireBoltMagic() {
      super(MagicElemental.FLAME, SpiritualMagic.SpiritLevel.MEDIUM);
   }

   public int defaultCast() {
      return 40;
   }

   public int masteryCast() {
      return 20;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 100.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            this.addMasteryPoint(instance, entity);
            FireBallProjectile fireBall = new FireBallProjectile(entity.m_9236_(), entity);
            fireBall.setBurnTicks(10);
            fireBall.setSpeed(1.2F);
            fireBall.setDamage(40.0F);
            fireBall.setMpCost(this.magiculeCost(entity, instance));
            fireBall.setSkill(instance);
            fireBall.setSpiritAttack(true);
            fireBall.m_20242_(true);
            fireBall.setPosAndShoot(entity);
            entity.m_9236_().m_7967_(fireBall);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11705_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }
      }
   }
}
