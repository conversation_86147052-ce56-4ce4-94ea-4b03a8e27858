package com.github.manasmods.tensura.ability.magic.spiritual.water;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.entity.magic.barrier.BlizzardEntity;
import com.github.manasmods.tensura.entity.magic.projectile.IceLanceProjectile;
import java.util.Iterator;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class BlizzardMagic extends SpiritualMagic {
   public BlizzardMagic() {
      super(MagicElemental.WATER, SpiritualMagic.SpiritLevel.GREATER);
   }

   public int defaultCast() {
      return 160;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 10000.0D;
   }

   public void onPressed(ManasSkillInstance instance, LivingEntity entity) {
      if (this.hasBlizzard(entity)) {
         if (!entity.m_6144_()) {
            if (!SkillHelper.outOfMagicule(entity, 2000.0D)) {
               entity.m_21011_(InteractionHand.MAIN_HAND, true);
               this.addMasteryPoint(instance, entity);
               instance.setCoolDown(10);
               IceLanceProjectile iceLance = new IceLanceProjectile(entity.m_9236_(), entity);
               iceLance.setSize(3.0F);
               iceLance.setSpeed(2.0F);
               iceLance.setDamage(250.0F);
               iceLance.setMpCost(this.magiculeCost(entity, instance));
               iceLance.setSkill(instance);
               iceLance.setSpiritAttack(true);
               iceLance.setPosAndShoot(entity);
               entity.m_9236_().m_7967_(iceLance);
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12049_, SoundSource.PLAYERS, 1.0F, 1.0F);
               instance.getOrCreateTag().m_128405_("HeldTicks", 0);
               instance.markDirty();
            }
         } else {
            Iterator var3 = entity.m_9236_().m_6443_(BlizzardEntity.class, entity.m_20191_(), (blizzardx) -> {
               return blizzardx.m_37282_() == entity;
            }).iterator();

            while(var3.hasNext()) {
               BlizzardEntity blizzard = (BlizzardEntity)var3.next();
               blizzard.m_146870_();
            }

         }
      }
   }

   public void addHeldAttributeModifiers(ManasSkillInstance instance, LivingEntity entity) {
      if (!this.hasBlizzard(entity)) {
         super.addHeldAttributeModifiers(instance, entity);
      }
   }

   public boolean onHeld(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      return this.hasBlizzard(entity) ? false : super.onHeld(instance, entity, heldTicks);
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (!entity.m_6144_()) {
         if (!this.hasBlizzard(entity)) {
            if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
               if (!SkillHelper.outOfMagicule(entity, instance)) {
                  entity.m_21011_(InteractionHand.MAIN_HAND, true);
                  this.addMasteryPoint(instance, entity);
                  BlizzardEntity blizzard = new BlizzardEntity(entity.m_9236_(), entity);
                  blizzard.setLife(instance.isMastered(entity) ? 3200 : 2000);
                  blizzard.setRadius(15.0F);
                  blizzard.setDamage(30.0F);
                  blizzard.setMpCost(this.magiculeCost(entity, instance));
                  blizzard.setSkill(instance);
                  blizzard.m_6034_(entity.m_20185_(), entity.m_20186_() + (double)(entity.m_20206_() / 2.0F), entity.m_20189_());
                  entity.m_9236_().m_7967_(blizzard);
                  entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12090_, SoundSource.PLAYERS, 1.0F, 1.0F);
               }
            }
         }
      }
   }

   private boolean hasBlizzard(LivingEntity owner) {
      return !owner.m_9236_().m_6443_(BlizzardEntity.class, owner.m_20191_(), (blizzard) -> {
         return blizzard.m_37282_() == owner;
      }).isEmpty();
   }
}
