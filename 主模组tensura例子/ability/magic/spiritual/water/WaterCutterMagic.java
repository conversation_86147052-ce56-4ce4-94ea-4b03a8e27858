package com.github.manasmods.tensura.ability.magic.spiritual.water;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.entity.magic.skill.WaterBladeProjectile;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class WaterCutterMagic extends SpiritualMagic {
   public WaterCutterMagic() {
      super(MagicElemental.WATER, SpiritualMagic.SpiritLevel.MEDIUM);
   }

   public int defaultCast() {
      return 30;
   }

   public int masteryCast() {
      return 10;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 50.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            if (!WaterMagic.isWaterEvaporated(entity, entity.f_19853_)) {
               this.addMasteryPoint(instance, entity);
               WaterBladeProjectile blade = new WaterBladeProjectile(entity.m_9236_(), entity);
               blade.setSpeed(2.0F);
               blade.setDamage(30.0F);
               blade.setMpCost(this.magiculeCost(entity, instance));
               blade.setSkill(instance);
               blade.setSpiritAttack(true);
               blade.setPosAndShoot(entity);
               entity.m_9236_().m_7967_(blade);
               entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11687_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
         }
      }
   }
}
