package com.github.manasmods.tensura.ability.magic.spiritual.wind;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import java.util.Iterator;
import java.util.List;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.phys.Vec3;

public class WindMagic extends SpiritualMagic {
   public WindMagic() {
      super(MagicElemental.WIND, SpiritualMagic.SpiritLevel.LESSER);
   }

   public boolean isInstant(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isMastered(entity);
   }

   public int defaultCast() {
      return 20;
   }

   public int masteryCast() {
      return 1;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 25.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            this.addMasteryPoint(instance, entity);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.GUST.get(), 2.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(entity, (ParticleOptions)TensuraParticles.SMALL_GUST.get(), 2.0D);
            List<LivingEntity> list = entity.m_9236_().m_6443_(LivingEntity.class, entity.m_20191_().m_82400_(3.0D), (living) -> {
               return !living.m_7306_(entity) && living.m_6084_();
            });
            if (!list.isEmpty()) {
               Iterator var5 = list.iterator();

               label31:
               while(true) {
                  LivingEntity target;
                  Player player;
                  do {
                     if (!var5.hasNext()) {
                        break label31;
                     }

                     target = (LivingEntity)var5.next();
                     if (!(target instanceof Player)) {
                        break;
                     }

                     player = (Player)target;
                  } while(player.m_150110_().f_35934_);

                  SkillHelper.knockBack(entity, target, 1.5F);
               }
            }

            entity.m_183634_();
            Vec3 delta = entity.m_20184_();
            double dy = delta.f_82480_ <= 0.0D ? 1.0D : delta.f_82480_ + 1.0D;
            entity.m_20256_(new Vec3(delta.m_7096_(), dy, delta.m_7094_()));
            entity.f_19812_ = true;
            entity.f_19864_ = true;
         }
      }
   }
}
