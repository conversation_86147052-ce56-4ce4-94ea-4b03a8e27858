package com.github.manasmods.tensura.ability.magic.spiritual.wind;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.entity.magic.projectile.WindBladeProjectile;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class WindBladeMagic extends SpiritualMagic {
   public WindBladeMagic() {
      super(MagicElemental.WIND, SpiritualMagic.SpiritLevel.MEDIUM);
   }

   public int defaultCast() {
      return 40;
   }

   public int masteryCast() {
      return 20;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 100.0D;
   }

   public boolean canIgnoreCoolDown(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isMastered(entity);
   }

   public int castingTime(ManasSkillInstance instance, LivingEntity entity) {
      return this.doubleBlade(instance, entity) ? 0 : super.castingTime(instance, entity);
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            this.addMasteryPoint(instance, entity);
            WindBladeProjectile blade = new WindBladeProjectile(entity.m_9236_(), entity);
            blade.setSpeed(1.5F);
            blade.setDamage(20.0F);
            blade.setKnockForce(2.0F);
            blade.setMpCost(this.magiculeCost(entity, instance));
            blade.setSkill(instance);
            blade.setSpiritAttack(true);
            blade.m_20242_(true);
            blade.setPosAndShoot(entity);
            entity.m_9236_().m_7967_(blade);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11687_, SoundSource.PLAYERS, 1.0F, 1.0F);
            if (instance.isMastered(entity)) {
               if (instance.onCoolDown()) {
                  instance.setCoolDown(0);
               } else {
                  instance.setCoolDown(2);
               }
            }

         }
      }
   }

   private boolean doubleBlade(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isMastered(entity) && instance.onCoolDown();
   }
}
