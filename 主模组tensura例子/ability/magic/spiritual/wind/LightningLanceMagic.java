package com.github.manasmods.tensura.ability.magic.spiritual.wind;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.entity.magic.projectile.LightningLanceProjectile;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class LightningLanceMagic extends SpiritualMagic {
   public LightningLanceMagic() {
      super(MagicElemental.WIND, SpiritualMagic.SpiritLevel.MEDIUM);
   }

   public int defaultCast() {
      return 40;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 125.0D;
   }

   public void onRelease(ManasSkillInstance instance, LivingEntity entity, int heldTicks) {
      super.onRelease(instance, entity, heldTicks);
      if (this.getHeldTicks(instance) >= this.castingTime(instance, entity)) {
         if (!SkillHelper.outOfMagicule(entity, instance)) {
            entity.m_21011_(InteractionHand.MAIN_HAND, true);
            this.addMasteryPoint(instance, entity);
            LightningLanceProjectile lance = new LightningLanceProjectile(entity.m_9236_(), entity);
            lance.setEffectRange(-1.0F);
            lance.setSpeed(1.75F);
            lance.setDamage(instance.isMastered(entity) ? 40.0F : 30.0F);
            lance.setMpCost(this.magiculeCost(entity, instance));
            lance.setSkill(instance);
            lance.setSpiritAttack(true);
            lance.m_20242_(true);
            lance.setPosAndShoot(entity);
            entity.m_9236_().m_7967_(lance);
            entity.m_9236_().m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11687_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }
      }
   }
}
