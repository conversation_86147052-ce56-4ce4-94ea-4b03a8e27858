package com.github.manasmods.tensura.ability.magic.summon;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.Attributes;
import org.jetbrains.annotations.NotNull;

public class SummonGreaterElementalMagic extends SummonElementalMagic {
   public SummonGreaterElementalMagic() {
      super(SpiritualMagic.SpiritLevel.GREATER);
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 3000.0D;
   }

   protected void addAdditionalSummonData(ManasSkillInstance instance, LivingEntity entity, TamableAnimal summon) {
      super.addAdditionalSummonData(instance, entity, summon);
      AttributeInstance attack = summon.m_21051_(Attributes.f_22281_);
      if (attack != null) {
         attack.m_22100_(attack.m_22115_() / 2.0D);
      }

      AttributeInstance health = summon.m_21051_(Attributes.f_22276_);
      if (health != null) {
         health.m_22100_(health.m_22115_() / 2.0D);
      }

      summon.m_21153_(summon.m_21233_());
   }

   @NotNull
   public Component getModeName(int mode) {
      Object var10000;
      switch(mode) {
      case 1:
         var10000 = Component.m_237115_("entity.tensura.war_gnome");
         break;
      case 2:
         var10000 = Component.m_237115_("entity.tensura.ifrit");
         break;
      case 3:
         var10000 = Component.m_237115_("entity.tensura.akash");
         break;
      case 4:
         var10000 = Component.m_237115_("entity.tensura.undine");
         break;
      case 5:
         var10000 = Component.m_237115_("entity.tensura.sylphide");
         break;
      default:
         var10000 = super.getModeName(mode);
      }

      return (Component)var10000;
   }

   protected EntityType<? extends TamableAnimal> getSummonedType(ManasSkillInstance instance) {
      EntityType var10000;
      switch(instance.getMode()) {
      case 1:
         var10000 = (EntityType)TensuraEntityTypes.WAR_GNOME.get();
         break;
      case 2:
         var10000 = (EntityType)TensuraEntityTypes.IFRIT.get();
         break;
      case 3:
         var10000 = (EntityType)TensuraEntityTypes.AKASH.get();
         break;
      case 4:
         var10000 = (EntityType)TensuraEntityTypes.UNDINE.get();
         break;
      case 5:
         var10000 = (EntityType)TensuraEntityTypes.SYLPHIDE.get();
         break;
      default:
         var10000 = null;
      }

      return var10000;
   }
}
