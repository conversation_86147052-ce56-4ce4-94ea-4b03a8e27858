package com.github.manasmods.tensura.ability.magic.summon;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.entity.HoundDogEntity;
import com.github.manasmods.tensura.entity.variant.HoundDogVariant;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

public class SummonHoundDogMagic extends SummoningMagic<HoundDogEntity> {
   public int defaultCast() {
      return 100;
   }

   public int masteryCast() {
      return 60;
   }

   public double magiculeCost(LivingEntity entity, ManasSkillInstance instance) {
      return 50.0D;
   }

   protected int getSuccessCooldown(ManasSkillInstance instance, LivingEntity entity) {
      return instance.isMastered(entity) ? 3600 : 6000;
   }

   protected void removeExistingSummon(ManasSkillInstance instance, LivingEntity entity) {
      HoundDogEntity dog = (HoundDogEntity)SkillHelper.getTargetingEntity(HoundDogEntity.class, entity, 30.0D, 0.2D, false);
      if (dog != null) {
         if (dog.m_21830_(entity)) {
            if (dog.getSummoningTick() >= 0) {
               dog.m_6469_(TensuraDamageSources.noEnergySource(entity), dog.m_21233_());
               instance.setCoolDown(0);
            }
         }
      }
   }

   protected void addAdditionalSummonData(ManasSkillInstance instance, LivingEntity entity, HoundDogEntity summon) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         summon.m_21828_(player);
      }

      summon.setSummoningTick(6000);
      summon.setVariant(HoundDogVariant.EVOLVED);
      summon.m_217045_();
   }

   protected EntityType<? extends HoundDogEntity> getSummonedType(ManasSkillInstance instance) {
      return (EntityType)TensuraEntityTypes.HOUND_DOG.get();
   }

   protected ResourceLocation getSummoningFxLocation(ManasSkillInstance instance) {
      return new ResourceLocation("tensura:dark_circle");
   }

   protected ParticleOptions getSummoningParticle(ManasSkillInstance instance) {
      return ParticleTypes.f_123784_;
   }

   protected SoundEvent getSummoningSound(ManasSkillInstance instance) {
      return SoundEvents.f_12049_;
   }

   protected SoundEvent getFailSound(ManasSkillInstance instance) {
      return SoundEvents.f_12618_;
   }
}
