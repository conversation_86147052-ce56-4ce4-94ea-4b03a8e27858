package com.github.manasmods.tensura.ability.magic;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;

public enum MagicElemental {
   DARKNESS(0, "darkness", ChatFormatting.DARK_GRAY, (ManasSkill)null),
   EARTH(1, "earth", ChatFormatting.GREEN, (ManasSkill)ExtraSkills.EARTH_MANIPULATION.get()),
   FLAME(2, "fire", ChatFormatting.RED, (ManasSkill)ExtraSkills.FLAME_MANIPULATION.get()),
   LIGHT(3, "light", ChatFormatting.YELLOW, (ManasSkill)null),
   SPACE(4, "space", ChatFormatting.LIGHT_PURPLE, (ManasSkill)ExtraSkills.SPATIAL_MANIPULATION.get()),
   WATER(5, "water", ChatFormatting.AQUA, (ManasSkill)ExtraSkills.WATER_MANIPULATION.get()),
   WIND(6, "wind", ChatFormatting.DARK_AQUA, (ManasSkill)ExtraSkills.WIND_MANIPULATION.get());

   private static final MagicElemental[] BY_ID = (MagicElemental[])Arrays.stream(values()).sorted(Comparator.comparingInt(MagicElemental::getId)).toArray((x$0) -> {
      return new MagicElemental[x$0];
   });
   private final int id;
   private final String namespace;
   private final ChatFormatting chatFormatting;
   @Nullable
   private final ManasSkill manipulation;

   private MagicElemental(int id, String namespace, ChatFormatting chatFormatting, ManasSkill manipulation) {
      this.id = id;
      this.namespace = namespace;
      this.chatFormatting = chatFormatting;
      this.manipulation = manipulation;
   }

   public static MagicElemental byId(int id) {
      return BY_ID[id % BY_ID.length];
   }

   public MutableComponent getName() {
      return Component.m_237115_("tensura.magic.elemental." + this.namespace);
   }

   public static List<MagicElemental> getCommonElementals() {
      return List.of(EARTH, FLAME, SPACE, WATER, WIND);
   }

   public int getId() {
      return this.id;
   }

   public String getNamespace() {
      return this.namespace;
   }

   public ChatFormatting getChatFormatting() {
      return this.chatFormatting;
   }

   @Nullable
   public ManasSkill getManipulation() {
      return this.manipulation;
   }

   // $FF: synthetic method
   private static MagicElemental[] $values() {
      return new MagicElemental[]{DARKNESS, EARTH, FLAME, LIGHT, SPACE, WATER, WIND};
   }
}
