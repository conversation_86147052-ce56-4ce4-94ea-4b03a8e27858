package com.github.manasmods.tensura.ability;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingDeathEvent;

public class TensuraSkillInstance extends ManasSkillInstance {
   public TensuraSkillInstance(ManasSkill skill) {
      super(skill);
   }

   public TensuraSkillInstance clone() {
      TensuraSkillInstance clone = new TensuraSkillInstance(this.getSkill());
      clone.deserialize(this.toNBT());
      return clone;
   }

   public boolean canInteractSkill(LivingEntity entity) {
      return this.getSkill().canInteractSkill(this, entity);
   }

   public double magiculeCost(LivingEntity entity) {
      return ((TensuraSkill)this.getSkill()).magiculeCost(entity, this);
   }

   public double auraCost(LivingEntity entity) {
      return ((TensuraSkill)this.getSkill()).auraCost(entity, this);
   }

   public void onNumberKeyPress(Player player, int keyNumber) {
      ((TensuraSkill)this.getSkill()).onNumberKeyPress(this, player, keyNumber);
   }

   public void onSubordinateDeath(LivingEntity owner, LivingDeathEvent e) {
      ((TensuraSkill)this.getSkill()).onSubordinateDeath(this, owner, e);
   }
}
