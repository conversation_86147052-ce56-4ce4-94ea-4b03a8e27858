package com.github.manasmods.tensura.ability;

import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.tensura.api.entity.subclass.ILivingPartEntity;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.event.EnergyDrainEvent;
import com.github.manasmods.tensura.event.EnergyRegenerateTickEvent;
import com.github.manasmods.tensura.event.SkillGriefEvent;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.server.level.TicketType;
import net.minecraft.util.Mth;
import net.minecraft.util.RandomSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobType;
import net.minecraft.world.entity.MoverType;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.WrappedGoal;
import net.minecraft.world.entity.item.FallingBlockEntity;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.level.ChunkPos;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Block;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.MinecraftForge;

public class SkillHelper {
   public static Component comingSoon(String name) {
      return Component.m_237110_("tooltip.tensura.coming_soon_feature", new Object[]{name}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD));
   }

   public static Component comingSoon() {
      return Component.m_237115_("tooltip.tensura.coming_soon").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD));
   }

   public static void comingSoonMessage(LivingEntity entity, Component component) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         player.m_5661_(component, false);
      }

   }

   public static void comingSoonMessage(LivingEntity entity, String name) {
      comingSoonMessage(entity, comingSoon(name));
   }

   public static void comingSoonMessage(LivingEntity entity) {
      comingSoonMessage(entity, comingSoon());
   }

   private static boolean isLabyrinthPvpOff(MobEffect effect, LivingEntity target, @Nullable Entity attacker) {
      if (attacker == target) {
         return false;
      } else if (!TensuraGameRules.isLabyrinthPvpOff(target.m_9236_(), target, attacker)) {
         return false;
      } else {
         return effect.m_19483_() == MobEffectCategory.HARMFUL || effect.m_19483_() == MobEffectCategory.NEUTRAL;
      }
   }

   public static void addEffectWithSource(LivingEntity target, LivingEntity source, MobEffectInstance instance, boolean ignoreSameLevel) {
      if (!isLabyrinthPvpOff(instance.m_19544_(), target, source)) {
         target = ILivingPartEntity.checkForHead(target);
         MobEffectInstance effectInstance = target.m_21124_(instance.m_19544_());
         if (effectInstance != null && effectInstance.m_19564_() >= instance.m_19564_() && !ignoreSameLevel) {
            if (effectInstance.m_19564_() == instance.m_19564_()) {
               TensuraEffectsCapability.setEffectSource(target, source, instance.m_19544_());
            }
         } else {
            TensuraEffectsCapability.setEffectSource(target, source, instance.m_19544_());
            target.m_147207_(instance, source);
         }

      }
   }

   public static void addEffectWithSource(LivingEntity target, LivingEntity source, MobEffect mobEffect, int duration, int level, boolean ignoreSameLevel, boolean ambient, boolean visible, boolean showIcon) {
      if (!isLabyrinthPvpOff(mobEffect, target, source)) {
         target = ILivingPartEntity.checkForHead(target);
         MobEffectInstance effectInstance = target.m_21124_(mobEffect);
         if (effectInstance != null && effectInstance.m_19564_() >= level && !ignoreSameLevel) {
            if (effectInstance.m_19564_() == level) {
               TensuraEffectsCapability.setEffectSource(target, source, mobEffect);
            }
         } else {
            TensuraEffectsCapability.setEffectSource(target, source, mobEffect);
            target.m_147207_(new MobEffectInstance(mobEffect, duration, level, ambient, visible, showIcon), source);
         }

      }
   }

   public static void addEffectWithSource(LivingEntity target, LivingEntity source, MobEffect mobEffect, int duration, int level, boolean ignoreSameLevel) {
      addEffectWithSource(target, source, mobEffect, duration, level, ignoreSameLevel, false, false, false);
   }

   public static void addEffectWithSource(LivingEntity target, LivingEntity source, MobEffect mobEffect, int duration, int level) {
      addEffectWithSource(target, source, mobEffect, duration, level, false);
   }

   public static void checkThenAddEffectSource(LivingEntity target, @Nullable Entity source, MobEffectInstance instance) {
      if (source instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)source;
         addEffectWithSource(target, living, instance, false);
      } else {
         target.m_147207_(instance, source);
      }

   }

   public static void checkThenAddEffectSource(LivingEntity target, @Nullable Entity source, MobEffect mobEffect, int duration, int level) {
      if (source instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)source;
         addEffectWithSource(target, living, mobEffect, duration, level, false);
      } else {
         target.m_147207_(new MobEffectInstance(mobEffect, duration, level, false, false, false), source);
      }

   }

   public static void checkThenAddEffectSource(LivingEntity target, @Nullable Entity source, MobEffect mobEffect, int duration, int level, boolean ambient, boolean visible, boolean showIcon) {
      if (source instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)source;
         addEffectWithSource(target, living, mobEffect, duration, level, false, ambient, visible, showIcon);
      } else {
         target.m_147207_(new MobEffectInstance(mobEffect, duration, level, ambient, visible, showIcon), source);
      }

   }

   public static void checkThenAddEffectSource(LivingEntity target, @Nullable Entity source, MobEffect mobEffect, int duration, int level, boolean ambient, boolean visible, boolean showIcon, boolean ignore) {
      if (source instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)source;
         addEffectWithSource(target, living, mobEffect, duration, level, ignore, ambient, visible, showIcon);
      } else {
         target.m_147207_(new MobEffectInstance(mobEffect, duration, level, ambient, visible, showIcon), source);
      }

   }

   public static boolean removePredicateEffect(LivingEntity target, Predicate<MobEffect> predicate) {
      return removePredicateEffect(target, predicate, 0.0D);
   }

   public static boolean removePredicateEffect(LivingEntity target, Predicate<MobEffect> predicate, double mpCost) {
      return removePredicateEffect(target, predicate, mpCost, 0.0D);
   }

   public static boolean removePredicateEffect(LivingEntity target, Predicate<MobEffect> predicate, double mpCost, double apCost) {
      boolean success = false;
      Iterator var7 = List.copyOf(target.m_21220_()).iterator();

      while(true) {
         MobEffectInstance effect;
         int level;
         do {
            do {
               do {
                  if (!var7.hasNext()) {
                     return success;
                  }

                  effect = (MobEffectInstance)var7.next();
               } while(!predicate.test(effect.m_19544_()));

               level = effect.m_19564_() + 1;
            } while(mpCost > 0.0D && outOfMagicule(target, mpCost * (double)level));
         } while(apCost > 0.0D && outOfAura(target, mpCost * (double)level));

         if (target.m_21195_(effect.m_19544_())) {
            success = true;
         }
      }
   }

   public static void removeLevelsOfEffect(LivingEntity entity, MobEffect effect, int level) {
      MobEffectInstance instance = entity.m_21124_(effect);
      if (instance != null) {
         int effectLevel = instance.m_19564_();
         if (effectLevel - level < 0) {
            entity.m_21195_(effect);
         } else {
            int duration = instance.m_19557_();
            boolean ambient = instance.m_19571_();
            boolean visible = instance.m_19572_();
            boolean showIcon = instance.m_19575_();
            entity.m_21195_(effect);
            entity.m_7292_(new MobEffectInstance(effect, duration, effectLevel - level, ambient, visible, showIcon));
         }
      }
   }

   public static void reduceEP(LivingEntity source, @Nullable LivingEntity attacker, double amount, boolean percentage) {
      reduceEP(source, attacker, amount, percentage, false);
   }

   public static void reduceEP(LivingEntity source, @Nullable LivingEntity attacker, double amount, boolean percentage, boolean max) {
      if (!(amount <= 0.0D) && source.m_6084_()) {
         if (max || !source.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_ENERGY_DRAIN)) {
            if (!TensuraGameRules.isLabyrinthPvpOff(source.m_9236_(), source, attacker)) {
               source = ILivingPartEntity.checkForHead(source);
               EnergyDrainEvent event = new EnergyDrainEvent(source, attacker, EnergyDrainEvent.DrainType.EP, amount, percentage, max);
               if (!MinecraftForge.EVENT_BUS.post(event)) {
                  TensuraEPCapability.getFrom(source).ifPresent((cap) -> {
                     double EP = max ? cap.getEP() : cap.getCurrentEP();
                     double drain = event.isPercentage() ? cap.getEP() * event.getAmount() : event.getAmount();
                     double newEP = EP - drain;
                     if (max) {
                        cap.setEP(source, newEP);
                     } else {
                        cap.setCurrentEP(source, newEP);
                     }

                     DamageSourceHelper.markHurt(source, attacker);
                     if (newEP < 0.0D && source.m_6084_()) {
                        DamageSource drainSource = attacker != null ? TensuraDamageSources.drainEP(attacker) : TensuraDamageSources.OUT_OF_ENERGY;
                        source.m_21231_().m_19289_(drainSource, source.m_21223_(), source.m_21223_());
                        source.m_21153_(0.0F);
                        source.m_7911_(0.0F);
                        source.m_6667_(drainSource);
                     }

                  });
               }
            }
         }
      }
   }

   public static boolean reduceEnergy(LivingEntity source, @Nullable LivingEntity attacker, double amountEach, boolean percentage) {
      if (!(amountEach <= 0.0D) && source.m_6084_()) {
         if (source.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_ENERGY_DRAIN)) {
            return false;
         } else {
            source = ILivingPartEntity.checkForHead(source);
            if (source instanceof Player) {
               Player playerTarget = (Player)source;
               if (TensuraGameRules.isLabyrinthPvpOff(source.m_9236_(), source, attacker)) {
                  return false;
               }

               EnergyDrainEvent event = new EnergyDrainEvent(source, attacker, EnergyDrainEvent.DrainType.BOTH, amountEach, percentage, false);
               if (MinecraftForge.EVENT_BUS.post(event)) {
                  return false;
               }

               TensuraPlayerCapability.getFrom(playerTarget).ifPresent((cap) -> {
                  double mp = event.isPercentage() ? source.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get()) * event.getAmount() : event.getAmount();
                  cap.setMagicule(cap.getMagicule() - mp);
                  double ap = event.isPercentage() ? source.m_21133_((Attribute)TensuraAttributeRegistry.MAX_AURA.get()) * event.getAmount() : event.getAmount();
                  cap.setAura(cap.getAura() - ap);
                  if (cap.getMagicule() + cap.getAura() <= 0.0D && source.m_6084_()) {
                     DamageSource drainSource = TensuraDamageSources.drainEP(attacker);
                     source.m_21231_().m_19289_(drainSource, source.m_21223_(), source.m_21223_());
                     source.m_21153_(0.0F);
                     source.m_7911_(0.0F);
                     source.m_6667_(drainSource);
                  }

               });
               DamageSourceHelper.markHurt(source, attacker);
               TensuraPlayerCapability.sync(playerTarget);
            } else {
               reduceEP(source, attacker, amountEach * 2.0D, percentage);
            }

            return true;
         }
      } else {
         return false;
      }
   }

   public static boolean drainEnergy(LivingEntity source, LivingEntity attacker, double amountEach, boolean percentage) {
      if (!(amountEach <= 0.0D) && source.m_6084_()) {
         if (source.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_ENERGY_DRAIN)) {
            return false;
         } else {
            source = ILivingPartEntity.checkForHead(source);
            EnergyDrainEvent event;
            if (source instanceof Player) {
               Player playerTarget = (Player)source;
               if (TensuraGameRules.isLabyrinthPvpOff(source.m_9236_(), source, attacker)) {
                  return false;
               }

               event = new EnergyDrainEvent(source, attacker, EnergyDrainEvent.DrainType.BOTH, amountEach, percentage, false);
               if (MinecraftForge.EVENT_BUS.post(event)) {
                  return false;
               }

               TensuraPlayerCapability.getFrom(playerTarget).ifPresent((cap) -> {
                  double mp = event.isPercentage() ? source.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get()) * event.getAmount() : event.getAmount();
                  gainMP(attacker, Math.min(cap.getMagicule(), mp));
                  cap.setMagicule(cap.getMagicule() - mp);
                  double ap = event.isPercentage() ? source.m_21133_((Attribute)TensuraAttributeRegistry.MAX_AURA.get()) * event.getAmount() : event.getAmount();
                  gainAP(attacker, Math.min(cap.getAura(), mp));
                  cap.setAura(cap.getAura() - ap);
                  if (cap.getMagicule() + cap.getAura() <= 0.0D && source.m_6084_()) {
                     DamageSource drainSource = TensuraDamageSources.drainEP(attacker);
                     source.m_21231_().m_19289_(drainSource, source.m_21223_(), source.m_21223_());
                     source.m_21153_(0.0F);
                     source.m_7911_(0.0F);
                     source.m_6667_(drainSource);
                  }

               });
               DamageSourceHelper.markHurt(source, attacker);
               TensuraPlayerCapability.sync(playerTarget);
            } else {
               event = new EnergyDrainEvent(source, attacker, EnergyDrainEvent.DrainType.EP, amountEach, percentage, false);
               if (MinecraftForge.EVENT_BUS.post(event)) {
                  return false;
               }

               TensuraEPCapability.getFrom(source).ifPresent((cap) -> {
                  double drain = event.isPercentage() ? cap.getEP() * event.getAmount() : event.getAmount() * 2.0D;
                  gainMP(attacker, Math.min(drain / 2.0D, cap.getCurrentEP()));
                  gainAP(attacker, Math.min(drain / 2.0D, cap.getCurrentEP()));
                  double newEP = cap.getCurrentEP() - drain;
                  cap.setCurrentEP(source, newEP);
                  DamageSourceHelper.markHurt(source, attacker);
                  if (newEP <= 0.0D && source.m_6084_()) {
                     DamageSource drainSource = TensuraDamageSources.drainEP(attacker);
                     source.m_21231_().m_19289_(drainSource, source.m_21223_(), source.m_21223_());
                     source.m_21153_(0.0F);
                     source.m_7911_(0.0F);
                     source.m_6667_(drainSource);
                  }

               });
            }

            return true;
         }
      } else {
         return false;
      }
   }

   public static boolean drainMP(LivingEntity source, LivingEntity attacker, double amount, boolean percentage) {
      if (!(amount <= 0.0D) && source.m_6084_()) {
         if (source.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_ENERGY_DRAIN)) {
            return false;
         } else {
            source = ILivingPartEntity.checkForHead(source);
            if (source instanceof Player) {
               Player playerTarget = (Player)source;
               if (TensuraGameRules.isLabyrinthPvpOff(source.m_9236_(), source, attacker)) {
                  return false;
               }

               EnergyDrainEvent event = new EnergyDrainEvent(source, attacker, EnergyDrainEvent.DrainType.MAGICULE, amount, percentage, false);
               if (MinecraftForge.EVENT_BUS.post(event)) {
                  return false;
               }

               double mp = event.isPercentage() ? source.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get()) * event.getAmount() : event.getAmount();
               TensuraPlayerCapability.getFrom(playerTarget).ifPresent((cap) -> {
                  gainMP(attacker, Math.min(mp, cap.getMagicule()));
                  cap.setMagicule(cap.getMagicule() - mp);
               });
               DamageSourceHelper.markHurt(source, attacker);
               TensuraPlayerCapability.sync(playerTarget);
            } else {
               EnergyDrainEvent event = new EnergyDrainEvent(source, attacker, EnergyDrainEvent.DrainType.EP, amount, percentage, false);
               if (MinecraftForge.EVENT_BUS.post(event)) {
                  return false;
               }

               TensuraEPCapability.getFrom(source).ifPresent((cap) -> {
                  double drain = event.isPercentage() ? cap.getEP() * event.getAmount() : event.getAmount();
                  gainMP(attacker, Math.min(drain, cap.getCurrentEP()));
                  double newEP = cap.getCurrentEP() - drain;
                  cap.setCurrentEP(source, newEP);
                  DamageSourceHelper.markHurt(source, attacker);
                  if (newEP <= 0.0D && source.m_6084_()) {
                     DamageSource drainSource = TensuraDamageSources.drainEP(attacker);
                     source.m_21231_().m_19289_(drainSource, source.m_21223_(), source.m_21223_());
                     source.m_21153_(0.0F);
                     source.m_7911_(0.0F);
                     source.m_6667_(drainSource);
                  }

               });
            }

            return true;
         }
      } else {
         return false;
      }
   }

   public static void gainMaxMP(LivingEntity living, double amount) {
      if (!(amount <= 0.0D) && living.m_6084_()) {
         living = ILivingPartEntity.checkForHead(living);
         if (living instanceof Player) {
            Player player = (Player)living;
            TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
               cap.setBaseMagicule(cap.getBaseMagicule() + amount, player);
               cap.setMagicule(cap.getMagicule() + amount);
            });
            TensuraPlayerCapability.sync(player);
         } else {
            TensuraEPCapability.getFrom(living).ifPresent((cap) -> {
               cap.setEP(living, cap.getEP() + amount);
            });
         }

      }
   }

   public static void gainMP(LivingEntity living, double amount, boolean exceedMax) {
      if (!(amount <= 0.0D) && living.m_6084_()) {
         living = ILivingPartEntity.checkForHead(living);
         if (living instanceof Player) {
            Player player = (Player)living;
            double maxMP = living.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get());
            TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
               double newMP = cap.getMagicule() + amount;
               if (exceedMax) {
                  cap.setMagicule(newMP);
               } else {
                  if (!(cap.getMagicule() < maxMP)) {
                     return;
                  }

                  cap.setMagicule(Math.min(newMP, maxMP));
               }

               TensuraPlayerCapability.sync(player);
            });
         } else {
            TensuraEPCapability.getFrom(living).ifPresent((cap) -> {
               cap.setCurrentEP(living, cap.getCurrentEP() + amount);
            });
         }

      }
   }

   public static void gainMP(LivingEntity living, double amount) {
      gainMP(living, amount, true);
   }

   public static void drainAP(LivingEntity source, LivingEntity attacker, double amount) {
      if (!(amount <= 0.0D) && source.m_6084_()) {
         if (!source.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_ENERGY_DRAIN)) {
            source = ILivingPartEntity.checkForHead(source);
            if (source instanceof Player) {
               Player playerTarget = (Player)source;
               if (TensuraGameRules.isLabyrinthPvpOff(source.m_9236_(), source, attacker)) {
                  return;
               }

               EnergyDrainEvent event = new EnergyDrainEvent(source, attacker, EnergyDrainEvent.DrainType.AURA, amount, false, false);
               if (MinecraftForge.EVENT_BUS.post(event)) {
                  return;
               }

               TensuraPlayerCapability.getFrom(playerTarget).ifPresent((cap) -> {
                  gainAP(attacker, Math.min(event.getAmount(), cap.getAura()));
                  cap.setAura(cap.getAura() - event.getAmount());
               });
               DamageSourceHelper.markHurt(source, attacker);
               TensuraPlayerCapability.sync(playerTarget);
            } else {
               EnergyDrainEvent event = new EnergyDrainEvent(source, attacker, EnergyDrainEvent.DrainType.MAGICULE, amount, false, false);
               if (MinecraftForge.EVENT_BUS.post(event)) {
                  return;
               }

               TensuraEPCapability.getFrom(source).ifPresent((cap) -> {
                  gainAP(attacker, Math.min(event.getAmount(), cap.getCurrentEP()));
                  double newEP = cap.getCurrentEP() - event.getAmount();
                  cap.setCurrentEP(source, newEP);
                  DamageSourceHelper.markHurt(source, attacker);
                  if (newEP <= 0.0D && source.m_6084_()) {
                     DamageSource drainSource = TensuraDamageSources.drainEP(attacker);
                     source.m_21231_().m_19289_(drainSource, source.m_21223_(), source.m_21223_());
                     source.m_21153_(0.0F);
                     source.m_7911_(0.0F);
                     source.m_6667_(drainSource);
                  }

               });
            }

         }
      }
   }

   public static void gainMaxAP(LivingEntity living, double amount) {
      if (!(amount <= 0.0D) && living.m_6084_()) {
         living = ILivingPartEntity.checkForHead(living);
         if (living instanceof Player) {
            Player player = (Player)living;
            TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
               cap.setBaseAura(cap.getBaseAura() + amount, player);
               cap.setAura(cap.getAura() + amount);
            });
            TensuraPlayerCapability.sync(player);
         } else {
            TensuraEPCapability.getFrom(living).ifPresent((cap) -> {
               cap.setEP(living, cap.getEP() + amount);
            });
         }

      }
   }

   public static void gainAP(LivingEntity living, double amount, boolean exceedMax) {
      if (!(amount <= 0.0D) && living.m_6084_()) {
         living = ILivingPartEntity.checkForHead(living);
         if (living instanceof Player) {
            Player player = (Player)living;
            double maxAP = living.m_21133_((Attribute)TensuraAttributeRegistry.MAX_AURA.get());
            TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
               double newAP = cap.getAura() + amount;
               if (exceedMax) {
                  cap.setAura(newAP);
               } else {
                  if (!(cap.getAura() < maxAP)) {
                     return;
                  }

                  cap.setAura(Math.min(newAP, maxAP));
               }

               TensuraPlayerCapability.sync(player);
            });
         } else {
            TensuraEPCapability.getFrom(living).ifPresent((cap) -> {
               cap.setCurrentEP(living, cap.getCurrentEP() + amount);
            });
         }

      }
   }

   public static void gainAP(LivingEntity living, double amount) {
      gainAP(living, amount, true);
   }

   public static double mpRegen(LivingEntity entity, double maxMP, double baseValue) {
      double value = baseValue;
      MobEffectInstance regeneration = entity.m_21124_((MobEffect)TensuraMobEffects.MAGICULE_REGENERATION.get());
      if (regeneration != null) {
         value = baseValue + (double)((float)(1 + regeneration.m_19564_()) * 0.5F) * baseValue;
      }

      MobEffectInstance rest = entity.m_21124_((MobEffect)TensuraMobEffects.REST.get());
      if (rest != null) {
         value *= (double)((float)(1 + rest.m_19564_()) * 10.0F);
      }

      if (entity.m_21023_((MobEffect)TensuraMobEffects.SLEEP_MODE.get())) {
         value += maxMP * 3.0E-4D;
      }

      MobEffectInstance blockade = entity.m_21124_((MobEffect)TensuraMobEffects.ENERGY_BLOCKADE.get());
      if (blockade != null) {
         value += (double)((float)(1 + blockade.m_19564_()) * -0.2F) * value;
      }

      EnergyRegenerateTickEvent.Magicule magicule = new EnergyRegenerateTickEvent.Magicule(entity, baseValue, value, maxMP);
      return MinecraftForge.EVENT_BUS.post(magicule) ? 0.0D : magicule.getValue();
   }

   public static double apRegen(LivingEntity entity, double maxAP, double baseValue) {
      double value = baseValue;
      MobEffectInstance rest = entity.m_21124_((MobEffect)TensuraMobEffects.REST.get());
      if (rest != null) {
         value = baseValue * (double)((float)(1 + rest.m_19564_()) * 10.0F);
      }

      if (entity.m_21023_((MobEffect)TensuraMobEffects.SLEEP_MODE.get())) {
         baseValue *= 3.0D;
      }

      MobEffectInstance blockade = entity.m_21124_((MobEffect)TensuraMobEffects.ENERGY_BLOCKADE.get());
      if (blockade != null) {
         value += (double)((float)(1 + blockade.m_19564_()) * -0.2F) * value;
      }

      EnergyRegenerateTickEvent.Aura aura = new EnergyRegenerateTickEvent.Aura(entity, baseValue, value, maxAP);
      return MinecraftForge.EVENT_BUS.post(aura) ? 0.0D : aura.getValue();
   }

   public static double getAP(LivingEntity entity, boolean max) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         return max ? entity.m_21133_((Attribute)TensuraAttributeRegistry.MAX_AURA.get()) : TensuraPlayerCapability.getAura(player);
      } else {
         return TensuraEPCapability.getEP(entity);
      }
   }

   public static boolean outOfAura(LivingEntity entity, ManasSkillInstance skillInstance) {
      if (skillInstance instanceof TensuraSkillInstance) {
         TensuraSkillInstance instance = (TensuraSkillInstance)skillInstance;
         return outOfAura(entity, instance.auraCost(entity));
      } else {
         return false;
      }
   }

   public static boolean outOfAura(LivingEntity entity, double cost) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         if (player.m_7500_()) {
            return false;
         } else if (TensuraPlayerCapability.getAura(player) - cost >= 0.0D) {
            TensuraPlayerCapability.decreaseAura(player, cost);
            return false;
         } else {
            player.m_5661_(Component.m_237115_("tensura.skill.lack_aura").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
            return true;
         }
      } else if (TensuraEPCapability.getCurrentEP(entity) - cost >= 0.0D) {
         TensuraEPCapability.decreaseCurrentEP(entity, cost);
         return false;
      } else {
         return true;
      }
   }

   public static double outOfAuraStillConsume(LivingEntity entity, double cost) {
      if (cost <= 0.0D) {
         return 0.0D;
      } else {
         double remainCost;
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (player.m_7500_()) {
               return 0.0D;
            } else {
               remainCost = TensuraPlayerCapability.getAura(player) - cost;
               if (remainCost >= 0.0D) {
                  TensuraPlayerCapability.decreaseAura(player, cost);
                  return 0.0D;
               } else {
                  TensuraPlayerCapability.setAura(player, 0.0D);
                  player.m_5661_(Component.m_237115_("tensura.skill.lack_aura").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
                  return Math.abs(remainCost);
               }
            }
         } else {
            remainCost = TensuraEPCapability.getCurrentEP(entity) - cost;
            if (remainCost >= 0.0D) {
               TensuraEPCapability.decreaseCurrentEP(entity, cost);
               return 0.0D;
            } else {
               TensuraEPCapability.setCurrentLivingEP(entity, 0.0D);
               return Math.abs(remainCost);
            }
         }
      }
   }

   public static boolean outOfEachEP(LivingEntity entity, double cost) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         if (player.m_7500_()) {
            return false;
         } else if (TensuraPlayerCapability.getAura(player) - cost < 0.0D) {
            player.m_5661_(Component.m_237115_("tensura.skill.lack_aura").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
            return true;
         } else if (TensuraPlayerCapability.getMagicule(player) - cost < 0.0D) {
            player.m_5661_(Component.m_237115_("tensura.skill.lack_magicule").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
            return true;
         } else {
            TensuraPlayerCapability.decreaseAura(player, cost);
            TensuraPlayerCapability.decreaseMagicule(player, cost);
            return false;
         }
      } else if (TensuraEPCapability.getCurrentEP(entity) - cost * 2.0D >= 0.0D) {
         TensuraEPCapability.decreaseCurrentEP(entity, cost * 2.0D);
         return false;
      } else {
         return true;
      }
   }

   public static double getMP(LivingEntity entity, boolean max) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         return max ? entity.m_21133_((Attribute)TensuraAttributeRegistry.MAX_MAGICULE.get()) : TensuraPlayerCapability.getMagicule(player);
      } else {
         return TensuraEPCapability.getEP(entity);
      }
   }

   public static boolean outOfMagicule(LivingEntity entity, ManasSkillInstance skillInstance) {
      if (skillInstance instanceof TensuraSkillInstance) {
         TensuraSkillInstance instance = (TensuraSkillInstance)skillInstance;
         return outOfMagicule(entity, instance.magiculeCost(entity));
      } else {
         return false;
      }
   }

   public static boolean outOfMagicule(LivingEntity entity, double cost) {
      if (cost <= 0.0D) {
         return false;
      } else if (entity instanceof Player) {
         Player player = (Player)entity;
         if (player.m_7500_()) {
            return false;
         } else if (TensuraPlayerCapability.getMagicule(player) - cost >= 0.0D) {
            TensuraPlayerCapability.decreaseMagicule(player, cost);
            return false;
         } else {
            player.m_5661_(Component.m_237115_("tensura.skill.lack_magicule").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
            return true;
         }
      } else if (TensuraEPCapability.getCurrentEP(entity) - cost >= 0.0D) {
         TensuraEPCapability.decreaseCurrentEP(entity, cost);
         return false;
      } else {
         return true;
      }
   }

   public static double outOfMagiculeStillConsume(LivingEntity entity, double cost) {
      if (cost <= 0.0D) {
         return 0.0D;
      } else {
         double remainCost;
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (player.m_7500_()) {
               return 0.0D;
            } else {
               remainCost = TensuraPlayerCapability.getMagicule(player) - cost;
               if (remainCost >= 0.0D) {
                  TensuraPlayerCapability.decreaseMagicule(player, cost);
                  return 0.0D;
               } else {
                  TensuraPlayerCapability.setMagicule(player, 0.0D);
                  player.m_5661_(Component.m_237115_("tensura.skill.lack_magicule").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), true);
                  return Math.abs(remainCost);
               }
            }
         } else {
            remainCost = TensuraEPCapability.getCurrentEP(entity) - cost;
            if (remainCost >= 0.0D) {
               TensuraEPCapability.decreaseCurrentEP(entity, cost);
               return 0.0D;
            } else {
               TensuraEPCapability.setCurrentLivingEP(entity, 0.0D);
               return Math.abs(remainCost);
            }
         }
      }
   }

   @Nullable
   public static Entity getEntityFromUUID(ServerLevel serverLevel, UUID uuid) {
      return getEntityFromUUID(serverLevel, uuid, (entity) -> {
         return true;
      });
   }

   @Nullable
   public static Entity getEntityFromUUID(ServerLevel serverLevel, UUID uuid, Predicate<Entity> predicate) {
      Entity entity = null;
      Iterator var4 = serverLevel.m_7654_().m_129785_().iterator();

      while(var4.hasNext()) {
         ServerLevel level = (ServerLevel)var4.next();
         Entity target = level.m_8791_(uuid);
         if (target != null && predicate.test(target)) {
            entity = target;
            break;
         }
      }

      return entity;
   }

   @Nullable
   public static Entity moveAcrossDimensionTo(Entity entity, Entity target) {
      Level var3 = target.m_9236_();
      if (var3 instanceof ServerLevel) {
         ServerLevel dimension = (ServerLevel)var3;
         return moveAcrossDimensionTo(entity, target.m_20185_(), target.m_20186_(), target.m_20189_(), target.m_146908_(), target.m_146909_(), dimension);
      } else {
         return null;
      }
   }

   @Nullable
   public static Entity moveAcrossDimensionTo(Entity pEntity, double pX, double pY, double pZ, float yaw, float pitch, ServerLevel dimension) {
      if (pEntity instanceof ServerPlayer) {
         ServerPlayer player = (ServerPlayer)pEntity;
         ChunkPos chunkpos = new ChunkPos(new BlockPos(pX, pY, pZ));
         dimension.m_7726_().m_8387_(TicketType.f_9448_, chunkpos, 1, pEntity.m_19879_());
         pEntity.m_8127_();
         if (player.m_5803_()) {
            player.m_6145_(true, true);
         }

         if (dimension == pEntity.f_19853_) {
            player.m_8999_(dimension, pX, pY, pZ, yaw, pitch);
         } else {
            player.m_8999_(dimension, pX, pY, pZ, yaw, pitch);
         }

         pEntity.f_19864_ = true;
      } else {
         if (dimension != pEntity.f_19853_) {
            pEntity.m_19877_();
            Entity entity = pEntity.m_6095_().m_20615_(dimension);
            if (entity != null) {
               entity.m_20361_(pEntity);
               entity.m_7678_(pX, pY, pZ, 0.0F, 0.0F);
               dimension.m_143334_(entity);
               entity.f_19864_ = true;
            }

            pEntity.m_142467_(RemovalReason.CHANGED_DIMENSION);
            return entity;
         }

         pEntity.m_6027_(pX, pY, pZ);
         pEntity.f_19864_ = true;
      }

      return pEntity;
   }

   public static float noCritAttackDamage(Player player, Entity pTarget) {
      float f = (float)player.m_21133_(Attributes.f_22281_);
      float f1;
      if (pTarget instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)pTarget;
         f1 = EnchantmentHelper.m_44833_(player.m_21205_(), living.m_6336_());
      } else {
         f1 = EnchantmentHelper.m_44833_(player.m_21205_(), MobType.f_21640_);
      }

      float f2 = player.m_36403_(0.5F);
      f *= 0.2F + f2 * f2 * 0.8F;
      f1 *= f2;
      return f + f1;
   }

   public static void removeTarget(Mob target) {
      target.m_6710_((LivingEntity)null);
      target.m_21335_((Entity)null);
      target.m_6703_((LivingEntity)null);
      target.f_21346_.m_148105_().forEach(WrappedGoal::m_8041_);
   }

   public static void removeSpecificTargetInRadius(Entity entity, double raidus, Predicate<Mob> predicate) {
      entity.m_9236_().m_45976_(Mob.class, entity.m_20191_().m_82400_(raidus)).stream().filter((mob) -> {
         return mob.m_5448_() == entity && predicate.test(mob);
      }).forEach(SkillHelper::removeTarget);
   }

   @Nullable
   public static <T extends Entity> T getTargetingEntity(Class<T> targetClass, LivingEntity livingEntity, double distance, double radius, boolean nonSubordinate, boolean lineOfSight) {
      Vec3 view = livingEntity.m_20252_(1.0F);
      T target = null;
      AABB expanded = livingEntity.m_20191_().m_82363_(view.f_82479_ * distance, view.f_82480_ * distance, view.f_82481_ * distance);
      List<T> list = livingEntity.m_9236_().m_6443_(targetClass, expanded, (entityx) -> {
         return !entityx.m_7306_(livingEntity) && (!entityx.m_7307_(livingEntity) || !nonSubordinate) && (livingEntity.m_142582_(entityx) || !lineOfSight);
      });
      if (list.isEmpty()) {
         return null;
      } else {
         double furthestEntity = distance;
         Vec3 eye = livingEntity.m_20299_(1.0F).m_82520_(view.f_82479_ * -1.0D, view.f_82480_ * -1.0D, view.f_82481_ * -1.0D);
         Vec3 furtherEye = eye.m_82520_(view.f_82479_ * distance, view.f_82480_ * distance, view.f_82481_ * distance);
         Iterator var16 = list.iterator();

         while(true) {
            while(true) {
               Entity entity;
               double closerEntity;
               do {
                  AABB aabb;
                  Optional optional;
                  do {
                     do {
                        if (!var16.hasNext()) {
                           if (target instanceof ILivingPartEntity) {
                              ILivingPartEntity part = (ILivingPartEntity)target;
                              Entity var24 = part.getHead();
                              if (var24 instanceof LivingEntity) {
                                 LivingEntity head = (LivingEntity)var24;
                                 return head;
                              }
                           }

                           return target;
                        }

                        entity = (Entity)var16.next();
                        aabb = entity.m_20191_().m_82400_(radius);
                        optional = aabb.m_82371_(eye, furtherEye);
                     } while(!optional.isPresent());
                  } while(aabb.m_82390_(eye));

                  closerEntity = eye.m_82554_((Vec3)optional.get());
               } while(!(closerEntity < furthestEntity) && furthestEntity != 0.0D);

               if (entity.m_20201_() == livingEntity.m_20201_() && !livingEntity.canRiderInteract()) {
                  if (furthestEntity == 0.0D) {
                     target = entity;
                  }
               } else {
                  target = entity;
                  furthestEntity = closerEntity;
               }
            }
         }
      }
   }

   @Nullable
   public static <T extends Entity> T getTargetingEntity(Class<T> targetClass, LivingEntity livingEntity, double distance, double radius, boolean nonSubordinate) {
      return getTargetingEntity(targetClass, livingEntity, distance, radius, nonSubordinate, true);
   }

   @Nullable
   public static Entity getTargetingEntity(LivingEntity livingEntity, double distance, double radius, boolean nonSubordinate, boolean lineOfSight) {
      return getTargetingEntity(Entity.class, livingEntity, distance, radius, nonSubordinate, lineOfSight);
   }

   @Nullable
   public static LivingEntity getTargetingEntity(LivingEntity livingEntity, double distance, boolean nonSubordinate, boolean lineOfSight) {
      return (LivingEntity)getTargetingEntity(LivingEntity.class, livingEntity, distance, 0.2D, nonSubordinate, lineOfSight);
   }

   @Nullable
   public static LivingEntity getTargetingEntity(LivingEntity livingEntity, double distance, boolean nonSubordinate) {
      return getTargetingEntity(livingEntity, distance, nonSubordinate, true);
   }

   public static boolean isSubordinate(LivingEntity owner, LivingEntity sub) {
      if (sub == owner) {
         return false;
      } else {
         UUID temporaryOwner = TensuraEPCapability.getTemporaryOwner(sub);
         if (temporaryOwner != null) {
            return Objects.equals(owner.m_20148_(), temporaryOwner);
         } else {
            UUID permanentOwner = TensuraEPCapability.getPermanentOwner(sub);
            if (permanentOwner != null) {
               return Objects.equals(owner.m_20148_(), permanentOwner);
            } else if (sub instanceof TamableAnimal) {
               TamableAnimal tamable = (TamableAnimal)sub;
               return tamable.m_21830_(owner);
            } else if (sub instanceof TensuraHorseEntity) {
               TensuraHorseEntity horse = (TensuraHorseEntity)sub;
               return horse.isOwnedBy(owner);
            } else {
               LivingEntity subOwner = getSubordinateOwner(sub);
               return subOwner != null && isSubordinate(owner, subOwner);
            }
         }
      }
   }

   @Nullable
   public static LivingEntity getSubordinateOwner(LivingEntity sub) {
      UUID temporaryOwner = TensuraEPCapability.getTemporaryOwner(sub);
      if (temporaryOwner != null) {
         return sub.m_9236_().m_46003_(temporaryOwner);
      } else {
         UUID permanentOwner = TensuraEPCapability.getPermanentOwner(sub);
         if (permanentOwner != null) {
            return sub.m_9236_().m_46003_(permanentOwner);
         } else if (sub instanceof TamableAnimal) {
            TamableAnimal tamable = (TamableAnimal)sub;
            return tamable.m_21826_();
         } else if (sub instanceof TensuraHorseEntity) {
            TensuraHorseEntity horse = (TensuraHorseEntity)sub;
            return horse.getOwner();
         } else {
            return null;
         }
      }
   }

   public static boolean isOrderedToStay(LivingEntity sub) {
      if (sub instanceof TamableAnimal) {
         TamableAnimal tamable = (TamableAnimal)sub;
         return tamable.m_21827_();
      } else if (sub instanceof TensuraHorseEntity) {
         TensuraHorseEntity horse = (TensuraHorseEntity)sub;
         return horse.isSitting();
      } else {
         return false;
      }
   }

   public static void setStay(LivingEntity living) {
      LivingEntity var3 = ILivingPartEntity.checkForHead(living);
      if (var3 instanceof TamableAnimal) {
         TamableAnimal tamable = (TamableAnimal)var3;
         tamable.m_21573_().m_26573_();
         tamable.m_6710_((LivingEntity)null);
         if (tamable instanceof TensuraTamableEntity) {
            TensuraTamableEntity entity = (TensuraTamableEntity)tamable;
            entity.setWandering(false);
         }

         tamable.m_21839_(true);
      } else if (living instanceof TensuraHorseEntity) {
         TensuraHorseEntity horse = (TensuraHorseEntity)living;
         horse.m_21573_().m_26573_();
         horse.m_6710_((LivingEntity)null);
         horse.setWandering(false);
         horse.setSitting(true);
      }

   }

   public static void setFollow(LivingEntity living) {
      LivingEntity var3 = ILivingPartEntity.checkForHead(living);
      if (var3 instanceof TamableAnimal) {
         TamableAnimal tamable = (TamableAnimal)var3;
         if (tamable instanceof TensuraTamableEntity) {
            TensuraTamableEntity entity = (TensuraTamableEntity)tamable;
            entity.setWandering(false);
         }

         tamable.m_21839_(false);
      } else if (living instanceof TensuraHorseEntity) {
         TensuraHorseEntity horse = (TensuraHorseEntity)living;
         horse.setWandering(false);
         horse.setSitting(false);
      }

   }

   public static void setWander(LivingEntity living) {
      LivingEntity var3 = ILivingPartEntity.checkForHead(living);
      if (var3 instanceof TamableAnimal) {
         TamableAnimal tamable = (TamableAnimal)var3;
         if (tamable.m_21824_()) {
            tamable.m_6710_((LivingEntity)null);
            if (tamable instanceof TensuraTamableEntity) {
               TensuraTamableEntity entity = (TensuraTamableEntity)tamable;
               if (entity.m_21824_()) {
                  entity.setWandering(true);
                  if (entity.m_21826_() != null) {
                     entity.setWanderPos(entity.m_21826_().m_20097_().m_7494_());
                  }
               }
            }
         }

         tamable.m_21839_(false);
      } else if (living instanceof TensuraHorseEntity) {
         TensuraHorseEntity horse = (TensuraHorseEntity)living;
         horse.setSitting(false);
         if (horse.m_30614_()) {
            horse.m_6710_((LivingEntity)null);
            horse.setWandering(true);
            if (horse.getOwner() != null) {
               horse.setWanderPos(horse.getOwner().m_20097_().m_7494_());
            }
         }
      }

   }

   public static void setNeutral(LivingEntity entity) {
      LivingEntity var3 = ILivingPartEntity.checkForHead(entity);
      if (var3 instanceof TensuraTamableEntity) {
         TensuraTamableEntity tamable = (TensuraTamableEntity)var3;
         tamable.m_6710_((LivingEntity)null);
         tamable.setBehaviour(0);
      } else if (entity instanceof TensuraHorseEntity) {
         TensuraHorseEntity horse = (TensuraHorseEntity)entity;
         horse.m_6710_((LivingEntity)null);
         horse.setBehaviour(0);
      }

   }

   public static void setPassive(LivingEntity entity) {
      LivingEntity var3 = ILivingPartEntity.checkForHead(entity);
      if (var3 instanceof TensuraTamableEntity) {
         TensuraTamableEntity tamable = (TensuraTamableEntity)var3;
         tamable.m_6710_((LivingEntity)null);
         tamable.setBehaviour(1);
      } else if (entity instanceof TensuraHorseEntity) {
         TensuraHorseEntity horse = (TensuraHorseEntity)entity;
         horse.m_6710_((LivingEntity)null);
         horse.setBehaviour(1);
      }

   }

   public static void setAggressive(LivingEntity entity) {
      LivingEntity var3 = ILivingPartEntity.checkForHead(entity);
      if (var3 instanceof TensuraTamableEntity) {
         TensuraTamableEntity tamable = (TensuraTamableEntity)var3;
         tamable.setBehaviour(2);
      } else if (entity instanceof TensuraHorseEntity) {
         TensuraHorseEntity horse = (TensuraHorseEntity)entity;
         horse.setBehaviour(2);
      }

   }

   public static void setProtect(LivingEntity entity) {
      LivingEntity var3 = ILivingPartEntity.checkForHead(entity);
      if (var3 instanceof TensuraTamableEntity) {
         TensuraTamableEntity tamable = (TensuraTamableEntity)var3;
         tamable.m_6710_((LivingEntity)null);
         tamable.setBehaviour(3);
      } else if (entity instanceof TensuraHorseEntity) {
         TensuraHorseEntity horse = (TensuraHorseEntity)entity;
         horse.m_6710_((LivingEntity)null);
         horse.setBehaviour(3);
      }

   }

   public static BlockHitResult getPlayerPOVHitResult(Level pLevel, Entity entity, Fluid pFluidMode, double reachDistance) {
      return getPlayerPOVHitResult(pLevel, entity, pFluidMode, Block.COLLIDER, reachDistance);
   }

   public static BlockHitResult getPlayerPOVHitResult(Level pLevel, Entity entity, Fluid pFluidMode, Block block, double reachDistance) {
      float f = entity.m_146909_();
      float f1 = entity.m_146908_();
      Vec3 vec3 = entity.m_146892_();
      float f2 = Mth.m_14089_(-f1 * 0.017453292F - 3.1415927F);
      float f3 = Mth.m_14031_(-f1 * 0.017453292F - 3.1415927F);
      float f4 = -Mth.m_14089_(-f * 0.017453292F);
      float f5 = Mth.m_14031_(-f * 0.017453292F);
      float f6 = f3 * f4;
      float f7 = f2 * f4;
      Vec3 vec31 = vec3.m_82520_((double)f6 * reachDistance, (double)f5 * reachDistance, (double)f7 * reachDistance);
      return pLevel.m_45547_(new ClipContext(vec3, vec31, block, pFluidMode, entity));
   }

   public static BlockHitResult getPlayerPOVHitResultFromPos(Level pLevel, LivingEntity entity, Fluid pFluidMode, double reachDistance, Vec3 fromPos) {
      return getPlayerPOVHitResultFromPos(pLevel, entity, pFluidMode, Block.OUTLINE, reachDistance, fromPos);
   }

   public static BlockHitResult getPlayerPOVHitResultFromPos(Level pLevel, LivingEntity entity, Fluid pFluidMode, Block block, double reachDistance, Vec3 fromPos) {
      float f = entity.m_146909_();
      float f1 = entity.m_146908_();
      float f2 = Mth.m_14089_(-f1 * 0.017453292F - 3.1415927F);
      float f3 = Mth.m_14031_(-f1 * 0.017453292F - 3.1415927F);
      float f4 = -Mth.m_14089_(-f * 0.017453292F);
      float f5 = Mth.m_14031_(-f * 0.017453292F);
      float f6 = f3 * f4;
      float f7 = f2 * f4;
      Vec3 vec31 = fromPos.m_82520_((double)f6 * reachDistance, (double)f5 * reachDistance, (double)f7 * reachDistance);
      return pLevel.m_45547_(new ClipContext(fromPos, vec31, block, pFluidMode, entity));
   }

   public static Vec3 getFloorPos(BlockPos pos) {
      return new Vec3((double)Mth.m_14143_((float)pos.m_123341_()) + 0.5D, (double)Mth.m_14143_((float)pos.m_123342_()), (double)Mth.m_14143_((float)pos.m_123343_()) + 0.5D);
   }

   public static Vec3 getFloorPos(Vec3 pos) {
      return new Vec3((double)Mth.m_14107_(pos.m_7096_()) + 0.5D, (double)Mth.m_14107_(pos.m_7098_()), (double)Mth.m_14107_(pos.m_7094_()) + 0.5D);
   }

   public static void riptidePush(LivingEntity entity, float riptideLevel) {
      float f7 = entity.m_146908_();
      float f = entity.m_146909_();
      float f1 = -Mth.m_14031_(f7 * 0.017453292F) * Mth.m_14089_(f * 0.017453292F);
      float f2 = -Mth.m_14031_(f * 0.017453292F);
      float f3 = Mth.m_14089_(f7 * 0.017453292F) * Mth.m_14089_(f * 0.017453292F);
      float f4 = Mth.m_14116_(f1 * f1 + f2 * f2 + f3 * f3);
      float f5 = 3.0F * ((1.0F + riptideLevel) / 4.0F);
      f1 *= f5 / f4;
      f2 *= f5 / f4;
      f3 *= f5 / f4;
      entity.m_5997_((double)f1, (double)f2, (double)f3);
   }

   public static void riptidePushVehicle(Entity vehicle, LivingEntity rider, float riptideLevel) {
      float f7 = rider.m_146908_();
      float f = rider.m_146909_();
      float f1 = -Mth.m_14031_(f7 * 0.017453292F) * Mth.m_14089_(f * 0.017453292F);
      float f2 = -Mth.m_14031_(f * 0.017453292F);
      float f3 = Mth.m_14089_(f7 * 0.017453292F) * Mth.m_14089_(f * 0.017453292F);
      float f4 = Mth.m_14116_(f1 * f1 + f2 * f2 + f3 * f3);
      float f5 = 3.0F * ((1.0F + riptideLevel) / 4.0F);
      f1 *= f5 / f4;
      f2 *= f5 / f4;
      f3 *= f5 / f4;
      vehicle.m_5997_((double)f1, (double)f2, (double)f3);
   }

   public static void knockBack(LivingEntity user, LivingEntity target, float strength) {
      if (target instanceof Player) {
         Player player = (Player)target;
         if (player.m_7500_()) {
            return;
         }
      }

      if (!target.m_5833_()) {
         double d1 = user.m_20185_() - target.m_20185_();

         double d0;
         for(d0 = user.m_20189_() - target.m_20189_(); d1 * d1 + d0 * d0 < 1.0E-4D; d0 = (Math.random() - Math.random()) * 0.01D) {
            d1 = (Math.random() - Math.random()) * 0.01D;
         }

         target.f_20918_ = (float)(Mth.m_14136_(d0, d1) * 57.2957763671875D - (double)target.m_146908_());
         target.m_147240_((double)strength, d1, d0);
         target.f_19864_ = true;
      }
   }

   public static void pushBackFromPos(BlockPos pos, Entity target, float strength) {
      if (target instanceof Player) {
         Player player = (Player)target;
         if (player.m_7500_()) {
            return;
         }
      }

      if (!target.m_5833_()) {
         double d1 = (double)pos.m_123341_() - target.m_20185_();

         double d0;
         for(d0 = (double)pos.m_123343_() - target.m_20189_(); d1 * d1 + d0 * d0 < 1.0E-4D; d0 = (Math.random() - Math.random()) * 0.01D) {
            d1 = (Math.random() - Math.random()) * 0.01D;
         }

         if (target instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)target;
            living.f_20918_ = (float)(Mth.m_14136_(d0, d1) * 57.2957763671875D - (double)target.m_146908_());
            living.m_147240_((double)strength, d1, d0);
         } else {
            target.f_19812_ = true;
            Vec3 vec3 = target.m_20184_();
            Vec3 vec31 = (new Vec3(d1, 0.0D, d0)).m_82541_().m_82490_((double)strength);
            target.m_20334_(vec3.f_82479_ / 2.0D - vec31.f_82479_, target.m_20096_() ? Math.min(0.4D, vec3.f_82480_ / 2.0D + (double)strength) : vec3.f_82480_, vec3.f_82481_ / 2.0D - vec31.f_82481_);
         }

         target.f_19864_ = true;
      }
   }

   public static void launchBlock(Entity entity, Vec3 pos, int radius, int yRadius, float upStrength, float pushStrength, Predicate<BlockState> statePredicate, Predicate<BlockPos> posPredicate) {
      launchBlock(entity, pos, radius, yRadius, upStrength, pushStrength, statePredicate, posPredicate, (ManasSkillInstance)null);
   }

   public static void launchBlock(Entity entity, Vec3 pos, int radius, int yRadius, float upStrength, float pushStrength, Predicate<BlockState> statePredicate, Predicate<BlockPos> posPredicate, @Nullable ManasSkillInstance instance) {
      Level level = entity.m_9236_();
      if (!level.m_5776_()) {
         int yPos = Mth.m_14107_(pos.m_7098_()) - 1;
         int xPos = Mth.m_14107_(pos.m_7096_());
         int zPos = Mth.m_14107_(pos.m_7094_());

         for(int j = -radius; j <= radius; ++j) {
            for(int k = -radius; k <= radius; ++k) {
               for(int i = -yRadius; i <= yRadius; ++i) {
                  int newYPos = yPos + i;
                  int newXPos = xPos + j;
                  int newZPos = zPos + k;
                  BlockPos blockpos = new BlockPos(newXPos, newYPos, newZPos);
                  if (!(blockpos.m_203193_(pos) > (double)(radius * radius)) && posPredicate.test(blockpos)) {
                     BlockState blockState = level.m_8055_(blockpos);
                     if (statePredicate.test(blockState)) {
                        SkillGriefEvent.Pre preGrief = new SkillGriefEvent.Pre(entity, instance, blockpos);
                        if (!MinecraftForge.EVENT_BUS.post(preGrief)) {
                           FallingBlockEntity fallingBlock = FallingBlockEntity.m_201971_(level, blockpos, blockState);
                           fallingBlock.m_20256_(fallingBlock.m_20184_().m_82520_(0.0D, (double)upStrength, 0.0D));
                           pushBackFromPos(entity.m_20183_(), fallingBlock, pushStrength);
                           fallingBlock.m_6478_(MoverType.SELF, fallingBlock.m_20184_());
                           fallingBlock.f_19864_ = true;
                           MinecraftForge.EVENT_BUS.post(new SkillGriefEvent.Post(entity, instance, blockpos));
                        }
                     }
                  }
               }
            }
         }

      }
   }

   public static ItemEntity dropItem(Entity entity, RandomSource random, ItemStack stack, int pickUpDelay, float throwForce) {
      double d0 = entity.m_20188_() - 0.3D;
      ItemEntity item = new ItemEntity(entity.f_19853_, entity.m_20185_(), d0, entity.m_20189_(), stack);
      item.m_32010_(pickUpDelay);
      float f8 = Mth.m_14031_(entity.m_146909_() * 0.017453292F);
      float f2 = Mth.m_14089_(entity.m_146909_() * 0.017453292F);
      float f3 = Mth.m_14031_(entity.m_146908_() * 0.017453292F);
      float f4 = Mth.m_14089_(entity.m_146908_() * 0.017453292F);
      float f5 = random.m_188501_() * 6.2831855F;
      float f6 = 0.02F * random.m_188501_();
      Vec3 throwVec = new Vec3((double)(-f3 * f2 * 0.3F) + Math.cos((double)f5) * (double)f6, (double)(-f8 * 0.3F + 0.1F + (random.m_188501_() - random.m_188501_()) * 0.1F), (double)(f4 * f2 * 0.3F) + Math.sin((double)f5) * (double)f6);
      item.m_20256_(throwVec.m_82490_((double)throwForce));
      entity.m_9236_().m_7967_(item);
      return item;
   }
}
