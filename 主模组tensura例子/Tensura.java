package com.github.manasmods.tensura;

import com.github.manasmods.tensura.ability.battlewill.utility.ViolentBreakArt;
import com.github.manasmods.tensura.client.TensuraClient;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.config.client.TensuraClientConfig;
import com.github.manasmods.tensura.core.AccessorRangedAttribute;
import com.github.manasmods.tensura.data.TensuraLootTable;
import com.github.manasmods.tensura.data.gen.TensuraAdvancementsProvider;
import com.github.manasmods.tensura.data.gen.TensuraBiomeMagiculeModifiersProvider;
import com.github.manasmods.tensura.data.gen.TensuraBiomeTagProvider;
import com.github.manasmods.tensura.data.gen.TensuraBlockStateProvider;
import com.github.manasmods.tensura.data.gen.TensuraBlockTagProvider;
import com.github.manasmods.tensura.data.gen.TensuraElementCombinationProvider;
import com.github.manasmods.tensura.data.gen.TensuraEntityEPProvider;
import com.github.manasmods.tensura.data.gen.TensuraEntityTypeTagProvider;
import com.github.manasmods.tensura.data.gen.TensuraFluidTagProvider;
import com.github.manasmods.tensura.data.gen.TensuraGearEPProvider;
import com.github.manasmods.tensura.data.gen.TensuraGlobalLootModifiersProvider;
import com.github.manasmods.tensura.data.gen.TensuraItemModelProvider;
import com.github.manasmods.tensura.data.gen.TensuraItemTagProvider;
import com.github.manasmods.tensura.data.gen.TensuraLanguageProvider;
import com.github.manasmods.tensura.data.gen.TensuraLevelMagiculeModifiersProvider;
import com.github.manasmods.tensura.data.gen.TensuraMoltenMaterialProvider;
import com.github.manasmods.tensura.data.gen.TensuraOtherworlderSpawningProvider;
import com.github.manasmods.tensura.data.gen.TensuraPaintingTagsProvider;
import com.github.manasmods.tensura.data.gen.TensuraPoiTypesProvider;
import com.github.manasmods.tensura.data.gen.TensuraRecipeProvider;
import com.github.manasmods.tensura.data.gen.TensuraStructureTagProvider;
import com.github.manasmods.tensura.data.pack.TensuraData;
import com.github.manasmods.tensura.enchantment.EngravingEnchantment;
import com.github.manasmods.tensura.handler.AttributeHandler;
import com.github.manasmods.tensura.handler.CapabilityHandler;
import com.github.manasmods.tensura.handler.TensuraEntityHandler;
import com.github.manasmods.tensura.item.food.HolyMilkItem;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.SyncElementCombinationPacket;
import com.github.manasmods.tensura.network.play2client.SyncEntityEPPacket;
import com.github.manasmods.tensura.network.play2client.SyncGearEPPacket;
import com.github.manasmods.tensura.network.play2client.SyncKilnMoltenColoringPacket;
import com.github.manasmods.tensura.network.play2client.SyncOtherworlderSpawningPacket;
import com.github.manasmods.tensura.registry.TensuraRegistry;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.world.TensuraGameRules;
import com.github.manasmods.tensura.world.biome.terrablender.TensuraOverworldRegion;
import java.io.InputStream;
import java.nio.file.CopyOption;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.FlowerPotBlock;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.ClientPlayerNetworkEvent.LoggingIn;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.data.event.GatherDataEvent;
import net.minecraftforge.event.server.ServerStartedEvent;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.fml.ModLoadingContext;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.config.ModConfig.Type;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.fml.event.lifecycle.FMLDedicatedServerSetupEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import net.minecraftforge.fml.loading.FMLPaths;
import net.minecraftforge.fml.loading.FileUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import software.bernie.geckolib3.GeckoLib;
import terrablender.api.Regions;

@Mod("tensura")
public class Tensura {
   public static final String MOD_ID = "tensura";
   public static final String CONFIG_DIR = "tensura-reincarnated";
   private static final Logger LOGGER = LogManager.getLogger();

   public Tensura() {
      IEventBus modEventBus = FMLJavaModLoadingContext.get().getModEventBus();
      TensuraRegistry.register(modEventBus);
      modEventBus.addListener(this::setup);
      modEventBus.addListener(this::generateData);
      modEventBus.addListener(this::fmlServerConfig);
      modEventBus.addListener(AttributeHandler::onAttributeModification);
      modEventBus.addListener(CapabilityHandler::registerCapabilities);
      modEventBus.addListener(TensuraEntityHandler::entityAttributeEvent);
      modEventBus.addListener(TensuraEntityHandler::registerEntityPlacements);
      IEventBus forgeEventBus = MinecraftForge.EVENT_BUS;
      forgeEventBus.addListener(this::serverStartedConfig);
      DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> {
         return () -> {
            forgeEventBus.addListener(this::clientLoadConfig);
            forgeEventBus.addListener(TensuraClient::openGui);
         };
      });
      GeckoLib.initialize();
      TensuraNetwork.register();
      TensuraData.KILN_MOLTEN_MATERIALS.subscribeAsSyncable(TensuraNetwork.INSTANCE, SyncKilnMoltenColoringPacket::new);
      TensuraData.GEAR_EP.subscribeAsSyncable(TensuraNetwork.INSTANCE, SyncGearEPPacket::new);
      TensuraData.ENTITY_EP.subscribeAsSyncable(TensuraNetwork.INSTANCE, SyncEntityEPPacket::new);
      TensuraData.ELEMENT_COMBINATION.subscribeAsSyncable(TensuraNetwork.INSTANCE, SyncElementCombinationPacket::new);
      TensuraData.OTHERWORLDER_SPAWNING.subscribeAsSyncable(TensuraNetwork.INSTANCE, SyncOtherworlderSpawningPacket::new);
      FileUtils.getOrCreateDirectory(FMLPaths.CONFIGDIR.get().resolve("tensura-reincarnated"), "tensura-reincarnated");
      ModLoadingContext.get().registerConfig(Type.SERVER, TensuraConfig.SPEC, getConfigFileName("common"));
      ModLoadingContext.get().registerConfig(Type.SERVER, SpawnRateConfig.SPEC, getConfigFileName("spawnrate-common"));
      ModLoadingContext.get().registerConfig(Type.CLIENT, TensuraClientConfig.SPEC, getConfigFileName("client"));
      Path common = FileUtils.getOrCreateDirectory(FMLPaths.GAMEDIR.get().resolve("defaultconfigs/tensura-reincarnated"), "tensura-reincarnated");
      copyDefaultConfig("common", common);
      Path spawnrate = FileUtils.getOrCreateDirectory(FMLPaths.GAMEDIR.get().resolve("defaultconfigs/tensura-reincarnated"), "tensura-reincarnated");
      copyDefaultConfig("spawnrate-common", spawnrate);
      FileUtils.getOrCreateDirectory(FMLPaths.GAMEDIR.get().resolve("defaultconfigs/" + getConfigFileName("remove_to_reset")), "tensura-reincarnated");
   }

   private void setup(FMLCommonSetupEvent event) {
      event.enqueueWork(() -> {
         Regions.register(new TensuraOverworldRegion());
         ((FlowerPotBlock)Blocks.f_50276_).addPlant(TensuraBlocks.PALM_SAPLING.getId(), TensuraBlocks.POTTED_PALM_SAPLING);
         ((FlowerPotBlock)Blocks.f_50276_).addPlant(TensuraBlocks.SAKURA_SAPLING.getId(), TensuraBlocks.POTTED_SAKURA_SAPLING);
         TensuraGameRules.registryGameRules();
         TensuraRecipeProvider.brewingRecipe();
         TensuraRecipeProvider.composterRecipe();
      });
   }

   private void generateData(GatherDataEvent event) {
      event.getGenerator().m_236039_(event.includeClient(), new TensuraBlockStateProvider(event));
      event.getGenerator().m_236039_(event.includeClient(), new TensuraItemModelProvider(event));
      event.getGenerator().m_236039_(event.includeServer(), new TensuraRecipeProvider(event));
      event.getGenerator().m_236039_(event.includeServer(), new TensuraLanguageProvider(event));
      event.getGenerator().m_236039_(event.includeServer(), new TensuraAdvancementsProvider(event));
      TensuraBlockTagProvider blockTagProvider = new TensuraBlockTagProvider(event);
      event.getGenerator().m_236039_(event.includeServer(), blockTagProvider);
      event.getGenerator().m_236039_(event.includeServer(), new TensuraFluidTagProvider(event));
      event.getGenerator().m_236039_(event.includeServer(), new TensuraItemTagProvider(event, blockTagProvider));
      event.getGenerator().m_236039_(event.includeServer(), new TensuraPaintingTagsProvider(event));
      event.getGenerator().m_236039_(event.includeServer(), new TensuraEntityTypeTagProvider(event));
      event.getGenerator().m_236039_(event.includeServer(), new TensuraStructureTagProvider(event));
      event.getGenerator().m_236039_(event.includeServer(), new TensuraBiomeTagProvider(event));
      event.getGenerator().m_236039_(event.includeServer(), new TensuraMoltenMaterialProvider(event));
      event.getGenerator().m_236039_(event.includeServer(), new TensuraGearEPProvider(event));
      event.getGenerator().m_236039_(event.includeServer(), new TensuraEntityEPProvider(event));
      event.getGenerator().m_236039_(event.includeServer(), new TensuraElementCombinationProvider(event));
      event.getGenerator().m_236039_(event.includeServer(), new TensuraOtherworlderSpawningProvider(event));
      event.getGenerator().m_236039_(event.includeServer(), new TensuraPoiTypesProvider(event));
      event.getGenerator().m_236039_(event.includeServer(), new TensuraLootTable(event));
      event.getGenerator().m_236039_(event.includeServer(), new TensuraLevelMagiculeModifiersProvider(event));
      event.getGenerator().m_236039_(event.includeServer(), new TensuraBiomeMagiculeModifiersProvider(event));
      event.getGenerator().m_236039_(event.includeServer(), new TensuraGlobalLootModifiersProvider(event));
   }

   public static Logger getLogger() {
      return LOGGER;
   }

   public static String getConfigFileName(String name) {
      return String.format("%s/%s.toml", "tensura-reincarnated", name);
   }

   public static void copyDefaultConfig(String path, Path destination) {
      String resourcePath = String.format("%s/%s/%s.toml", "defaultconfigs", "tensura-reincarnated", path);

      try {
         InputStream inputStream = Tensura.class.getClassLoader().getResourceAsStream(resourcePath);

         try {
            if (inputStream == null) {
               throw new IllegalArgumentException("Resource not found: " + resourcePath);
            }

            if (Files.exists(FMLPaths.GAMEDIR.get().resolve("defaultconfigs/tensura-reincarnated/reset"), new LinkOption[0])) {
               throw new IllegalArgumentException("No need, file already exist: " + resourcePath);
            }

            Path replace = FileUtils.getOrCreateDirectory(FMLPaths.GAMEDIR.get().resolve("defaultconfigs/" + getConfigFileName(path)), "tensura-reincarnated");
            Files.copy(inputStream, replace, new CopyOption[]{StandardCopyOption.REPLACE_EXISTING});
         } catch (Throwable var7) {
            if (inputStream != null) {
               try {
                  inputStream.close();
               } catch (Throwable var6) {
                  var7.addSuppressed(var6);
               }
            }

            throw var7;
         }

         if (inputStream != null) {
            inputStream.close();
         }
      } catch (Exception var8) {
         var8.printStackTrace();
      }

   }

   public void fmlServerConfig(FMLDedicatedServerSetupEvent event) {
      applyConfig();
   }

   public void serverStartedConfig(ServerStartedEvent event) {
      applyConfig();
   }

   public void clientLoadConfig(LoggingIn event) {
      applyConfig();
   }

   public static void applyConfig() {
      AccessorRangedAttribute accessorHP = (AccessorRangedAttribute)Attributes.f_22276_;
      accessorHP.setMaxValue((Double)TensuraConfig.INSTANCE.attributeConfig.maxHP.get());
      AccessorRangedAttribute accessorAttack = (AccessorRangedAttribute)Attributes.f_22281_;
      accessorAttack.setMaxValue((Double)TensuraConfig.INSTANCE.attributeConfig.maxAttack.get());
      AccessorRangedAttribute accessorArmor = (AccessorRangedAttribute)Attributes.f_22284_;
      accessorArmor.setMaxValue((Double)TensuraConfig.INSTANCE.attributeConfig.maxArmor.get());
      AccessorRangedAttribute accessorToughness = (AccessorRangedAttribute)Attributes.f_22285_;
      accessorToughness.setMaxValue((Double)TensuraConfig.INSTANCE.attributeConfig.maxToughness.get());
      EngravingEnchantment.loadConfig();
      HolyMilkItem.loadConfig();
      ViolentBreakArt.loadConfig();
   }
}
