package com.github.manasmods.tensura.enchantment;

import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.EnchantmentCategory;
import net.minecraft.world.item.enchantment.Enchantment.Rarity;

public class ElementalBoostEnchantment extends EngravingEnchantment implements IInherentEngrave {
   public ElementalBoostEnchantment() {
      super(Rarity.RARE, EnchantmentCategory.WEARABLE, EquipmentSlot.values());
   }

   public int m_6586_() {
      return 2;
   }

   public float getDamageBonus(int pLevel, DamageSource source, LivingEntity attacker, LivingEntity target, EquipmentSlot slot, float damage) {
      if (slot.m_20743_().equals(Type.HAND)) {
         return 0.0F;
      } else {
         return DamageSourceHelper.isNaturalEffects(source) ? 0.1F * (float)pLevel * damage : 0.0F;
      }
   }

   public boolean shouldHasFoil(ItemStack stack) {
      return !stack.m_204117_(TensuraTags.Items.HOLY_ARMAMENTS_ITEMS);
   }
}
