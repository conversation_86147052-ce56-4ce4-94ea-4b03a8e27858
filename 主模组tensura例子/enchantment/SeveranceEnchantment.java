package com.github.manasmods.tensura.enchantment;

import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.MobType;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.EnchantmentCategory;
import net.minecraft.world.item.enchantment.Enchantment.Rarity;

public class SeveranceEnchantment extends EngravingEnchantment implements IInherentEngrave {
   public SeveranceEnchantment() {
      super(Rarity.VERY_RARE, EnchantmentCategory.WEAPON, EquipmentSlot.MAINHAND);
   }

   public float getDamageBonus(int level, MobType mobType, ItemStack enchantedItem) {
      return (float)(3 * level);
   }

   public boolean shouldHasFoil(ItemStack stack) {
      if (stack.m_150930_((Item)TensuraToolItems.MOONLIGHT.get())) {
         return false;
      } else {
         return !stack.m_150930_((Item)TensuraToolItems.SPATIAL_BLADE.get());
      }
   }
}
