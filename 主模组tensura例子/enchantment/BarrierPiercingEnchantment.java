package com.github.manasmods.tensura.enchantment;

import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.item.enchantment.EnchantmentCategory;
import net.minecraft.world.item.enchantment.Enchantment.Rarity;

public class BarrierPiercingEnchantment extends EngravingEnchantment implements IInherentEngrave {
   public BarrierPiercingEnchantment() {
      super(Rarity.RARE, EnchantmentCategory.WEAPON, EquipmentSlot.MAINHAND);
   }

   public static void applyMoonlightEnchantments(ItemStack toStack) {
      if (toStack.m_150930_((Item)TensuraToolItems.MOONLIGHT.get()) && toStack.getEnchantmentLevel((Enchantment)TensuraEnchantments.BARRIER_PIERCING.get()) < 1) {
         toStack.m_41663_((Enchantment)TensuraEnchantments.BARRIER_PIERCING.get(), 1);
         toStack.m_41663_((Enchantment)TensuraEnchantments.SEVERANCE.get(), 1);
      }

   }

   public boolean shouldHasFoil(ItemStack stack) {
      return !stack.m_150930_((Item)TensuraToolItems.MOONLIGHT.get());
   }
}
