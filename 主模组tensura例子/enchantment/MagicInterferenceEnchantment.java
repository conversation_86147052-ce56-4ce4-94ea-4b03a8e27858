package com.github.manasmods.tensura.enchantment;

import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.item.enchantment.EnchantmentCategory;
import net.minecraft.world.item.enchantment.Enchantment.Rarity;

public class MagicInterferenceEnchantment extends EngravingEnchantment implements IInherentEngrave {
   public MagicInterferenceEnchantment() {
      super(Rarity.VERY_RARE, EnchantmentCategory.BREAKABLE, EquipmentSlot.values());
   }

   public static void applyMagicInterference(ItemStack toStack) {
      if (toStack.m_204117_(TensuraTags.Items.CHARYBDIS_ITEMS)) {
         if (toStack.getEnchantmentLevel((Enchantment)TensuraEnchantments.MAGIC_INTERFERENCE.get()) < 1) {
            toStack.m_41663_((Enchantment)TensuraEnchantments.MAGIC_INTERFERENCE.get(), 1);
         }
      }
   }
}
