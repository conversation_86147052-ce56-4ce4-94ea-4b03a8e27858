package com.github.manasmods.tensura.enchantment;

import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.EnchantmentCategory;
import net.minecraft.world.item.enchantment.Enchantment.Rarity;

public class MagicWeaponEnchantment extends EngravingEnchantment {
   public MagicWeaponEnchantment() {
      super(Rarity.VERY_RARE, EnchantmentCategory.WEAPON, EquipmentSlot.MAINHAND);
   }

   public void doAdditionalAttack(ItemStack stack, LivingEntity attacker, Entity target, int pLevel, float damage) {
      if (target.f_19802_ < 60) {
         target.f_19802_ = 0;
         target.m_6469_(TensuraDamageSources.genericMagic(attacker), damage * (float)pLevel);
      }
   }
}
