package com.github.manasmods.tensura.capability.race;

import net.minecraft.core.Direction;
import net.minecraft.nbt.CompoundTag;
import net.minecraftforge.common.capabilities.Capability;
import net.minecraftforge.common.capabilities.ICapabilityProvider;
import net.minecraftforge.common.capabilities.ICapabilitySerializable;
import net.minecraftforge.common.util.LazyOptional;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class TensuraPlayerCapabilityProvider implements ICapabilityProvider, ICapabilitySerializable<CompoundTag> {
   private final ITensuraPlayerCapability defaultCapability = new TensuraPlayerCapability();
   private final LazyOptional<ITensuraPlayerCapability> cap = LazyOptional.of(() -> {
      return this.defaultCapability;
   });

   @NotNull
   public <T> LazyOptional<T> getCapability(@NotNull Capability<T> cap, @Nullable Direction side) {
      return cap == TensuraPlayerCapability.CAPABILITY ? this.cap.cast() : LazyOptional.empty();
   }

   public CompoundTag serializeNBT() {
      return (CompoundTag)this.defaultCapability.serializeNBT();
   }

   public void deserializeNBT(CompoundTag nbt) {
      this.defaultCapability.deserializeNBT(nbt);
   }
}
