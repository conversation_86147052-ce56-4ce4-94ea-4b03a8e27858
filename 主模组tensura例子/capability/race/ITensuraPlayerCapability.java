package com.github.manasmods.tensura.capability.race;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.race.Race;
import java.util.List;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.util.INBTSerializable;
import org.jetbrains.annotations.Nullable;

public interface ITensuraPlayerCapability extends INBTSerializable<CompoundTag> {
   void setRace(LivingEntity var1, Race var2, boolean var3);

   @Nullable
   Race getRace();

   @Nullable
   Race getTrackedEvolution();

   void setTrackedEvolution(Player var1, @Nullable Race var2);

   void applyBaseAttributeModifiers(LivingEntity var1);

   void setSleepMode(int var1);

   int getSleepMode();

   void setBaseAura(double var1, LivingEntity var3);

   double getBaseAura();

   void setAura(double var1);

   double getAura();

   void setSpiritualForm(boolean var1);

   boolean isSpiritualForm();

   void setBaseMagicule(double var1, LivingEntity var3);

   void setBaseMagicule(double var1, LivingEntity var3, boolean var4);

   double getBaseMagicule();

   void setMagicule(double var1);

   double getMagicule();

   void setSoulPoints(int var1);

   int getSoulPoints();

   void setResetCounter(int var1);

   int getResetCounter();

   void setDemonLordSeed(boolean var1);

   boolean isDemonLordSeed();

   void setTrueDemonLord(boolean var1);

   boolean isTrueDemonLord();

   void setBlessed(boolean var1);

   boolean isBlessed();

   void setHeroEgg(boolean var1);

   boolean isHeroEgg();

   void setTrueHero(boolean var1);

   boolean isTrueHero();

   void setSprintSpeed(double var1, Player var3);

   double getSprintSpeed();

   void addIntrinsicSkill(ManasSkill var1);

   void clearIntrinsicSkills();

   List<ResourceLocation> getIntrinsicSkills();
}
