package com.github.manasmods.tensura.capability.smithing;

import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.SyncSmithingCapabilityPacket;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.tags.ItemTags;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.capabilities.Capability;
import net.minecraftforge.common.capabilities.CapabilityManager;
import net.minecraftforge.common.capabilities.CapabilityToken;
import net.minecraftforge.common.util.LazyOptional;
import net.minecraftforge.event.AttachCapabilitiesEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber;
import net.minecraftforge.fml.common.Mod.EventBusSubscriber.Bus;
import net.minecraftforge.network.PacketDistributor;

@EventBusSubscriber(
   modid = "tensura",
   bus = Bus.FORGE
)
public class SmithingCapability implements ISmithingCapability {
   static final Capability<ISmithingCapability> CAPABILITY = CapabilityManager.get(new CapabilityToken<ISmithingCapability>() {
   });
   private static final ResourceLocation IDENTIFIER = new ResourceLocation("tensura", "schematics");
   private final List<ResourceLocation> knownSchematics = new ArrayList();

   @SubscribeEvent
   public static void attach(AttachCapabilitiesEvent<Entity> e) {
      ItemTags.create(new ResourceLocation("tensura", "test"));
      if (e.getObject() instanceof Player) {
         e.addCapability(IDENTIFIER, new SmithingCapabilityProvider());
      }

   }

   public static LazyOptional<ISmithingCapability> getFrom(Player player) {
      return player.getCapability(CAPABILITY);
   }

   public static void sync(Player player) {
      if (player instanceof ServerPlayer) {
         ServerPlayer serverPlayer = (ServerPlayer)player;
         getFrom(serverPlayer).ifPresent((data) -> {
            TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
               return serverPlayer;
            }), new SyncSmithingCapabilityPacket((CompoundTag)data.serializeNBT(), serverPlayer.m_19879_()));
         });
      }

   }

   public boolean hasSchematic(ResourceLocation schematic) {
      return this.knownSchematics.contains(schematic);
   }

   public void unlockSchematic(ResourceLocation schematicId) {
      this.knownSchematics.add(schematicId);
   }

   public void clearSchematics() {
      this.knownSchematics.clear();
   }

   public CompoundTag serializeNBT() {
      CompoundTag tag = new CompoundTag();

      for(int i = 0; i < this.knownSchematics.size(); ++i) {
         tag.m_128359_(String.valueOf(i), ((ResourceLocation)this.knownSchematics.get(i)).toString());
      }

      return tag;
   }

   public void deserializeNBT(CompoundTag nbt) {
      this.knownSchematics.clear();
      nbt.m_128431_().forEach((s) -> {
         this.knownSchematics.add(ResourceLocation.m_135820_(nbt.m_128461_(s)));
      });
   }
}
