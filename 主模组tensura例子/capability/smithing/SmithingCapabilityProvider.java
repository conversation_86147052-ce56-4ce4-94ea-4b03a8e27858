package com.github.manasmods.tensura.capability.smithing;

import net.minecraft.core.Direction;
import net.minecraft.nbt.CompoundTag;
import net.minecraftforge.common.capabilities.Capability;
import net.minecraftforge.common.capabilities.ICapabilitySerializable;
import net.minecraftforge.common.util.LazyOptional;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class SmithingCapabilityProvider implements ICapabilitySerializable<CompoundTag> {
   private final ISmithingCapability data = new SmithingCapability();
   private final LazyOptional<ISmithingCapability> lazyOptional = LazyOptional.of(() -> {
      return this.data;
   });

   public CompoundTag serializeNBT() {
      return (CompoundTag)this.data.serializeNBT();
   }

   public void deserializeNBT(CompoundTag nbt) {
      this.data.deserializeNBT(nbt);
   }

   @NotNull
   public <T> LazyOptional<T> getCapability(@NotNull Capability<T> cap, @Nullable Direction side) {
      return this.getCapability(cap);
   }

   @NotNull
   public <T> LazyOptional<T> getCapability(@NotNull Capability<T> cap) {
      return SmithingCapability.CAPABILITY.orEmpty(cap, this.lazyOptional);
   }
}
