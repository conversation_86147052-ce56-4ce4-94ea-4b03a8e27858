package com.github.manasmods.tensura.capability.ep;

import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.world.entity.LivingEntity;
import net.minecraftforge.common.util.INBTSerializable;

public interface ITensuraEPCapability extends INBTSerializable<CompoundTag> {
   double getEP();

   void setEP(LivingEntity var1, double var2);

   void setEP(LivingEntity var1, double var2, boolean var4);

   double getGainedEP();

   void setGainedEP(double var1);

   double getCurrentEP();

   void setCurrentEP(LivingEntity var1, double var2);

   double getSpiritualHealth();

   void setSpiritualHealth(double var1);

   void setChaos(boolean var1);

   boolean isChaos();

   void setMajin(boolean var1);

   boolean isMajin();

   boolean isSkipEPDrop();

   void setSkipEPDrop(boolean var1);

   boolean isHarvestGift();

   void setHarvestGift(boolean var1);

   boolean isNameable();

   void setNameable(boolean var1);

   @Nullable
   String getName();

   void setName(@Nullable String var1);

   @Nullable
   UUID getPermanentOwner();

   void setPermanentOwner(@Nullable UUID var1);

   @Nullable
   UUID getTemporaryOwner();

   void setTemporaryOwner(@Nullable UUID var1);

   boolean isTargetNeutral(UUID var1);

   void addNeutralTarget(UUID var1);

   void removeNeutralTarget(UUID var1);

   void clearNeutralTargets();

   int getHumanKill();

   void setHumanKill(int var1);
}
