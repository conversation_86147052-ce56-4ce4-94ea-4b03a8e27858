package com.github.manasmods.tensura.capability.effects;

import net.minecraft.core.Direction;
import net.minecraft.nbt.CompoundTag;
import net.minecraftforge.common.capabilities.Capability;
import net.minecraftforge.common.capabilities.ICapabilityProvider;
import net.minecraftforge.common.capabilities.ICapabilitySerializable;
import net.minecraftforge.common.util.LazyOptional;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class TensuraEffectsCapabilityProvider implements ICapabilityProvider, ICapabilitySerializable<CompoundTag> {
   private final ITensuraEffectsCapability defaultCapability = new TensuraEffectsCapability();
   private final LazyOptional<ITensuraEffectsCapability> cap = LazyOptional.of(() -> {
      return this.defaultCapability;
   });

   @NotNull
   public <T> LazyOptional<T> getCapability(@NotNull Capability<T> cap, @Nullable Direction side) {
      return cap == TensuraEffectsCapability.CAPABILITY ? this.cap.cast() : LazyOptional.empty();
   }

   public CompoundTag serializeNBT() {
      return (CompoundTag)this.defaultCapability.serializeNBT();
   }

   public void deserializeNBT(CompoundTag nbt) {
      this.defaultCapability.deserializeNBT(nbt);
   }
}
