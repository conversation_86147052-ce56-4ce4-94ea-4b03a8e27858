package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.attribute.ManasCoreAttributes;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.api.entity.ai.FlyingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.ai.UndergroundTargetingEntitiesGoal;
import com.github.manasmods.tensura.api.entity.subclass.IGiantMob;
import com.github.manasmods.tensura.api.entity.subclass.ITensuraMount;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.OrbitSwoopFLyingEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Nullable;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.protocol.game.ClientboundAnimatePacket;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.util.RandomSource;
import net.minecraft.world.Difficulty;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.MenuProvider;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.HasCustomInventoryScreen;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.MoverType;
import net.minecraft.world.entity.PlayerRideable;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.ai.targeting.TargetingConditions;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ChestMenu;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.common.Tags.Items;
import net.minecraftforge.event.ForgeEventFactory;
import net.minecraftforge.fluids.FluidType;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class MegalodonEntity extends OrbitSwoopFLyingEntity implements IAnimatable, ITensuraMount, PlayerRideable, IGiantMob, HasCustomInventoryScreen {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Boolean> SADDLED;
   private static final EntityDataAccessor<Boolean> CHESTED;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);
   public int miscAnimationTicks = 0;
   public SimpleContainer inventory;
   public MenuProvider inventoryMenu;
   private boolean hasChestVarChanged = false;
   @Nullable
   public CharybdisEntity charybdis;

   public MegalodonEntity(EntityType<? extends MegalodonEntity> type, Level level) {
      super(type, level);
      this.f_21364_ = 100;
      this.f_19793_ = 2.0F;
      this.initInventory();
      this.m_20242_(true);
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22276_, 70.0D).m_22268_(Attributes.f_22281_, 20.0D).m_22268_(Attributes.f_22284_, 10.0D).m_22268_(Attributes.f_22277_, 48.0D).m_22268_(Attributes.f_22279_, 0.4000000059604645D).m_22268_(Attributes.f_22280_, 0.5D).m_22268_(Attributes.f_22278_, 1.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 5.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new MegalodonEntity.MegalodonSwoopAttackGoal(this));
      this.f_21345_.m_25352_(3, new FlyingFollowOwnerGoal(this, 0.7D, 10.0F, 4.0F, true, false));
      this.f_21345_.m_25352_(4, new MegalodonEntity.MegalodonSwoopAttackGoal(this));
      this.f_21345_.m_25352_(5, new MegalodonEntity.MegalodonAttackGoal(this));
      this.f_21345_.m_25352_(6, new OrbitSwoopFLyingEntity.CircleFlightGoal(this, 8.0F) {
         protected BlockPos getBaseOrbitChange() {
            CharybdisEntity owner = MegalodonEntity.this.getCharybdis();
            return owner != null ? owner.getOrbitPos().m_6625_(20) : this.entity.getOrbitPos();
         }
      });
      this.f_21345_.m_25352_(8, new TensuraTamableEntity.FlyingWanderAroundPosGoal(this, 1.0D, 64) {
         public boolean m_8036_() {
            return !MegalodonEntity.this.m_21824_() && MegalodonEntity.this.m_5448_() == null && MegalodonEntity.this.isWandering() ? false : super.m_8036_();
         }
      });
      this.f_21345_.m_25352_(10, new RandomLookAroundGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(3, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(4, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{MegalodonEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(5, new UndergroundTargetingEntitiesGoal(this, Player.class, false, 128.0F, this::shouldAttackPlayer));
      this.f_21346_.m_25352_(7, new UndergroundTargetingEntitiesGoal(this, LivingEntity.class, false, 128.0F, this::shouldAttack));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   public boolean shouldAttackPlayer(LivingEntity entity) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         if (player.m_7500_()) {
            return false;
         }
      }

      if (!entity.m_5833_()) {
         if (this.m_21826_() != null) {
            if (entity.m_7307_(this.m_21826_())) {
               return false;
            } else {
               return this.m_21826_().m_21214_() == entity || this.m_21826_().m_21188_() == entity;
            }
         } else {
            return true;
         }
      } else {
         return false;
      }
   }

   public boolean shouldAttack(LivingEntity entity) {
      if (entity == this) {
         return false;
      } else if (!entity.m_6084_()) {
         return false;
      } else if (this.m_7307_(entity)) {
         return false;
      } else if (entity.m_6095_().m_204039_(TensuraTags.EntityTypes.MEGALODON_NEUTRAL)) {
         return false;
      } else if (this.m_21826_() == null) {
         return TensuraEPCapability.getEP(entity) > 1000.0D;
      } else if (entity.m_7307_(this.m_21826_())) {
         return false;
      } else if (entity instanceof Mob) {
         Mob mob = (Mob)entity;
         return mob.m_5448_() == this.m_21826_();
      } else {
         return this.m_21826_().m_21214_() == entity || this.m_21826_().m_21188_() == entity;
      }
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(SADDLED, Boolean.FALSE);
      this.f_19804_.m_135372_(CHESTED, Boolean.FALSE);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128379_("Saddled", this.isSaddled());
      compound.m_128379_("Chested", this.isChested());
      if (this.inventory != null) {
         ListTag listTag = new ListTag();

         for(int i = 0; i < this.inventory.m_6643_(); ++i) {
            ItemStack itemstack = this.inventory.m_8020_(i);
            if (!itemstack.m_41619_()) {
               CompoundTag CompoundNBT = new CompoundTag();
               CompoundNBT.m_128344_("Slot", (byte)i);
               itemstack.m_41739_(CompoundNBT);
               listTag.add(CompoundNBT);
            }
         }

         compound.m_128365_("Items", listTag);
      }

   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.setSaddled(compound.m_128471_("Saddled"));
      this.setChested(compound.m_128471_("Chested"));
      ListTag listTag;
      int i;
      CompoundTag CompoundNBT;
      int j;
      if (this.inventory != null) {
         listTag = compound.m_128437_("Items", 10);
         this.initInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.inventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      } else {
         listTag = compound.m_128437_("Items", 10);
         this.initInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.initInventory();
            this.inventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      }

   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public boolean isSaddled() {
      return (Boolean)this.f_19804_.m_135370_(SADDLED);
   }

   public void setSaddled(boolean saddled) {
      this.f_19804_.m_135381_(SADDLED, saddled);
   }

   public boolean isChested() {
      return (Boolean)this.f_19804_.m_135370_(CHESTED);
   }

   public void setChested(boolean chested) {
      this.f_19804_.m_135381_(CHESTED, chested);
      this.hasChestVarChanged = true;
   }

   public boolean m_7307_(Entity entity) {
      if (super.m_7307_(entity)) {
         return true;
      } else if (entity instanceof MegalodonEntity) {
         MegalodonEntity megalodon = (MegalodonEntity)entity;
         return megalodon.m_21824_() == this.m_21824_();
      } else if (entity instanceof CharybdisEntity) {
         CharybdisEntity charybdis = (CharybdisEntity)entity;
         return charybdis.m_21824_() == this.m_21824_();
      } else {
         return false;
      }
   }

   public boolean isPushedByFluid(FluidType type) {
      return false;
   }

   public boolean canDrownInFluidType(FluidType type) {
      return false;
   }

   public int m_8132_() {
      return 1;
   }

   public int m_8085_() {
      return 1;
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19310_ || source == DamageSource.f_19314_ || source == DamageSource.f_19325_ || source == DamageSource.f_19309_ || super.m_6673_(source);
   }

   public boolean breakableBlocks(LivingEntity entity, BlockPos pos, BlockState state) {
      return state.m_204336_(TensuraTags.Blocks.BOSS_IMMUNE) ? false : ForgeEventFactory.onEntityDestroyBlock(entity, pos, state);
   }

   public boolean dropBlockLoot(LivingEntity entity, BlockState state) {
      if (this.m_21023_((MobEffect)TensuraMobEffects.RAMPAGE.get())) {
         return false;
      } else {
         return !state.m_204336_(TensuraTags.Blocks.SKILL_BREAK_EASY);
      }
   }

   protected boolean m_8028_() {
      return true;
   }

   public boolean m_6785_(double pDistanceToClosestPlayer) {
      return this.getSpawnType() == MobSpawnType.MOB_SUMMONED ? false : super.m_6785_(pDistanceToClosestPlayer);
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.megalodonSpawnRate.get(), this.m_217043_(), pSpawnReason);
   }

   public static boolean checkMegalodonSpawnRules(EntityType<MegalodonEntity> fish, LevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      return pLevel.m_46791_() != Difficulty.PEACEFUL && pPos.m_123342_() > 90 && SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.megalodonSpawnRate.get(), pRandom, MobSpawnType.NATURAL) && checkFlyingSpawnRules(fish, pLevel, pSpawnType, pPos, pRandom);
   }

   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      int y = Math.min(25 + this.f_19796_.m_188503_(6), 250 - (int)this.m_20186_());
      this.setOrbitPos(this.m_20183_().m_6630_(y));
      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   @Nullable
   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      return null;
   }

   public void m_8119_() {
      super.m_8119_();
      LivingEntity target;
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         if (this.getMiscAnimation() == 2 && this.miscAnimationTicks == 5) {
            target = this.m_5448_();
            if (target != null) {
               this.m_7618_(Anchor.EYES, target.m_146892_());
               float damage = (float)(this.m_21133_(Attributes.f_22281_) * this.m_21133_((Attribute)ManasCoreAttributes.CRIT_MULTIPLIER.get()));
               target.m_6469_(DamageSource.m_19370_(this), damage);
               SkillHelper.knockBack(this, target, 3.0F);
               Level var10 = this.f_19853_;
               if (var10 instanceof ServerLevel) {
                  ServerLevel serverLevel = (ServerLevel)var10;
                  serverLevel.m_7726_().m_8394_(target, new ClientboundAnimatePacket(target, 4));
               }

               this.m_5496_(SoundEvents.f_12313_, 5.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            }
         } else if (this.getMiscAnimation() == 3) {
            if (this.miscAnimationTicks == 5) {
               if (this.m_5448_() != null) {
                  this.m_7618_(Anchor.EYES, this.m_5448_().m_146892_());
               }

               SkillHelper.riptidePush(this, 4.0F);
               this.m_5834_();
               TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123766_, 2.0D);
               TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123766_, 3.0D);
               this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12519_, SoundSource.NEUTRAL, 5.0F, 1.0F);
            } else if (this.miscAnimationTicks > 5) {
               this.f_19812_ = true;
               AABB aabb = this.m_20191_().m_82400_(2.5D);
               List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, aabb, (entity) -> {
                  return !this.m_7307_(entity) && entity != this.m_21826_() && entity != this;
               });
               if (!livingEntityList.isEmpty()) {
                  float damage = (float)(this.m_21133_(Attributes.f_22281_) * 2.0D);

                  LivingEntity target;
                  for(Iterator var4 = livingEntityList.iterator(); var4.hasNext(); target.f_19864_ = true) {
                     target = (LivingEntity)var4.next();
                     target.m_6469_(DamageSource.m_19370_(this), damage);
                     target.m_20256_(target.m_20184_().m_82549_(this.m_20184_()));
                  }
               }
            }
         } else if (this.getMiscAnimation() == 4 && this.miscAnimationTicks == 10) {
            this.areaAttack(1.5D, 2.5F, 4.0F);
            TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123796_, 3.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123766_, 3.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123766_, 4.0D);
            this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11913_, SoundSource.NEUTRAL, 5.0F, 1.0F);
         }

         if (this.miscAnimationTicks >= this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

      if (this.hasChestVarChanged && this.inventory != null && !this.isChested()) {
         for(int i = 3; i < 18; ++i) {
            if (!this.inventory.m_8020_(i).m_41619_()) {
               if (!this.f_19853_.f_46443_) {
                  this.m_5552_(this.inventory.m_8020_(i), 1.0F);
               }

               this.inventory.m_8016_(i);
            }
         }

         this.hasChestVarChanged = false;
      }

      if (!this.f_19853_.f_46443_) {
         if (this.m_146895_() != null) {
            this.targetingMovementHelper();
            target = this.m_5448_();
            if (target != null && target.m_6084_()) {
               this.m_7618_(Anchor.EYES, target.m_146892_());
            }
         }

      }
   }

   protected void handleFlying() {
      super.handleFlying();
      if (!this.f_19853_.m_5776_()) {
         LivingEntity controller = this.getControllingPassenger();
         SimpleContainer container = this.isChested() ? this.inventory : null;
         if (!this.m_21824_()) {
            this.breakBlocks(this, 1.0F, true, 0, container, true);
         } else if (controller != null && this.m_21830_(controller)) {
            this.breakBlocks(this, 1.0F, false, 0, container);
         }

         if (controller != null) {
            if (controller.m_146909_() <= 20.0F) {
               this.digBlocks(this, 1.0F, 1, 1.0F, false, container);
            } else {
               this.digBlocks(this, 1.0F, 0, 1.0F, controller.m_146909_() >= 40.0F, container);
            }

         }
      }
   }

   private int getAnimationTick(int miscAnimation) {
      byte var10000;
      switch(miscAnimation) {
      case 1:
         var10000 = 5;
         break;
      case 2:
         var10000 = 13;
         break;
      case 3:
         var10000 = 16;
         break;
      default:
         var10000 = 21;
      }

      return var10000;
   }

   public void mountAbility(Player rider) {
      if (this.getMiscAnimation() != 1) {
         if (this.getMiscAnimation() != 2) {
            if (this.getMiscAnimation() != 4) {
               this.setMiscAnimation(3);
            }
         }
      }
   }

   public void areaAttack(double multiplier, float strength, float range) {
      AABB aabb = this.m_20191_().m_82400_((double)range);
      List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, aabb, (entity) -> {
         return !this.m_7307_(entity) && entity != this.m_21826_() && !entity.equals(this);
      });
      if (!livingEntityList.isEmpty()) {
         Iterator var7 = livingEntityList.iterator();

         while(var7.hasNext()) {
            LivingEntity target = (LivingEntity)var7.next();
            target.m_6469_(DamageSource.m_19370_(this), (float)(this.m_21133_(Attributes.f_22281_) * multiplier));
            SkillHelper.knockBack(this, target, strength);
         }

      }
   }

   private void initInventory() {
      SimpleContainer chest = this.inventory;
      this.inventory = new SimpleContainer(54) {
         public boolean m_6542_(Player player) {
            return MegalodonEntity.this.m_6084_() && !MegalodonEntity.this.f_19817_;
         }
      };
      if (chest != null) {
         int i = Math.min(chest.m_6643_(), this.inventory.m_6643_());

         for(int j = 0; j < i; ++j) {
            ItemStack itemstack = chest.m_8020_(j);
            if (!itemstack.m_41619_()) {
               this.inventory.m_6836_(j, itemstack.m_41777_());
            }
         }
      }

   }

   public void m_213583_(Player pPlayer) {
      if (this.isChested()) {
         if (this.inventory != null) {
            pPlayer.m_5893_(this.getMenu());
            if (!pPlayer.f_19853_.f_46443_) {
               this.m_146852_(GameEvent.f_157803_, pPlayer);
            }

         }
      }
   }

   public void m_6667_(DamageSource cause) {
      super.m_6667_(cause);
      if (!this.f_19853_.m_5776_()) {
         if (this.inventory != null) {
            if (!this.m_6084_()) {
               for(int i = 0; i < this.inventory.m_6643_(); ++i) {
                  ItemStack itemstack = this.inventory.m_8020_(i);
                  if (!itemstack.m_41619_()) {
                     this.m_5552_(itemstack, 0.0F);
                  }
               }

            }
         }
      }
   }

   public MenuProvider getMenu() {
      if (this.inventoryMenu == null) {
         this.inventoryMenu = new MenuProvider() {
            public AbstractContainerMenu m_7208_(int menu, Inventory inventory, Player player) {
               return new ChestMenu(MenuType.f_39962_, menu, inventory, MegalodonEntity.this.inventory, 6);
            }

            public Component m_5446_() {
               return Component.m_237115_("container.chest");
            }
         };
      }

      return this.inventoryMenu;
   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_41720_() instanceof HealingPotionItem ? false : pStack.m_41614_();
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (this.f_19853_.f_46443_) {
            boolean flag = this.m_21830_(player) || this.m_21824_();
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         } else if (this.m_21824_() && this.m_21830_(player)) {
            if (!this.m_6162_()) {
               Item item = itemstack.m_41720_();
               if (item.equals(TensuraMaterialItems.MONSTER_SADDLE.get()) && !this.isSaddled()) {
                  if (!player.m_150110_().f_35937_) {
                     itemstack.m_41774_(1);
                  }

                  this.setSaddled(true);
                  this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  return InteractionResult.m_19078_(this.f_19853_.f_46443_);
               }

               if (!this.isChested() && itemstack.m_204117_(Items.CHESTS_WOODEN)) {
                  this.setChested(true);
                  this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  if (!player.m_150110_().f_35937_) {
                     itemstack.m_41774_(1);
                  }

                  return InteractionResult.m_19078_(this.f_19853_.f_46443_);
               }

               if (this.isChested() && item.equals(net.minecraft.world.item.Items.f_42574_)) {
                  this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  this.m_19998_(Blocks.f_50087_);

                  for(int i = 0; i < this.inventory.m_6643_(); ++i) {
                     this.m_19983_(this.inventory.m_8020_(i));
                  }

                  this.inventory.m_6211_();
                  this.setChested(false);
                  return InteractionResult.SUCCESS;
               }

               if (this.isSaddled() && item.equals(net.minecraft.world.item.Items.f_42574_)) {
                  this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  this.m_19998_((ItemLike)TensuraMaterialItems.MONSTER_SADDLE.get());
                  this.setSaddled(false);
                  return InteractionResult.m_19078_(this.f_19853_.f_46443_);
               }
            }

            if (player.m_36341_() || !this.isSaddled() && !this.isChested()) {
               this.commanding(player);
            } else if (player.m_146895_() == null && this.isSaddled()) {
               this.m_21839_(false);
               this.setWandering(false);
               player.m_7998_(this, false);
            } else if (this.isChested()) {
               this.m_213583_(player);
            }

            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         } else {
            return super.m_6071_(player, hand);
         }
      }
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack) && this.m_21223_() < this.m_21233_()) {
         if (!player.m_7500_()) {
            itemstack.m_41774_(1);
         }

         this.m_8035_();
         this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
         return InteractionResult.SUCCESS;
      } else {
         return InteractionResult.PASS;
      }
   }

   public void m_8035_() {
      super.m_8035_();
      this.setMiscAnimation(1);
      this.m_5634_(3.0F);
   }

   public boolean m_20068_() {
      if (super.m_20068_()) {
         return true;
      } else {
         return this.getControllingPassenger() != null && !this.m_20096_();
      }
   }

   public boolean m_6146_() {
      return true;
   }

   @Nullable
   public LivingEntity getControllingPassenger() {
      Iterator var1 = this.m_20197_().iterator();

      while(var1.hasNext()) {
         Entity passenger = (Entity)var1.next();
         if (passenger instanceof Player) {
            Player player = (Player)passenger;
            if (player.equals(this.m_21826_())) {
               return player;
            }
         }
      }

      return null;
   }

   public void m_7332_(Entity passenger) {
      if (this.m_20363_(passenger)) {
         passenger.m_183634_();
         float radius = 0.4F;
         float angle = 0.017453292F * this.f_20883_;
         double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
         double extraZ = (double)(radius * Mth.m_14089_(angle));
         double yOffset = this.m_20186_() - 0.25D + this.m_6048_() + passenger.m_6049_();
         passenger.m_6034_(this.m_20185_() + extraX, yOffset, this.m_20189_() + extraZ);
      }
   }

   public void m_7023_(Vec3 pTravelVector) {
      if (this.m_6084_()) {
         LivingEntity controller = this.getControllingPassenger();
         if (this.m_20160_() && controller != null) {
            this.m_146922_(controller.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(controller.m_146909_() * 0.5F);
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.f_20883_;
            float f = controller.f_20900_ * 0.5F;
            float f1 = controller.f_20902_;
            if (f1 <= 0.0F) {
               f1 *= 0.25F;
            }

            this.f_20887_ = this.m_6113_() * 0.5F;
            if (this.m_6109_()) {
               float speed = (float)this.m_21133_(Attributes.f_22279_) / 2.0F;
               if (controller.m_20142_()) {
                  speed = (float)((double)speed * 1.5D);
               }

               this.m_7910_(speed);
               if (controller.f_20899_) {
                  this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.1D, 0.0D));
               } else if (TensuraKeybinds.MOUNT_DESCENDING.m_90857_()) {
                  this.descending(this, controller);
               }

               super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
            } else if (controller instanceof Player && this.getMiscAnimation() != 3) {
               this.m_20256_(Vec3.f_82478_);
            }

            this.m_146872_();
         } else {
            this.f_20887_ = 0.02F;
            if (this.m_6142_() && this.m_20069_()) {
               this.m_19920_(this.m_6113_(), pTravelVector);
               this.m_6478_(MoverType.SELF, this.m_20184_());
               this.m_20256_(this.m_20184_().m_82490_(0.9D));
               if (this.m_5448_() == null) {
                  this.m_20256_(this.m_20184_().m_82520_(0.0D, -0.005D, 0.0D));
               }
            } else {
               super.m_7023_(pTravelVector);
            }
         }
      }

   }

   protected void m_5907_() {
      super.m_5907_();
      if (this.isSaddled() && !this.m_9236_().m_5776_()) {
         this.m_19998_((ItemLike)TensuraMaterialItems.MONSTER_SADDLE.get());
      }

      if (this.isChested()) {
         if (!this.f_19853_.f_46443_) {
            this.m_19998_(Blocks.f_50087_);

            for(int i = 0; i < this.inventory.m_6643_(); ++i) {
               this.m_19983_(this.inventory.m_8020_(i));
            }
         }

         this.inventory.m_6211_();
         this.setChested(false);
      }

   }

   public SoundSource m_5720_() {
      return SoundSource.HOSTILE;
   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_11701_;
   }

   protected SoundEvent m_7975_(DamageSource source) {
      return SoundEvents.f_11895_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_11703_;
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_20072_()) {
         if (event.isMoving()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.megalodon.swim", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.megalodon.swim_idle", EDefaultLoopTypes.LOOP));
         }
      } else if (event.isMoving() && !this.m_21825_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.megalodon.fly", EDefaultLoopTypes.LOOP));
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.megalodon.fly_idle", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState playOncePredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.megalodon.bite", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.megalodon.fling", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.megalodon.roll", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 4) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.megalodon.take_off", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "playOnceController", 0.0F, this::playOncePredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   @Nullable
   public CharybdisEntity getCharybdis() {
      return this.charybdis;
   }

   public void setCharybdis(@Nullable CharybdisEntity charybdis) {
      this.charybdis = charybdis;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(MegalodonEntity.class, EntityDataSerializers.f_135028_);
      SADDLED = SynchedEntityData.m_135353_(MegalodonEntity.class, EntityDataSerializers.f_135035_);
      CHESTED = SynchedEntityData.m_135353_(MegalodonEntity.class, EntityDataSerializers.f_135035_);
   }

   protected static class MegalodonSwoopAttackGoal extends OrbitSwoopFLyingEntity.SwoopAttackGoal {
      public final MegalodonEntity megalodon;

      public MegalodonSwoopAttackGoal(MegalodonEntity entity) {
         super(entity, false, 0.7D);
         this.megalodon = entity;
      }

      protected void performAttack(LivingEntity target) {
         double distance = (double)this.megalodon.m_20270_(target);
         if (this.megalodon.f_19796_.m_188503_(10) == 1 && distance <= 10.0D) {
            this.megalodon.m_7618_(Anchor.EYES, target.m_146892_());
            this.megalodon.setMiscAnimation(3);
            this.entity.behavior = 0;
         } else {
            int attack = this.megalodon.f_19796_.m_188499_() ? 1 : 2;
            double attackReach = this.getAttackReach(target);
            if (attack == 2) {
               attackReach += 3.0D;
            }

            if (this.entity.m_20191_().m_82400_(attackReach).m_82381_(target.m_20191_())) {
               this.megalodon.setMiscAnimation(attack);
               if (attack == 1) {
                  this.entity.m_7327_(target);
                  this.entity.m_5496_(SoundEvents.f_11865_, 5.0F, 0.95F + this.entity.m_217043_().m_188501_() * 0.1F);
               }

               this.entity.behavior = 0;
            }
         }

      }

      protected double getAttackReach(LivingEntity target) {
         return Math.sqrt((double)(this.megalodon.m_20205_() * 2.0F * this.megalodon.m_20205_() * 2.0F + target.m_20205_()));
      }
   }

   private static class MegalodonAttackGoal extends Goal {
      private int tickDelay;
      private final MegalodonEntity megalodon;
      private LivingEntity target;

      public MegalodonAttackGoal(MegalodonEntity megalodon) {
         this.megalodon = megalodon;
      }

      public boolean m_183429_() {
         return true;
      }

      public boolean m_8036_() {
         if (this.megalodon.m_21827_()) {
            return false;
         } else {
            LivingEntity entity = this.megalodon.m_5448_();
            if (entity != null) {
               this.target = entity;
               return this.megalodon.m_21040_(entity, TargetingConditions.m_148352_().m_148355_().m_26893_());
            } else {
               return false;
            }
         }
      }

      public void m_8056_() {
         this.tickDelay = 100;
         this.megalodon.behavior = 0;
         this.updateOrbit();
      }

      public void m_8037_() {
         if (this.megalodon.behavior == 0) {
            --this.tickDelay;
            if (this.tickDelay <= 0) {
               if (this.target != null) {
                  double distance = (double)this.target.m_20270_(this.megalodon);
                  if (distance <= 10.0D && this.megalodon.f_19796_.m_188503_(5) == 1) {
                     this.tickDelay = 20;
                     this.megalodon.setMiscAnimation(4);
                  } else {
                     this.megalodon.behavior = 2;
                     this.updateOrbit();
                     this.tickDelay = (4 + this.megalodon.f_19796_.m_188503_(4)) * 20;
                  }

               }
            }
         }
      }

      private void updateOrbit() {
         if (this.target.m_20096_() || this.target.m_20072_()) {
            int y = Math.min(25 + this.megalodon.f_19796_.m_188503_(6), 250 - (int)this.megalodon.m_20186_());
            this.megalodon.setOrbitPos(this.target.m_20183_().m_6630_(y));
         }
      }
   }
}
