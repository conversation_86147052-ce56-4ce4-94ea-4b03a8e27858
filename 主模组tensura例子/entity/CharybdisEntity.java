package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.api.entity.ai.FlyingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.ai.UndergroundTargetingEntitiesGoal;
import com.github.manasmods.tensura.api.entity.subclass.IGiantMob;
import com.github.manasmods.tensura.api.entity.subclass.ITensuraMount;
import com.github.manasmods.tensura.block.CharybdisCoreBlock;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.barrier.BarrierPart;
import com.github.manasmods.tensura.entity.magic.misc.TempestScaleEntity;
import com.github.manasmods.tensura.entity.projectile.TensuraFallingBlock;
import com.github.manasmods.tensura.entity.template.OrbitSwoopFLyingEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Nullable;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerBossEvent;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.BossEvent.BossBarColor;
import net.minecraft.world.BossEvent.BossBarOverlay;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.MoverType;
import net.minecraft.world.entity.PlayerRideable;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.SpawnPlacements;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.ai.targeting.TargetingConditions;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.NaturalSpawner;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.ClipContext.Block;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.Explosion.BlockInteraction;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.SculkSensorPhase;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.event.ForgeEventFactory;
import net.minecraftforge.fluids.FluidType;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class CharybdisEntity extends OrbitSwoopFLyingEntity implements IAnimatable, ITensuraMount, PlayerRideable, IGiantMob {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Integer> MEGALODON_SUMMON;
   private static final EntityDataAccessor<BlockPos> BEAM_TARGET;
   private static final EntityDataAccessor<Float> SIZE;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);
   public int miscAnimationTicks = 0;
   @Nullable
   private LivingEntity beamEntity;
   private Vec3 beamRandomVec;
   private final ServerBossEvent bossEvent;

   public CharybdisEntity(EntityType<? extends CharybdisEntity> type, Level level) {
      super(type, level);
      this.beamRandomVec = Vec3.f_82478_;
      this.bossEvent = new ServerBossEvent(this.m_5446_(), BossBarColor.BLUE, BossBarOverlay.NOTCHED_20);
      this.f_21364_ = 12000;
      this.f_19793_ = 5.0F;
      this.f_19811_ = true;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22276_, 3000.0D).m_22268_(Attributes.f_22281_, 100.0D).m_22268_(Attributes.f_22284_, 20.0D).m_22268_(Attributes.f_22277_, 256.0D).m_22268_(Attributes.f_22279_, 0.30000001192092896D).m_22268_(Attributes.f_22280_, 0.009999999776482582D).m_22268_(Attributes.f_22278_, 1.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 7.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new CharybdisEntity.CharybdisAttackGoal(this));
      this.f_21345_.m_25352_(3, new FlyingFollowOwnerGoal(this, 0.7D, 40.0F, 80.0F, true, false));
      this.f_21345_.m_25352_(7, new OrbitSwoopFLyingEntity.CircleFlightGoal(this, 16.0F));
      this.f_21345_.m_25352_(8, new TensuraTamableEntity.FlyingWanderAroundPosGoal(this, 1.0D, 64) {
         public boolean m_8036_() {
            return !CharybdisEntity.this.m_21824_() && CharybdisEntity.this.m_5448_() == null && CharybdisEntity.this.isWandering() ? false : super.m_8036_();
         }
      });
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(3, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(4, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new NearestAttackableTargetGoal(this, Player.class, 10, false, false, this::m_21674_));
      this.f_21346_.m_25352_(5, new UndergroundTargetingEntitiesGoal(this, Player.class, false, 128.0F, this::shouldAttackPlayer));
      this.f_21346_.m_25352_(6, new UndergroundTargetingEntitiesGoal(this, LivingEntity.class, false, 128.0F, this::shouldAttack));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   public boolean shouldAttackPlayer(LivingEntity entity) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         if (player.m_7500_()) {
            return false;
         }
      }

      if (!entity.m_5833_()) {
         if (this.m_21826_() != null) {
            if (entity.m_7307_(this.m_21826_())) {
               return false;
            } else {
               return this.m_21826_().m_21214_() == entity || this.m_21826_().m_21188_() == entity;
            }
         } else {
            return true;
         }
      } else {
         return false;
      }
   }

   public boolean shouldAttack(LivingEntity entity) {
      if (entity == this) {
         return false;
      } else if (!entity.m_6084_()) {
         return false;
      } else if (this.m_7307_(entity)) {
         return false;
      } else if (this.m_21826_() == null) {
         return TensuraEPCapability.getEP(entity) > 100.0D;
      } else if (entity.m_7307_(this.m_21826_())) {
         return false;
      } else if (entity instanceof Mob) {
         Mob mob = (Mob)entity;
         return mob.m_5448_() == this.m_21826_();
      } else {
         return this.m_21826_().m_21214_() == entity || this.m_21826_().m_21188_() == entity;
      }
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(MEGALODON_SUMMON, 3);
      this.f_19804_.m_135372_(BEAM_TARGET, BlockPos.f_121853_);
      this.f_19804_.m_135372_(SIZE, 10.0F);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128405_("MegalodonSummon", this.getMegalodonSummon());
      compound.m_128405_("BX", this.getBeamTarget().m_123341_());
      compound.m_128405_("BY", this.getBeamTarget().m_123342_());
      compound.m_128405_("BZ", this.getBeamTarget().m_123343_());
      compound.m_128350_("Size", this.getSize());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.setMegalodonSummon(compound.m_128451_("MegalodonSummon"));
      if (compound.m_128441_("BX")) {
         this.setBeamTarget(new BlockPos(compound.m_128451_("BX"), compound.m_128451_("BY"), compound.m_128451_("BZ")));
      }

      if (this.m_8077_()) {
         this.bossEvent.m_6456_(this.m_5446_());
      }

      this.setSize(compound.m_128457_("Size"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public int getMegalodonSummon() {
      return (Integer)this.f_19804_.m_135370_(MEGALODON_SUMMON);
   }

   public void setMegalodonSummon(int summon) {
      this.f_19804_.m_135381_(MEGALODON_SUMMON, summon);
   }

   public BlockPos getBeamTarget() {
      return (BlockPos)this.f_19804_.m_135370_(BEAM_TARGET);
   }

   public void setBeamTarget(BlockPos pos) {
      this.f_19804_.m_135381_(BEAM_TARGET, pos);
   }

   public float getSize() {
      return (Float)this.f_19804_.m_135370_(SIZE);
   }

   public void setSize(float size) {
      this.f_19804_.m_135381_(SIZE, size);
   }

   public void m_7350_(EntityDataAccessor<?> pKey) {
      if (SIZE.equals(pKey)) {
         this.m_6210_();
      }

      super.m_7350_(pKey);
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions entitydimensions = super.m_6972_(pPose);
      return entitydimensions.m_20388_(0.1F * this.getSize());
   }

   public boolean m_7307_(Entity entity) {
      if (super.m_7307_(entity)) {
         return true;
      } else if (entity instanceof MegalodonEntity) {
         MegalodonEntity megalodon = (MegalodonEntity)entity;
         return megalodon.m_21824_() == this.m_21824_();
      } else if (entity instanceof CharybdisEntity) {
         CharybdisEntity charybdis = (CharybdisEntity)entity;
         return charybdis.m_21824_() == this.m_21824_();
      } else {
         return false;
      }
   }

   public boolean isPushedByFluid(FluidType type) {
      return false;
   }

   public boolean canDrownInFluidType(FluidType type) {
      return false;
   }

   public int m_8132_() {
      return 1;
   }

   public int m_8085_() {
      return 1;
   }

   @Nullable
   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      return null;
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19310_ || source == DamageSource.f_19314_ || source == DamageSource.f_19325_ || source == DamageSource.f_19309_ || super.m_6673_(source);
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      return this.m_6673_(pSource) ? false : super.m_6469_(pSource, pAmount * 0.5F);
   }

   public boolean m_6060_() {
      return TensuraEffectsCapability.hasSyncedEffect(this, (MobEffect)TensuraMobEffects.BLACK_BURN.get());
   }

   public boolean m_6783_(double pDistance) {
      return true;
   }

   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      int y = Math.min(50 + this.f_19796_.m_188503_(6), 250 - (int)this.m_20186_());
      this.setOrbitPos(this.m_20183_().m_6630_(y));
      this.setMegalodonSummon(pLevel.m_213780_().m_188499_() ? 3 : 4);
      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   public void m_6593_(@Nullable Component pName) {
      super.m_6593_(pName);
      this.bossEvent.m_6456_(this.m_5446_());
   }

   public void m_6457_(ServerPlayer pPlayer) {
      super.m_6457_(pPlayer);
      this.bossEvent.m_6543_(pPlayer);
   }

   public void m_6452_(ServerPlayer pPlayer) {
      super.m_6452_(pPlayer);
      this.bossEvent.m_6539_(pPlayer);
   }

   public boolean breakableBlocks(LivingEntity entity, BlockPos pos, BlockState state) {
      return state.m_204336_(TensuraTags.Blocks.BOSS_IMMUNE) ? false : ForgeEventFactory.onEntityDestroyBlock(entity, pos, state);
   }

   public boolean dropBlockLoot(LivingEntity entity, BlockState state) {
      return false;
   }

   public Vec3 getBeamStartOffset() {
      float radius = 14.0F;
      float angle = 0.017453292F * this.f_20883_;
      double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
      double extraZ = (double)(radius * Mth.m_14089_(angle));
      return new Vec3(extraX, (double)this.m_20206_() / 2.0D, extraZ);
   }

   protected void m_8024_() {
      super.m_8024_();
      if (this.f_19797_ % 20 == 0) {
         List<BarrierPart> list = this.f_19853_.m_45976_(BarrierPart.class, this.m_20191_().m_82400_(1.0D));
         if (!list.isEmpty()) {
            Iterator var2 = list.iterator();

            while(var2.hasNext()) {
               BarrierPart barrier = (BarrierPart)var2.next();
               this.m_7327_(barrier);
            }
         }
      }

      this.bossEvent.m_142711_(this.m_21223_() / this.m_21233_());
   }

   public void mountAbility(Player rider) {
      if (this.getMiscAnimation() == 0) {
         this.setMiscAnimation(2);
      }
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         if (!this.m_9236_().m_5776_()) {
            if (this.getMiscAnimation() == 2) {
               if (this.miscAnimationTicks == 5) {
                  if (this.m_5448_() != null) {
                     this.f_21365_.m_148051_(this.m_5448_());
                  }

                  SkillHelper.riptidePush(this, 5.0F);
                  this.m_5834_();
                  TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123766_, 8.0D);
                  TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123766_, 5.0D);
                  this.m_5496_(SoundEvents.f_12519_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
               } else if (this.miscAnimationTicks > 5) {
                  this.f_19812_ = true;
                  AABB aabb = this.m_20191_().m_82400_(16.0D);
                  List<LivingEntity> list = this.f_19853_.m_6443_(LivingEntity.class, aabb, (entity) -> {
                     return !this.m_7307_(entity) && entity != this.m_21826_() && entity != this;
                  });
                  if (!list.isEmpty()) {
                     float damage = (float)(this.m_21133_(Attributes.f_22281_) * 2.0D);
                     DamageSource source = DamageSource.m_19370_(this);

                     LivingEntity target;
                     for(Iterator var5 = list.iterator(); var5.hasNext(); target.f_19864_ = true) {
                        target = (LivingEntity)var5.next();
                        DamageSourceHelper.dealSplitDamage(target, source, 0.5F, source.m_19389_().m_19380_(), damage);
                        target.m_6469_(DamageSource.m_19370_(this), damage);
                        target.m_20256_(target.m_20184_().m_82549_(this.m_20184_()));
                     }
                  }
               }
            } else if (this.getMiscAnimation() == 3) {
               this.m_21573_().m_26573_();
               this.wickedLightRay();
            } else if (this.getMiscAnimation() == 4) {
               this.m_21573_().m_26573_();
               switch(this.miscAnimationTicks) {
               case 10:
                  this.summonMegalodons(10, 15);
                  break;
               case 17:
                  this.summonMegalodons(15, 20);
                  break;
               case 21:
                  this.summonMegalodons(20, 25);
               }

               this.m_5496_(SoundEvents.f_11862_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            } else if (this.getMiscAnimation() == 5) {
               this.m_21573_().m_26573_();
               if (this.miscAnimationTicks >= 10) {
                  this.summonTempestScales(10, 15);
                  this.summonTempestScales(5, 10);
               }

               this.m_5496_(SoundEvents.f_11862_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            }
         }

         if (this.miscAnimationTicks >= this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
            this.setBeamTarget(BlockPos.f_121853_);
         }
      }

      if (!this.f_19853_.f_46443_) {
         if (this.getSize() < 10.0F) {
            if (this.getSpawnType() == MobSpawnType.EVENT) {
               this.setSize(this.getSize() + 0.1F);
               this.m_5496_(SoundEvents.f_215775_, 10.0F, 1.0F);
            } else if (this.getSize() <= 0.0F) {
               this.setSize(10.0F);
            }
         }

         if (this.m_146895_() != null) {
            this.targetingMovementHelper();
            LivingEntity target = this.m_5448_();
            if (target != null && target.m_6084_()) {
               this.m_7618_(Anchor.EYES, target.m_146892_());
            }
         }

      }
   }

   protected void handleFlying() {
      super.handleFlying();
      if (!this.f_19853_.m_5776_()) {
         LivingEntity controller = this.getControllingPassenger();
         if (!this.m_21824_()) {
            this.breakBlocks(this, 1.0F, true, 0, (SimpleContainer)null, true);
         } else if (controller != null && this.m_21830_(controller)) {
            this.breakBlocks(this, 1.0F, false, 0, (SimpleContainer)null);
         }

         if (controller != null) {
            if (controller.m_146909_() <= 20.0F) {
               this.digBlocks(this, 1.0F, 1, 1.0F, false, (SimpleContainer)null);
            } else {
               this.digBlocks(this, 1.0F, 0, 1.0F, controller.m_146909_() >= 40.0F, (SimpleContainer)null);
            }

         }
      }
   }

   private int getAnimationTick(int miscAnimation) {
      byte var10000;
      switch(miscAnimation) {
      case 1:
         var10000 = 11;
         break;
      case 2:
      default:
         var10000 = 20;
         break;
      case 3:
         var10000 = 45;
         break;
      case 4:
         var10000 = 26;
         break;
      case 5:
         var10000 = 30;
         break;
      case 6:
      case 7:
         var10000 = 15;
      }

      return var10000;
   }

   public boolean m_7327_(Entity pEntity) {
      if (super.m_7327_(pEntity) && pEntity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)pEntity;
         if (TensuraEPCapability.getEP(living) > TensuraEPCapability.getEP(this) * 3.5D) {
            return true;
         } else {
            SkillHelper.addEffectWithSource(living, this, (MobEffect)TensuraMobEffects.MAGIC_INTERFERENCE.get(), 6000, 1, true);
            return true;
         }
      } else {
         return false;
      }
   }

   private void wickedLightRay() {
      if (this.miscAnimationTicks < 20) {
         this.m_5496_(SoundEvents.f_11737_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
      } else {
         double d0;
         if (this.miscAnimationTicks == 20) {
            this.beamRandomVec = Vec3.f_82478_;
            if (this.m_5448_() != null) {
               this.setBeamTarget(this.m_5448_().m_20183_().m_7495_());
               this.beamEntity = this.m_5448_();
            } else {
               d0 = this.f_19853_.f_46441_.m_188500_() * 3.141592653589793D * 2.0D;
               BlockPos newTarget = new BlockPos((double)this.m_20183_().m_123341_() - Math.sin(d0) * 25.0D, (double)(this.getBeamTarget().m_123342_() - 50), (double)this.m_20183_().m_123343_() - Math.cos(d0) * 25.0D);
               this.setBeamTarget(newTarget);
            }
         } else if (this.miscAnimationTicks >= 40) {
            this.beamEntity = null;
            this.setBeamTarget(BlockPos.f_121853_);
         } else {
            if (this.beamEntity != null && this.beamEntity.m_6084_()) {
               this.setBeamTarget(this.getBeamTarget().m_7495_());
            } else {
               if (this.m_5448_() != null) {
                  if (this.beamRandomVec == Vec3.f_82478_) {
                     this.beamRandomVec = this.m_5448_().m_20182_().m_82546_(Vec3.m_82512_(this.getBeamTarget())).m_82541_().m_82490_(5.0D);
                  }
               } else if (this.beamRandomVec == Vec3.f_82478_) {
                  d0 = this.f_19853_.f_46441_.m_188500_() * 3.141592653589793D * 2.0D;
                  this.beamRandomVec = new Vec3(-Math.sin(d0) * 5.0D, 0.0D, -Math.cos(d0) * 5.0D);
               }

               BlockPos newTarget = new BlockPos((double)this.getBeamTarget().m_123341_() + this.beamRandomVec.m_7096_(), (double)this.getBeamTarget().m_123342_() + this.beamRandomVec.m_7098_() - 1.0D, (double)this.getBeamTarget().m_123343_() + this.beamRandomVec.m_7094_());
               this.setBeamTarget(newTarget);
            }

            this.m_5496_(SoundEvents.f_11736_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            this.rayDamage();
         }
      }

   }

   private void rayDamage() {
      Vec3 source = this.m_146892_().m_82549_(this.getBeamStartOffset());
      Vec3 targetPos = Vec3.m_82512_(this.getBeamTarget());
      Vec3 offSetToTarget = targetPos.m_82546_(source);
      Vec3 normalizes = offSetToTarget.m_82541_();
      double beamRadius = 4.0D;
      BlockInteraction interaction = this.f_19853_.m_46469_().m_46207_(GameRules.f_46132_) ? BlockInteraction.DESTROY : BlockInteraction.NONE;
      ArrayList<LivingEntity> exploded = new ArrayList();

      for(int i = 0; i < Mth.m_14107_(offSetToTarget.m_82553_()) + 1; i += 2) {
         Vec3 particlePos = source.m_82549_(normalizes.m_82490_((double)i));
         AABB aabb = new AABB(particlePos.f_82479_ + beamRadius, particlePos.f_82480_ + beamRadius, particlePos.f_82481_ + beamRadius, particlePos.f_82479_ - beamRadius, particlePos.f_82480_ - beamRadius, particlePos.f_82481_ - beamRadius);
         boolean explosion = false;
         List<LivingEntity> list = this.f_19853_.m_6443_(LivingEntity.class, aabb, (entity) -> {
            return entity != this.m_21826_() && entity != this && !this.m_7307_(entity) && entity.m_6084_();
         });
         Iterator var14 = list.iterator();

         while(var14.hasNext()) {
            LivingEntity living = (LivingEntity)var14.next();
            float multiplier = RaceHelper.isAffectedByHolyCoat(living) ? 2.0F : 1.5F;
            if (living.m_6469_(TensuraDamageSources.wickedLightRay(this), (float)this.m_21133_(Attributes.f_22281_) * multiplier) && TensuraEPCapability.getEP(living) <= TensuraEPCapability.getEP(this) * 3.5D) {
               SkillHelper.addEffectWithSource(living, this, (MobEffect)TensuraMobEffects.MAGIC_INTERFERENCE.get(), 6000, 1, true);
            }

            if (!exploded.contains(living)) {
               exploded.add(living);
               this.f_19853_.m_46511_(this, living.m_20185_(), living.m_20227_(0.0625D), living.m_20189_(), 5.0F, interaction);
               explosion = true;
            }
         }

         if (!explosion) {
            HitResult hitresult = this.f_19853_.m_45547_(new ClipContext(particlePos, particlePos.m_82549_(normalizes), Block.COLLIDER, Fluid.NONE, (Entity)null));
            if (hitresult.m_6662_().equals(Type.BLOCK)) {
               this.f_19853_.m_46511_(this, hitresult.m_82450_().m_7096_(), hitresult.m_82450_().m_7098_(), hitresult.m_82450_().m_7094_(), 3.0F, interaction);
            }
         }
      }

   }

   private void summonMegalodons(int minRadius, int maxRadius) {
      Level var4 = this.m_9236_();
      if (var4 instanceof ServerLevel) {
         ServerLevel serverLevel = (ServerLevel)var4;
         int i = Mth.m_14107_(this.m_20185_());
         int j = Mth.m_14107_(this.m_20186_() + (double)(this.m_20206_() / 2.0F));
         int k = Mth.m_14107_(this.m_20189_());
         MegalodonEntity megalodon = new MegalodonEntity((EntityType)TensuraEntityTypes.MEGALODON.get(), serverLevel);

         for(int l = 0; l < 50; ++l) {
            int i1 = i + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            int j1 = j + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            int k1 = k + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            BlockPos blockpos = new BlockPos(i1, j1, k1);
            EntityType<?> entitytype = (EntityType)TensuraEntityTypes.MEGALODON.get();
            net.minecraft.world.entity.SpawnPlacements.Type type = SpawnPlacements.m_21752_(entitytype);
            if (NaturalSpawner.m_47051_(type, serverLevel, blockpos, entitytype)) {
               megalodon.m_6034_((double)i1, (double)j1, (double)k1);
               if (serverLevel.m_45784_(megalodon) && serverLevel.m_45786_(megalodon)) {
                  megalodon.m_6518_(serverLevel, serverLevel.m_6436_(megalodon.m_20183_()), MobSpawnType.MOB_SUMMONED, (SpawnGroupData)null, (CompoundTag)null);
                  megalodon.m_6710_(this.m_5448_());
                  megalodon.setCharybdis(this);
                  megalodon.setFlying(true);
                  megalodon.m_20242_(true);
                  megalodon.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.RAMPAGE.get(), 12000, 0, false, false, false));
                  serverLevel.m_47205_(megalodon);
                  TensuraParticleHelper.addServerParticlesAroundSelf(megalodon, (ParticleOptions)TensuraParticles.SOLAR_FLASH.get(), 2.0D);
                  TensuraParticleHelper.addServerParticlesAroundSelf(megalodon, ParticleTypes.f_123812_, 2.0D);
                  break;
               }
            }
         }

      }
   }

   private void summonTempestScales(int minRadius, int maxRadius) {
      Level var4 = this.m_9236_();
      if (var4 instanceof ServerLevel) {
         ServerLevel serverLevel = (ServerLevel)var4;
         int i = Mth.m_14107_(this.m_20185_());
         int j = Mth.m_14107_(this.m_20186_() + (double)(this.m_20206_() / 2.0F));
         int k = Mth.m_14107_(this.m_20189_());
         TempestScaleEntity scale = new TempestScaleEntity(serverLevel, this);

         for(int l = 0; l < 50; ++l) {
            int i1 = i + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            int j1 = j + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            int k1 = k + Mth.m_216271_(this.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.f_19796_, -1, 1);
            scale.m_6034_((double)i1, (double)j1, (double)k1);
            if (serverLevel.m_45784_(scale) && serverLevel.m_45786_(scale)) {
               double d0 = this.m_20185_() - scale.m_20185_();
               double d1 = this.m_20186_() + (double)(this.m_20206_() / 2.0F) - scale.m_20186_();
               double d2 = this.m_20189_() - scale.m_20189_();
               scale.m_20256_((new Vec3(d0, d1, d2)).m_82541_().m_82490_(2.0D));
               scale.setDamage((float)this.m_21133_(Attributes.f_22281_) / 2.0F);
               if (this.m_217043_().m_188503_(5) == 1) {
                  scale.setKnockForce(1.0F);
               }

               if (this.m_217043_().m_188503_(10) == 1) {
                  scale.setTarget(this.m_5448_());
               }

               serverLevel.m_47205_(scale);
               break;
            }
         }

      }
   }

   protected boolean m_8028_() {
      return true;
   }

   public boolean m_6785_(double pDistanceToClosestPlayer) {
      return false;
   }

   protected boolean removeWhenNoAction() {
      return false;
   }

   protected void m_6153_() {
      if (++this.f_20919_ >= 40) {
         this.m_142687_(RemovalReason.KILLED);
         TensuraFallingBlock block = TensuraFallingBlock.fall(this.f_19853_, this.m_20183_(), (BlockState)((net.minecraft.world.level.block.Block)TensuraBlocks.CHARYBDIS_CORE.get()).m_49966_().m_61124_(CharybdisCoreBlock.MODE, SculkSensorPhase.COOLDOWN));
         block.setIndestructible(true);
         this.m_5496_(SoundEvents.f_11913_, 10.0F, 1.0F);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123796_);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123765_);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123796_, 2.0D);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123765_, 2.0D);
      }

   }

   public void m_6667_(DamageSource source) {
      super.m_6667_(source);
      if (!this.m_6084_()) {
         if (this.m_20089_() == Pose.DYING) {
            this.setMiscAnimation(!this.m_20096_() && !this.isInFluidType() ? 7 : 6);
         }

      }
   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_41720_() instanceof HealingPotionItem ? false : pStack.m_41614_();
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (!this.f_19853_.f_46443_) {
            if (this.m_21824_() && this.m_21830_(player)) {
               if (player.m_36341_()) {
                  this.commanding(player);
               } else if (player.m_146895_() == null) {
                  this.m_21839_(false);
                  this.setWandering(false);
                  player.m_7998_(this, false);
               }

               return InteractionResult.m_19078_(this.f_19853_.f_46443_);
            } else {
               return super.m_6071_(player, hand);
            }
         } else {
            boolean flag = this.m_21830_(player) || this.m_21824_();
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         }
      }
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack) && this.m_21223_() < this.m_21233_()) {
         if (!player.m_7500_()) {
            itemstack.m_41774_(1);
         }

         this.m_8035_();
         this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
         return InteractionResult.SUCCESS;
      } else {
         return InteractionResult.PASS;
      }
   }

   public void m_8035_() {
      super.m_8035_();
      this.setMiscAnimation(1);
      this.m_5634_(3.0F);
   }

   public boolean m_20068_() {
      if (super.m_20068_()) {
         return true;
      } else {
         return this.getControllingPassenger() != null && !this.m_20096_();
      }
   }

   public boolean m_6146_() {
      return true;
   }

   @Nullable
   public LivingEntity getControllingPassenger() {
      Iterator var1 = this.m_20197_().iterator();

      while(var1.hasNext()) {
         Entity passenger = (Entity)var1.next();
         if (passenger instanceof Player) {
            Player player = (Player)passenger;
            if (player.equals(this.m_21826_())) {
               return player;
            }
         }
      }

      return null;
   }

   public void m_7332_(Entity passenger) {
      if (this.m_20363_(passenger)) {
         passenger.m_183634_();
         float radius = 2.0F;
         float angle = 0.017453292F * this.f_20883_;
         double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
         double extraZ = (double)(radius * Mth.m_14089_(angle));
         double yOffset = this.m_20186_() + this.m_6048_() + passenger.m_6049_();
         passenger.m_6034_(this.m_20185_() + extraX, yOffset, this.m_20189_() + extraZ);
      }
   }

   public double m_6048_() {
      return super.m_6048_() + 3.25D;
   }

   public void m_7023_(Vec3 pTravelVector) {
      if (this.m_6084_()) {
         LivingEntity controller = this.getControllingPassenger();
         if (this.m_20160_() && controller != null) {
            this.m_146922_(controller.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(controller.m_146909_() * 0.5F);
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.f_20883_;
            float f = controller.f_20900_ * 0.5F;
            float f1 = controller.f_20902_;
            if (f1 <= 0.0F) {
               f1 *= 0.25F;
            }

            this.f_20887_ = this.m_6113_() * 0.5F;
            if (this.m_6109_()) {
               float speed = (float)this.m_21133_(Attributes.f_22279_) / 2.0F;
               if (controller.m_20142_()) {
                  speed = (float)((double)speed * 1.5D);
               }

               this.m_7910_(speed);
               if (controller.f_20899_) {
                  this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.1D, 0.0D));
               } else if (TensuraKeybinds.MOUNT_DESCENDING.m_90857_()) {
                  this.descending(this, controller);
               }

               super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
            } else if (controller instanceof Player && this.getMiscAnimation() != 3) {
               this.m_20256_(Vec3.f_82478_);
            }

            this.m_146872_();
         } else {
            this.f_20887_ = 0.02F;
            if (this.m_6142_() && this.m_20069_()) {
               this.m_19920_(this.m_6113_(), pTravelVector);
               this.m_6478_(MoverType.SELF, this.m_20184_());
               this.m_20256_(this.m_20184_().m_82490_(0.9D));
               if (this.m_5448_() == null) {
                  this.m_20256_(this.m_20184_().m_82520_(0.0D, -0.005D, 0.0D));
               }
            } else {
               super.m_7023_(pTravelVector);
            }
         }
      }

   }

   public SoundSource m_5720_() {
      return SoundSource.HOSTILE;
   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_11890_;
   }

   protected SoundEvent m_7975_(DamageSource source) {
      return SoundEvents.f_11895_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_11891_;
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_20072_()) {
         if (event.isMoving()) {
            if (this.m_21660_()) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.charybdis.swim_fast", EDefaultLoopTypes.LOOP));
            } else {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.charybdis.swim_slow", EDefaultLoopTypes.LOOP));
            }
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.charybdis.swim_idle", EDefaultLoopTypes.LOOP));
         }
      } else if (event.isMoving() && !this.m_21825_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.charybdis.fly", EDefaultLoopTypes.LOOP));
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.charybdis.fly_idle", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState playOncePredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.charybdis.bite", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.charybdis.roll", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.charybdis.beam", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 4) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.charybdis.roar", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 5) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.charybdis.tempest_scale", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 6) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.charybdis.swim_death", EDefaultLoopTypes.HOLD_ON_LAST_FRAME));
         } else if (this.getMiscAnimation() == 7) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.charybdis.fly_death", EDefaultLoopTypes.HOLD_ON_LAST_FRAME));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "playOnceController", 0.0F, this::playOncePredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   @Nullable
   public LivingEntity getBeamEntity() {
      return this.beamEntity;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(CharybdisEntity.class, EntityDataSerializers.f_135028_);
      MEGALODON_SUMMON = SynchedEntityData.m_135353_(CharybdisEntity.class, EntityDataSerializers.f_135028_);
      BEAM_TARGET = SynchedEntityData.m_135353_(CharybdisEntity.class, EntityDataSerializers.f_135038_);
      SIZE = SynchedEntityData.m_135353_(CharybdisEntity.class, EntityDataSerializers.f_135029_);
   }

   private static class CharybdisAttackGoal extends Goal {
      private int tickDelay;
      private final CharybdisEntity charybdis;
      private LivingEntity target;

      public CharybdisAttackGoal(CharybdisEntity entity) {
         this.charybdis = entity;
      }

      public boolean m_183429_() {
         return true;
      }

      public boolean m_8036_() {
         if (this.charybdis.m_21827_()) {
            return false;
         } else {
            LivingEntity entity = this.charybdis.m_5448_();
            if (entity != null) {
               this.target = entity;
               return this.charybdis.m_21040_(entity, TargetingConditions.m_148352_().m_148355_().m_26893_());
            } else {
               return false;
            }
         }
      }

      public void m_8056_() {
         this.tickDelay = 80;
         this.updateOrbit();
      }

      public void m_8037_() {
         --this.tickDelay;
         if (this.charybdis.getMiscAnimation() == 0 || this.charybdis.getMiscAnimation() == 1) {
            if (this.tickDelay <= 0 && this.target != null) {
               double distance = (double)this.target.m_20270_(this.charybdis);
               double attackRange = this.getAttackReach(this.target);
               if (distance <= attackRange + 12.0D && this.charybdis.m_217043_().m_188503_(10) != 1) {
                  if (distance < attackRange) {
                     this.tickDelay = 20;
                     this.charybdis.setMiscAnimation(1);
                     this.charybdis.m_7327_(this.target);
                     this.charybdis.m_5496_(SoundEvents.f_11865_, 10.0F, 0.95F + this.charybdis.f_19796_.m_188501_() * 0.1F);
                  } else {
                     this.tickDelay = 40;
                     this.charybdis.setMiscAnimation(2);
                     this.charybdis.f_21365_.m_148051_(this.target);
                  }
               } else {
                  int randomAttack = this.randomAttack(distance);
                  this.charybdis.setMiscAnimation(randomAttack);
                  this.updateOrbit();
                  this.tickDelay = (1 + this.charybdis.f_19796_.m_188503_(4)) * 10;
                  this.charybdis.m_5496_(SoundEvents.f_11893_, 10.0F, 0.95F + this.charybdis.f_19796_.m_188501_() * 0.1F);
               }
            }

         }
      }

      private void updateOrbit() {
         if (this.target.m_20096_() || this.target.m_20072_()) {
            int y = Math.min(50 + this.charybdis.f_19796_.m_188503_(6), 250 - (int)this.charybdis.m_20186_());
            this.charybdis.setOrbitPos(this.target.m_20183_().m_6630_(y));
         }
      }

      protected int randomAttack(double distance) {
         if (distance >= 20.0D && this.canSummonMegalodons()) {
            return 4;
         } else if (this.charybdis.f_19796_.m_188503_(5) == 1) {
            return 5;
         } else {
            return this.charybdis.f_19796_.m_188503_(8) == 1 ? 3 : 0;
         }
      }

      private double getAttackReach(LivingEntity pAttackTarget) {
         return Math.sqrt((double)(this.charybdis.m_20205_() * 2.0F * this.charybdis.m_20205_() * 2.0F + pAttackTarget.m_20205_()));
      }

      private boolean canSummonMegalodons() {
         if (this.charybdis.m_21824_()) {
            return false;
         } else if (this.charybdis.m_217043_().m_188503_(10) != 1) {
            return false;
         } else {
            int summon = this.charybdis.getMegalodonSummon();
            if (summon <= 0) {
               return false;
            } else {
               this.charybdis.setMegalodonSummon(summon - 1);
               return true;
            }
         }
      }
   }
}
