package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.api.entity.ai.LeapWithStrengthGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.IGiantMob;
import com.github.manasmods.tensura.api.entity.subclass.ITensuraMount;
import com.github.manasmods.tensura.api.entity.subclass.SpittingRangedMonster;
import com.github.manasmods.tensura.block.SpiderEggBlock;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.projectile.MonsterSpitProjectile;
import com.github.manasmods.tensura.entity.template.ClimbingEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.advancements.CriteriaTriggers;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.stats.Stats;
import net.minecraft.util.Mth;
import net.minecraft.util.RandomSource;
import net.minecraft.world.Difficulty;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.ExperienceOrb;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.PlayerRideableJumping;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.control.LookControl;
import net.minecraft.world.entity.ai.control.MoveControl;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.MoveToBlockGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.animal.IronGolem;
import net.minecraft.world.entity.npc.Villager;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.LevelReader;
import net.minecraft.world.level.LightLayer;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeHooks;
import net.minecraftforge.common.ForgeMod;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class BlackSpiderEntity extends ClimbingEntity implements IAnimatable, IGiantMob, ITensuraMount, PlayerRideableJumping, SpittingRangedMonster {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Integer> EGGS;
   private static final EntityDataAccessor<Boolean> SADDLED;
   private static final EntityDataAccessor<Boolean> STRIPED;
   public int miscAnimationTicks = 0;
   protected float playerJumpPendingScale;
   protected boolean playerJumping;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public BlackSpiderEntity(EntityType<? extends BlackSpiderEntity> type, Level level) {
      super(type, level);
      this.f_19793_ = 3.0F;
      this.f_21364_ = 30;
      this.f_21342_ = new BlackSpiderEntity.BlackSpiderMoveControl();
      this.f_21365_ = new BlackSpiderEntity.BlackSpiderLookControl();
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 6.0D).m_22268_(Attributes.f_22276_, 65.0D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22281_, 10.0D).m_22268_(Attributes.f_22279_, 0.25D).m_22268_(Attributes.f_22278_, 0.6000000238418579D).m_22268_(Attributes.f_22288_, 1.5D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 2.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(0, new BlackSpiderEntity.SpiderLayEggsGoal(this, 1.0D));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new WanderingFollowOwnerGoal(this, 1.2D, 20.0F, 5.0F, false));
      this.f_21345_.m_25352_(4, new BlackSpiderEntity.BlackSpiderLeapGoal(this));
      this.f_21345_.m_25352_(4, new BlackSpiderEntity.SpiderBreedGoal(this, 1.0D));
      this.f_21345_.m_25352_(5, new TensuraTamableEntity.WanderAroundPosGoal(this));
      this.f_21345_.m_25352_(6, new LookAtPlayerGoal(this, Player.class, 10.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(2, new BlackSpiderEntity.BlackSpiderAttackGoal(this, 1.2D, true));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, Player.class, false, (Predicate)null));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, Villager.class, false, (Predicate)null));
      this.f_21346_.m_25352_(5, new NonTameRandomTargetGoal(this, Animal.class, false, (entity) -> {
         return !(entity instanceof BlackSpiderEntity);
      }));
      this.f_21346_.m_25352_(5, new NonTameRandomTargetGoal(this, IronGolem.class, false, (Predicate)null));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(EGGS, 0);
      this.f_19804_.m_135372_(SADDLED, Boolean.FALSE);
      this.f_19804_.m_135372_(STRIPED, Boolean.FALSE);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128405_("Eggs", this.getEggs());
      compound.m_128379_("Saddled", this.isSaddled());
      compound.m_128379_("Striped", this.isStriped());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.setEggs(compound.m_128451_("Eggs"));
      this.setSaddled(compound.m_128471_("Saddled"));
      this.setStriped(compound.m_128471_("Striped"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      this.f_19804_.m_135381_(MISC_ANIMATION, animation);
   }

   public int getEggs() {
      return (Integer)this.f_19804_.m_135370_(EGGS);
   }

   public void setEggs(int egg) {
      this.f_19804_.m_135381_(EGGS, egg);
   }

   public boolean isSaddled() {
      return (Boolean)this.f_19804_.m_135370_(SADDLED);
   }

   public void setSaddled(boolean saddled) {
      this.f_19804_.m_135381_(SADDLED, saddled);
   }

   public boolean isStriped() {
      return (Boolean)this.f_19804_.m_135370_(STRIPED);
   }

   public void setStriped(boolean striped) {
      this.f_19804_.m_135381_(STRIPED, striped);
   }

   public boolean m_5957_() {
      return super.m_5957_() && this.getEggs() <= 0;
   }

   public boolean m_7848_(Animal pOtherAnimal) {
      return !this.m_21824_() ? false : super.m_7848_(pOtherAnimal);
   }

   public BlackSpiderEntity getBreedOffspring(ServerLevel pLevel, AgeableMob pOtherParent) {
      BlackSpiderEntity spider = (BlackSpiderEntity)((EntityType)TensuraEntityTypes.BLACK_SPIDER.get()).m_20615_(pLevel);
      if (spider == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            spider.m_21816_(uuid);
            spider.m_7105_(true);
         }

         return spider;
      }
   }

   public boolean m_7327_(Entity pEntity) {
      boolean flag = super.m_7327_(pEntity);
      if (flag && this.getMiscAnimation() == 0) {
         this.setMiscAnimation(1);
      }

      return flag;
   }

   public int m_5792_() {
      return 1;
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions entitydimensions = super.m_6972_(pPose);
      return this.m_6162_() ? entitydimensions.m_20390_(0.4F, 0.4F) : entitydimensions;
   }

   public void m_7334_(Entity pEntity) {
      if (!(pEntity instanceof BlackSpiderEntity)) {
         super.m_7334_(pEntity);
      }
   }

   public void m_8119_() {
      super.m_8119_();
      this.targetingMovementHelper();
      LivingEntity owner;
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         if (this.getMiscAnimation() == 3 && this.miscAnimationTicks == 25) {
            this.areaAttack();
            TensuraParticleHelper.spawnGroundSlamParticle(this, 5, 5.0F);
            TensuraParticleHelper.spawnGroundSlamParticle(this, 5, 4.0F);
            TensuraParticleHelper.spawnGroundSlamParticle(this, 5, 3.0F);
            TensuraParticleHelper.spawnGroundSlamParticle(this, 5, 2.0F);
            this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11913_, SoundSource.NEUTRAL, 1.0F, 1.0F);
         } else if (this.getMiscAnimation() == 4 && this.miscAnimationTicks == 15) {
            owner = this.m_5448_();
            if (owner != null) {
               this.performRangedAttack(owner, this.m_6162_() ? -0.3D : -1.5D, this.m_6162_() ? 1.0D : 5.0D);
               this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12098_, SoundSource.NEUTRAL, 1.0F, 1.0F);
            }
         } else if (this.getMiscAnimation() == -1 && this.miscAnimationTicks == 15) {
            owner = this.getControllingPassenger();
            if (owner != null) {
               BlockHitResult hitResult = SkillHelper.getPlayerPOVHitResult(this.f_19853_, owner, Fluid.NONE, 30.0D);
               this.performRangedAttack(hitResult.m_82425_(), -1.5D, 5.0D);
               this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12098_, SoundSource.NEUTRAL, 1.0F, 1.0F);
            }
         }

         if (this.miscAnimationTicks >= this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

      if (!this.f_19853_.m_5776_()) {
         owner = this.getControllingPassenger();
         if (!this.m_21824_() || owner != null && this.m_21830_(owner)) {
            this.breakBlocks(this, 1.0F, false);
         }

      }
   }

   public void mountAbility(Player rider) {
      if (this.getMiscAnimation() == 1 || this.getMiscAnimation() == 0) {
         if (!this.m_6162_()) {
            this.setMiscAnimation(-1);
         }
      }
   }

   private int getAnimationTick(int miscAnimation) {
      byte var10000;
      switch(miscAnimation) {
      case -1:
      case 4:
         var10000 = 25;
         break;
      case 0:
      case 1:
      default:
         var10000 = 5;
         break;
      case 2:
         var10000 = 27;
         break;
      case 3:
      case 5:
         var10000 = 30;
      }

      return var10000;
   }

   public void areaAttack() {
      AABB aabb = this.m_20191_().m_82400_(5.0D);
      List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, aabb, (entity) -> {
         return !entity.m_7307_(this) && entity != this.m_21826_() && !entity.equals(this) && !(entity instanceof BlackSpiderEntity);
      });
      if (!livingEntityList.isEmpty()) {
         Iterator var3 = livingEntityList.iterator();

         while(var3.hasNext()) {
            LivingEntity target = (LivingEntity)var3.next();
            double damageMultiplier = 2.0D;
            target.m_6469_(DamageSourceHelper.addSkillAndCost(DamageSource.m_19370_(this).m_19389_(), 20.0D), (float)(this.m_21133_(Attributes.f_22281_) * damageMultiplier));
            target.m_20184_().m_82520_(0.0D, 0.5D * damageMultiplier, 0.0D);
         }

      }
   }

   public void spitHit(LivingEntity pTarget) {
      if (!(pTarget instanceof BlackSpiderEntity)) {
         if (pTarget.m_6469_(DamageSource.m_19370_(this), (float)this.m_21133_(Attributes.f_22281_))) {
            int duration = this.m_6162_() ? 50 : 200;
            if (!(pTarget.m_20206_() <= 3.0F) && !(pTarget.m_20205_() <= 3.0F)) {
               pTarget.m_147207_(new MobEffectInstance(MobEffects.f_19597_, duration, 0, false, false, true), this);
            } else {
               pTarget.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.WEBBED.get(), duration, 0, false, false, true), this);
               pTarget.m_147207_(new MobEffectInstance((MobEffect)TensuraMobEffects.SILENCE.get(), duration, 0, false, false, true), this);
            }
         }

      }
   }

   public void spitParticle(MonsterSpitProjectile projectile) {
      if (this.m_6162_()) {
         this.particleSpawning(projectile, ParticleTypes.f_123764_, 2);
      } else {
         this.particleSpawning(projectile, ParticleTypes.f_123764_, 5);
      }

   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (this.f_19853_.f_46443_) {
            boolean flag = this.m_21830_(player) || this.m_21824_();
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         } else if (this.m_21824_() && this.m_21830_(player)) {
            if (!this.m_6162_()) {
               Item item = itemstack.m_41720_();
               if (item.equals(TensuraMaterialItems.MONSTER_SADDLE.get()) && !this.isSaddled()) {
                  if (!player.m_150110_().f_35937_) {
                     itemstack.m_41774_(1);
                  }

                  this.setSaddled(true);
                  this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  return InteractionResult.SUCCESS;
               }

               if (this.isSaddled() && item.equals(Items.f_42574_)) {
                  this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  this.m_19998_((ItemLike)TensuraMaterialItems.MONSTER_SADDLE.get());
                  this.setSaddled(false);
                  return InteractionResult.SUCCESS;
               }
            }

            if (!player.m_36341_() && this.isSaddled()) {
               if (player.m_146895_() == null) {
                  this.m_21839_(false);
                  this.setWandering(false);
                  player.m_7998_(this, true);
               }
            } else {
               this.commanding(player);
            }

            return InteractionResult.SUCCESS;
         } else {
            return super.m_6071_(player, hand);
         }
      }
   }

   public InteractionResult handleEating(Player pPlayer, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack)) {
         if (this.m_21223_() < this.m_21233_()) {
            if (!pPlayer.m_7500_()) {
               itemstack.m_41774_(1);
            }

            this.m_8035_();
            this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            return InteractionResult.SUCCESS;
         }

         int i = this.m_146764_();
         if (!this.f_19853_.m_5776_() && i == 0 && this.m_5957_()) {
            this.m_142075_(pPlayer, hand, itemstack);
            this.m_27595_(pPlayer);
            return InteractionResult.SUCCESS;
         }

         if (this.m_6162_()) {
            this.m_142075_(pPlayer, hand, itemstack);
            this.m_8035_();
            this.m_146740_(m_216967_(-i), true);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }
      }

      return InteractionResult.PASS;
   }

   public void m_8035_() {
      super.m_8035_();
      this.setMiscAnimation(1);
      this.m_5634_(5.0F);
   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_41720_() instanceof HealingPotionItem ? false : pStack.m_41614_();
   }

   public boolean m_7132_() {
      return this.m_20160_();
   }

   public double getCustomJump() {
      return this.m_21133_(Attributes.f_22288_);
   }

   public boolean m_6146_() {
      return true;
   }

   @Nullable
   public LivingEntity getControllingPassenger() {
      Iterator var1 = this.m_20197_().iterator();

      while(var1.hasNext()) {
         Entity passenger = (Entity)var1.next();
         if (passenger instanceof Player) {
            Player player = (Player)passenger;
            if (this.m_21830_(player)) {
               return player;
            }
         }
      }

      return null;
   }

   public void m_7332_(Entity passenger) {
      if (this.m_20363_(passenger)) {
         passenger.m_183634_();
         float radius = -0.25F;
         float angle = 0.017453292F * this.f_20883_;
         double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
         double extraZ = (double)(radius * Mth.m_14089_(angle));
         double yOffset = this.m_20186_() + this.m_6048_() + passenger.m_6049_();
         passenger.m_6034_(this.m_20185_() + extraX, yOffset, this.m_20189_() + extraZ);
      }
   }

   public void m_7888_(int pJumpPower) {
      if (pJumpPower >= 90) {
         this.playerJumpPendingScale = 1.0F;
      } else {
         if (pJumpPower < 0) {
            pJumpPower = 0;
         }

         this.playerJumpPendingScale = 0.4F + 0.4F * (float)pJumpPower / 90.0F;
      }

   }

   public void m_7199_(int pJumpPower) {
      if (this.m_20096_()) {
         this.playJumpSound();
         this.setMiscAnimation(2);
      }
   }

   public void m_8012_() {
   }

   public void m_7023_(Vec3 pTravelVector) {
      if (this.m_6084_()) {
         LivingEntity livingentity = this.getControllingPassenger();
         if (this.m_20160_() && livingentity != null) {
            this.m_146922_(livingentity.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(livingentity.m_146909_() * 0.5F);
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.f_20883_;
            float f = livingentity.f_20900_ * 0.5F;
            float f1 = livingentity.f_20902_;
            if (f1 <= 0.0F) {
               f1 *= 0.25F;
            }

            if (this.playerJumpPendingScale > 0.0F && !this.isPlayerJumping() && this.f_19861_) {
               double d0 = this.getCustomJump() * (double)this.playerJumpPendingScale * (double)this.m_20098_();
               double d1 = d0 + this.m_182332_();
               Vec3 vec3 = this.m_20184_();
               this.m_20334_(vec3.f_82479_, d1, vec3.f_82481_);
               this.setPlayerJumping(true);
               this.f_20899_ = true;
               this.f_19812_ = true;
               ForgeHooks.onLivingJump(this);
               if (f1 > 0.0F) {
                  float f2 = Mth.m_14031_(this.m_146908_() * 0.017453292F);
                  float f3 = Mth.m_14089_(this.m_146908_() * 0.017453292F);
                  this.m_20256_(this.m_20184_().m_82520_((double)(-0.4F * f2 * this.playerJumpPendingScale), 0.0D, (double)(0.4F * f3 * this.playerJumpPendingScale)));
               }

               this.playerJumpPendingScale = 0.0F;
            }

            this.f_20887_ = this.m_6113_() * 0.1F;
            if (this.m_6109_()) {
               float speed = (float)this.m_21133_(Attributes.f_22279_);
               if (livingentity.m_20142_()) {
                  speed = (float)((double)speed * 1.25D);
               }

               this.m_7910_(speed);
               if (this.isInFluidType((fluidType, height) -> {
                  return height > this.m_20204_();
               }) && f1 > 0.0F) {
                  this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.03D, 0.0D));
                  super.m_7023_(new Vec3((double)f, (double)livingentity.f_20901_, (double)f1));
               } else {
                  super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
               }
            } else if (livingentity instanceof Player) {
               this.m_20256_(Vec3.f_82478_);
            }

            if (this.f_19861_) {
               this.playerJumpPendingScale = 0.0F;
               this.setPlayerJumping(false);
               this.f_20899_ = false;
            }

            this.m_146872_();
         } else {
            this.f_20887_ = 0.02F;
            super.m_7023_(pTravelVector);
         }
      }

   }

   protected void m_5907_() {
      super.m_5907_();
      if (this.isSaddled() && !this.m_9236_().m_5776_()) {
         this.m_19998_((ItemLike)TensuraMaterialItems.MONSTER_SADDLE.get());
      }

   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19310_ || source == DamageSource.f_19314_ || source == DamageSource.f_19325_ || source == DamageSource.f_19309_ || super.m_6673_(source);
   }

   public void m_7601_(BlockState pState, Vec3 pMotionMultiplier) {
      if (!pState.m_204336_(TensuraTags.Blocks.WEB_BLOCKS)) {
         super.m_7601_(pState, pMotionMultiplier);
      }
   }

   protected float m_6041_() {
      BlockState blockstate = this.f_19853_.m_8055_(this.m_20183_());
      return blockstate.m_204336_(TensuraTags.Blocks.WEB_BLOCKS) ? 1.0F : super.m_6041_();
   }

   protected float m_20098_() {
      BlockState blockstate = this.f_19853_.m_8055_(this.m_20183_());
      return blockstate.m_204336_(TensuraTags.Blocks.WEB_BLOCKS) ? 1.0F : super.m_20098_();
   }

   protected float getClimbSpeedMultiplier() {
      return this.m_6162_() ? 1.5F : 3.0F;
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      if (!(pFallDistance < 10.0F) && !this.m_9236_().m_8055_(this.m_20097_()).m_204336_(TensuraTags.Blocks.WEB_BLOCKS)) {
         if (pFallDistance > 10.0F) {
            this.m_5496_(SoundEvents.f_12319_, 0.4F, 1.0F);
         }

         int i = this.m_5639_(pFallDistance - 10.0F, pMultiplier);
         if (i <= 0) {
            return false;
         } else {
            this.m_6469_(pSource, (float)i);
            if (this.m_20160_()) {
               Iterator var5 = this.m_146897_().iterator();

               while(var5.hasNext()) {
                  Entity entity = (Entity)var5.next();
                  entity.m_6469_(pSource, (float)i);
               }
            }

            this.m_21229_();
            return true;
         }
      } else {
         return false;
      }
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if (this.f_19796_.m_188499_()) {
         this.setStriped(true);
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.blackSpiderSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   public static boolean checkSpiderSpawnRules(EntityType<BlackSpiderEntity> spider, ServerLevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      if (!pLevel.m_8055_(pPos.m_7495_()).m_204336_(TensuraTags.Blocks.MOBS_SPAWNABLE_ON)) {
         return false;
      } else {
         return pLevel.m_46791_() != Difficulty.PEACEFUL && pLevel.m_45517_(LightLayer.BLOCK, pPos) <= 5 && m_217057_(spider, pLevel, pSpawnType, pPos, pRandom);
      }
   }

   public void m_6667_(DamageSource pDamageSource) {
      super.m_6667_(pDamageSource);
      if (!this.m_9236_().m_5776_()) {
         if (!this.m_6162_()) {
            int additionalAmount = this.m_217043_().m_188503_(10) == 1 ? this.m_217043_().m_216339_(4, 8) : 0;
            int eggAmount = this.getEggs() + additionalAmount;
            if (eggAmount > 0) {
               for(int i = 0; i < eggAmount; ++i) {
                  BlackSpiderEntity spider = (BlackSpiderEntity)((EntityType)TensuraEntityTypes.BLACK_SPIDER.get()).m_20615_(this.m_9236_());
                  if (spider != null) {
                     spider.m_146762_(-24000);
                     spider.m_6027_(this.m_20185_() + 0.3D, this.m_20186_(), this.m_20189_() + 0.3D);
                     spider.m_20334_((double)(this.f_19796_.m_188501_() - 0.5F), (double)this.f_19796_.m_188501_(), (double)(this.f_19796_.m_188501_() - 0.5F));
                     spider.m_5834_();
                     this.m_9236_().m_7967_(spider);
                  }
               }

            }
         }
      }
   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_12432_;
   }

   protected SoundEvent m_7975_(DamageSource source) {
      return SoundEvents.f_12434_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_12433_;
   }

   public SoundSource m_5720_() {
      return SoundSource.HOSTILE;
   }

   protected void playJumpSound() {
      this.m_5496_((SoundEvent)TensuraSoundEvents.SMALL_JUMP_IMPACT.get(), 0.4F, 1.0F);
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.getMiscAnimation() != 2 && this.getMiscAnimation() != 3 && this.getMiscAnimation() != 4) {
         if (this.m_21825_()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.black_spider.stay", EDefaultLoopTypes.LOOP));
         } else if (event.isMoving()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.black_spider.walk", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.black_spider.idle", EDefaultLoopTypes.LOOP));
         }

         return PlayState.CONTINUE;
      } else {
         return PlayState.CONTINUE;
      }
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.black_spider.bite", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.black_spider.leap", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.black_spider.slam", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() != 4 && this.getMiscAnimation() != -1) {
            if (this.getMiscAnimation() == 5) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.black_spider.lay_eggs", EDefaultLoopTypes.PLAY_ONCE));
            }
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.black_spider.silk", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   public void setPlayerJumping(boolean playerJumping) {
      this.playerJumping = playerJumping;
   }

   public boolean isPlayerJumping() {
      return this.playerJumping;
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(BlackSpiderEntity.class, EntityDataSerializers.f_135028_);
      EGGS = SynchedEntityData.m_135353_(BlackSpiderEntity.class, EntityDataSerializers.f_135028_);
      SADDLED = SynchedEntityData.m_135353_(BlackSpiderEntity.class, EntityDataSerializers.f_135035_);
      STRIPED = SynchedEntityData.m_135353_(BlackSpiderEntity.class, EntityDataSerializers.f_135035_);
   }

   public class BlackSpiderMoveControl extends MoveControl {
      public BlackSpiderMoveControl() {
         super(BlackSpiderEntity.this);
      }

      public void m_8126_() {
         if (!BlackSpiderEntity.this.m_5803_()) {
            if (BlackSpiderEntity.this.getMiscAnimation() != 3) {
               if (BlackSpiderEntity.this.getMiscAnimation() != 5) {
                  super.m_8126_();
               }
            }
         }
      }
   }

   public class BlackSpiderLookControl extends LookControl {
      public BlackSpiderLookControl() {
         super(BlackSpiderEntity.this);
      }

      public void m_8128_() {
         if (!BlackSpiderEntity.this.m_5803_()) {
            if (BlackSpiderEntity.this.getMiscAnimation() != 3) {
               if (BlackSpiderEntity.this.getMiscAnimation() != 5) {
                  super.m_8128_();
               }
            }
         }
      }
   }

   static class SpiderLayEggsGoal extends MoveToBlockGoal {
      private final BlackSpiderEntity spider;
      private int coolDown = 200;

      SpiderLayEggsGoal(BlackSpiderEntity spider, double pSpeedModifier) {
         super(spider, pSpeedModifier, 20);
         this.spider = spider;
      }

      public boolean m_8036_() {
         return this.spider.getEggs() > 0 && super.m_8036_() && !this.spider.m_21827_();
      }

      public void m_8056_() {
         super.m_8056_();
         this.coolDown = 0;
      }

      public void m_8037_() {
         --this.coolDown;
         if (this.coolDown <= 0) {
            super.m_8037_();
            BlockPos blockpos = this.spider.m_20183_();
            Level level = this.spider.f_19853_;
            if (this.m_25625_()) {
               level.m_5594_((Player)null, blockpos, SoundEvents.f_12486_, SoundSource.BLOCKS, 0.3F, 0.9F + level.f_46441_.m_188501_() * 0.2F);
               level.m_46796_(2001, blockpos, Block.m_49956_(level.m_8055_(blockpos.m_7495_())));
               if (this.spider.getMiscAnimation() == 5) {
                  this.spider.f_21344_.m_26573_();
                  if (this.spider.miscAnimationTicks == 15) {
                     level.m_7731_(this.f_25602_.m_7494_(), ((Block)TensuraBlocks.SPIDER_EGG.get()).m_49966_(), 3);
                     this.spider.setEggs(this.spider.getEggs() - 1);
                     this.coolDown = 60;
                     if (this.spider.getEggs() <= 0) {
                        this.spider.m_27601_(600);
                     }

                     this.spider.setMiscAnimation(0);
                  }
               } else {
                  this.spider.setMiscAnimation(5);
               }
            }

         }
      }

      protected BlockPos m_6669_() {
         BlockPos newPos = new BlockPos((double)this.f_25602_.m_123341_() + 0.5D, (double)this.f_25602_.m_123342_(), (double)this.f_25602_.m_123343_() + 0.5D);
         return newPos.m_7494_();
      }

      protected boolean m_6465_(LevelReader pLevel, BlockPos pPos) {
         return pLevel.m_46859_(pPos.m_7494_()) && SpiderEggBlock.canLayEgg(pLevel, pPos);
      }
   }

   static class BlackSpiderLeapGoal extends LeapWithStrengthGoal {
      private final BlackSpiderEntity spider;

      public BlackSpiderLeapGoal(BlackSpiderEntity spider) {
         super(spider, 0.7F, 2.0F, 10.0D, 20.0D, 40);
         this.spider = spider;
      }

      public void m_8056_() {
         super.m_8056_();
         this.spider.setMiscAnimation(2);
      }

      public boolean m_8045_() {
         if (this.spider.getMiscAnimation() != 0) {
            return false;
         } else {
            return !this.spider.m_20096_();
         }
      }
   }

   static class SpiderBreedGoal extends BreedGoal {
      private final BlackSpiderEntity spider;

      SpiderBreedGoal(BlackSpiderEntity moth, double pSpeedModifier) {
         super(moth, pSpeedModifier);
         this.spider = moth;
      }

      public boolean m_8036_() {
         return super.m_8036_() && this.spider.getEggs() <= 0;
      }

      protected void m_8026_() {
         ServerPlayer serverplayer = this.f_25113_.m_27592_();
         if (serverplayer == null && this.f_25115_ != null && this.f_25115_.m_27592_() != null) {
            serverplayer = this.f_25115_.m_27592_();
         }

         if (serverplayer != null && this.f_25115_ != null) {
            serverplayer.m_36220_(Stats.f_12937_);
            CriteriaTriggers.f_10581_.m_147278_(serverplayer, this.f_25113_, this.f_25115_, (AgeableMob)null);
         }

         this.spider.setEggs(this.spider.f_19796_.m_188503_(5));
         this.f_25113_.m_27594_();
         this.f_25115_.m_27594_();
         RandomSource randomsource = this.f_25113_.m_217043_();
         if (this.f_25114_.m_46469_().m_46207_(GameRules.f_46135_)) {
            this.f_25114_.m_7967_(new ExperienceOrb(this.f_25114_, this.f_25113_.m_20185_(), this.f_25113_.m_20186_(), this.f_25113_.m_20189_(), randomsource.m_188503_(7) + 1));
         }

      }
   }

   static class BlackSpiderAttackGoal extends MeleeAttackGoal {
      private final BlackSpiderEntity spider;

      public BlackSpiderAttackGoal(BlackSpiderEntity spider, double pSpeedModifier, boolean pFollowingTargetEvenIfNotSeen) {
         super(spider, pSpeedModifier, pFollowingTargetEvenIfNotSeen);
         this.spider = spider;
      }

      public boolean m_8036_() {
         return this.spider.m_21827_() ? false : super.m_8036_();
      }

      public boolean m_8045_() {
         return this.spider.m_21827_() ? false : super.m_8045_();
      }

      public void m_8037_() {
         if (this.spider.getMiscAnimation() == 0) {
            super.m_8037_();
         }

      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double distance = this.m_6639_(pEnemy);
         if (this.spider.getMiscAnimation() == 0) {
            int randomAttack = this.randomAttack(distance);
            double var10000;
            switch(randomAttack) {
            case 3:
               this.spider.m_21573_().m_26573_();
               var10000 = 25.0D;
               break;
            case 4:
               var10000 = 225.0D;
               break;
            default:
               var10000 = distance;
            }

            double attackRange = var10000;
            if (pDistToEnemySqr <= attackRange && this.m_25564_()) {
               this.m_25563_();
               this.spider.setMiscAnimation(randomAttack);
               if (randomAttack == 1) {
                  this.spider.m_7327_(pEnemy);
               }
            }
         }

      }

      protected int randomAttack(double distance) {
         if (this.spider.f_19796_.m_188503_(5) == 2 && this.spider.getControllingPassenger() == null && !this.spider.m_6162_()) {
            return 3;
         } else {
            return (double)this.spider.f_19796_.m_188501_() <= 0.4D && distance > (this.spider.m_6162_() ? 1.6D : 8.0D) ? 4 : 1;
         }
      }

      protected double m_6639_(LivingEntity pAttackTarget) {
         return (double)(this.f_25540_.m_20205_() * this.f_25540_.m_20205_() * 3.0F + pAttackTarget.m_20205_());
      }
   }
}
