package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.api.entity.ai.LeapWithStrengthGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.IGiantMob;
import com.github.manasmods.tensura.api.entity.subclass.ITensuraMount;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.ClimbingEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.PlayerRideableJumping;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.animal.IronGolem;
import net.minecraft.world.entity.npc.Villager;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.ExplosionDamageCalculator;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.Explosion.BlockInteraction;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeHooks;
import net.minecraftforge.common.ForgeMod;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class KnightSpiderEntity extends ClimbingEntity implements IAnimatable, IGiantMob, ITensuraMount, PlayerRideableJumping {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Boolean> SLAMMING_ATTACK;
   private static final EntityDataAccessor<Boolean> SLAMMING_FALL;
   private static final EntityDataAccessor<Boolean> SADDLED;
   public int miscAnimationTicks = 0;
   protected int slammingCoolDown = 0;
   protected int ticksJump = 0;
   protected float playerJumpPendingScale;
   protected boolean playerJumping;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public KnightSpiderEntity(EntityType<? extends KnightSpiderEntity> type, Level level) {
      super(type, level);
      this.f_19793_ = 4.0F;
      this.f_21364_ = 40;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 8.0D).m_22268_(Attributes.f_22276_, 90.0D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22281_, 22.0D).m_22268_(Attributes.f_22279_, 0.3499999940395355D).m_22268_(Attributes.f_22278_, 0.8999999761581421D).m_22268_(Attributes.f_22288_, 2.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 2.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new WanderingFollowOwnerGoal(this, 1.2D, 20.0F, 5.0F, false));
      this.f_21345_.m_25352_(4, new KnightSpiderEntity.KnightSpiderLeapGoal(this));
      this.f_21345_.m_25352_(5, new TensuraTamableEntity.WanderAroundPosGoal(this));
      this.f_21345_.m_25352_(6, new LookAtPlayerGoal(this, Player.class, 10.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(2, new KnightSpiderEntity.KnightSpiderAttackGoal(this, 1.2D, true));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, Player.class, false, (Predicate)null));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, Villager.class, false, (Predicate)null));
      this.f_21346_.m_25352_(5, new NonTameRandomTargetGoal(this, Animal.class, false, (entity) -> {
         return !(entity instanceof KnightSpiderEntity);
      }));
      this.f_21346_.m_25352_(5, new NonTameRandomTargetGoal(this, IronGolem.class, false, (Predicate)null));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(SLAMMING_ATTACK, false);
      this.f_19804_.m_135372_(SLAMMING_FALL, false);
      this.f_19804_.m_135372_(SADDLED, Boolean.FALSE);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128379_("Slamming", this.isSlamming());
      compound.m_128379_("SlammingFall", this.isSlammingFall());
      compound.m_128379_("Saddled", this.isSaddled());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.setSlamming(compound.m_128471_("Slamming"));
      this.setSlammingFall(compound.m_128471_("SlammingFall"));
      this.setSaddled(compound.m_128471_("Saddled"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      this.f_19804_.m_135381_(MISC_ANIMATION, animation);
   }

   public boolean isSlamming() {
      return (Boolean)this.f_19804_.m_135370_(SLAMMING_ATTACK);
   }

   public void setSlamming(boolean slamming) {
      this.f_19804_.m_135381_(SLAMMING_ATTACK, slamming);
   }

   public boolean isSlammingFall() {
      return (Boolean)this.f_19804_.m_135370_(SLAMMING_FALL);
   }

   public void setSlammingFall(boolean falling) {
      this.f_19804_.m_135381_(SLAMMING_FALL, falling);
   }

   public boolean isSaddled() {
      return (Boolean)this.f_19804_.m_135370_(SADDLED);
   }

   public void setSaddled(boolean saddled) {
      this.f_19804_.m_135381_(SADDLED, saddled);
   }

   public boolean m_5957_() {
      return false;
   }

   public boolean m_7848_(Animal pOtherAnimal) {
      return false;
   }

   public KnightSpiderEntity getBreedOffspring(ServerLevel pLevel, AgeableMob pOtherParent) {
      KnightSpiderEntity spider = (KnightSpiderEntity)((EntityType)TensuraEntityTypes.KNIGHT_SPIDER.get()).m_20615_(pLevel);
      if (spider == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            spider.m_21816_(uuid);
            spider.m_7105_(true);
         }

         return spider;
      }
   }

   public boolean m_7327_(Entity pEntity) {
      boolean flag = super.m_7327_(pEntity);
      if (flag && this.getMiscAnimation() == 0) {
         this.setMiscAnimation(1);
      }

      return flag;
   }

   public void m_7601_(BlockState pState, Vec3 pMotionMultiplier) {
      if (!pState.m_204336_(TensuraTags.Blocks.WEB_BLOCKS)) {
         super.m_7601_(pState, pMotionMultiplier);
      }
   }

   protected float m_6041_() {
      BlockState blockstate = this.f_19853_.m_8055_(this.m_20183_());
      return blockstate.m_204336_(TensuraTags.Blocks.WEB_BLOCKS) ? 1.0F : super.m_6041_();
   }

   protected float m_20098_() {
      BlockState blockstate = this.f_19853_.m_8055_(this.m_20183_());
      return blockstate.m_204336_(TensuraTags.Blocks.WEB_BLOCKS) ? 1.0F : super.m_20098_();
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (this.f_19853_.f_46443_) {
            boolean flag = this.m_21830_(player) || this.m_21824_();
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         } else if (this.m_21824_() && this.m_21830_(player)) {
            Item item = itemstack.m_41720_();
            if (item.equals(TensuraMaterialItems.MONSTER_SADDLE.get()) && !this.isSaddled()) {
               if (!player.m_150110_().f_35937_) {
                  itemstack.m_41774_(1);
               }

               this.setSaddled(true);
               this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
               return InteractionResult.SUCCESS;
            } else if (this.isSaddled() && item.equals(Items.f_42574_)) {
               this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
               this.m_19998_((ItemLike)TensuraMaterialItems.MONSTER_SADDLE.get());
               this.setSaddled(false);
               return InteractionResult.SUCCESS;
            } else {
               if (!player.m_36341_() && this.isSaddled()) {
                  if (player.m_146895_() == null) {
                     this.m_21839_(false);
                     this.setWandering(false);
                     player.m_7998_(this, true);
                  }
               } else {
                  this.commanding(player);
               }

               return InteractionResult.SUCCESS;
            }
         } else {
            return super.m_6071_(player, hand);
         }
      }
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack) && this.m_21223_() < this.m_21233_()) {
         if (!player.m_7500_()) {
            itemstack.m_41774_(1);
         }

         this.m_8035_();
         this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
         return InteractionResult.SUCCESS;
      } else {
         return InteractionResult.PASS;
      }
   }

   public void m_8035_() {
      super.m_8035_();
      this.setMiscAnimation(1);
      this.m_5634_(5.0F);
   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_41720_() instanceof HealingPotionItem ? false : pStack.m_41614_();
   }

   public boolean m_7132_() {
      return this.m_20160_();
   }

   public double getCustomJump() {
      return this.m_21133_(Attributes.f_22288_);
   }

   public boolean m_6146_() {
      return true;
   }

   @Nullable
   public LivingEntity getControllingPassenger() {
      Iterator var1 = this.m_20197_().iterator();

      while(var1.hasNext()) {
         Entity passenger = (Entity)var1.next();
         if (passenger instanceof Player) {
            Player player = (Player)passenger;
            if (player.equals(this.m_21826_())) {
               return player;
            }
         }
      }

      return null;
   }

   public void m_7332_(Entity passenger) {
      if (this.m_20363_(passenger)) {
         passenger.m_183634_();
         float radius = 2.7F;
         float angle = 0.017453292F * this.f_20883_;
         double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
         double extraZ = (double)(radius * Mth.m_14089_(angle));
         double yOffset = this.m_20186_() - 0.25D + this.m_6048_() + passenger.m_6049_();
         passenger.m_6034_(this.m_20185_() + extraX, yOffset, this.m_20189_() + extraZ);
      }
   }

   public void m_7888_(int pJumpPower) {
      if (pJumpPower >= 90) {
         this.playerJumpPendingScale = 1.0F;
      } else {
         if (pJumpPower < 0) {
            pJumpPower = 0;
         }

         this.playerJumpPendingScale = 0.4F + 0.4F * (float)pJumpPower / 90.0F;
      }

   }

   public void m_7199_(int pJumpPower) {
      if (this.m_20096_()) {
         this.playJumpSound();
         this.setMiscAnimation(2);
      }
   }

   public void m_8012_() {
   }

   public void m_7023_(Vec3 pTravelVector) {
      if (this.m_6084_()) {
         LivingEntity livingentity = this.getControllingPassenger();
         if (this.m_20160_() && livingentity != null) {
            this.m_146922_(livingentity.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(livingentity.m_146909_() * 0.5F);
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.f_20883_;
            float f = livingentity.f_20900_ * 0.5F;
            float f1 = livingentity.f_20902_;
            if (f1 <= 0.0F) {
               f1 *= 0.25F;
            }

            if (this.playerJumpPendingScale > 0.0F && !this.isPlayerJumping() && this.f_19861_) {
               double d0 = this.getCustomJump() * (double)this.playerJumpPendingScale * (double)this.m_20098_();
               double d1 = d0 + this.m_182332_();
               Vec3 vec3 = this.m_20184_();
               this.m_20334_(vec3.f_82479_, d1, vec3.f_82481_);
               this.setPlayerJumping(true);
               this.f_19812_ = true;
               ForgeHooks.onLivingJump(this);
               if (f1 > 0.0F) {
                  float f2 = Mth.m_14031_(this.m_146908_() * 0.017453292F);
                  float f3 = Mth.m_14089_(this.m_146908_() * 0.017453292F);
                  this.m_20256_(this.m_20184_().m_82520_((double)(-0.4F * f2 * this.playerJumpPendingScale), 0.0D, (double)(0.4F * f3 * this.playerJumpPendingScale)));
               }

               this.playerJumpPendingScale = 0.0F;
            }

            this.f_20887_ = this.m_6113_() * 0.1F;
            if (this.m_6109_()) {
               float speed = (float)this.m_21133_(Attributes.f_22279_);
               if (livingentity.m_20142_()) {
                  speed = (float)((double)speed * 1.25D);
               }

               this.m_7910_(speed);
               if (this.isInFluidType((fluidType, height) -> {
                  return height > this.m_20204_();
               }) && f1 > 0.0F) {
                  this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.03D, 0.0D));
                  super.m_7023_(new Vec3((double)f, (double)livingentity.f_20901_, (double)f1));
               } else {
                  super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
               }
            } else if (livingentity instanceof Player) {
               this.m_20256_(Vec3.f_82478_);
            }

            if (this.f_19861_) {
               this.playerJumpPendingScale = 0.0F;
               this.setPlayerJumping(false);
            }

            this.m_146872_();
         } else {
            this.f_20887_ = 0.02F;
            super.m_7023_(pTravelVector);
         }
      }

   }

   protected void m_5907_() {
      super.m_5907_();
      if (this.isSaddled() && !this.m_9236_().m_5776_()) {
         this.m_19998_((ItemLike)TensuraMaterialItems.MONSTER_SADDLE.get());
      }

   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19310_ || source == DamageSource.f_19314_ || source == DamageSource.f_19325_ || source == DamageSource.f_19309_ || super.m_6673_(source);
   }

   public int m_5792_() {
      return 1;
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.knightSpiderSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   protected float getClimbSpeedMultiplier() {
      return 2.0F;
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      if (this.isSlammingFall() && pFallDistance >= 5.0F) {
         this.setMiscAnimation(4);
         this.setSlamming(Boolean.FALSE);
         this.setSlammingFall(Boolean.FALSE);
         AABB aabb = AABB.m_165882_(this.m_20182_(), 16.0D, 16.0D, 16.0D);
         List<LivingEntity> list = this.m_9236_().m_6443_(LivingEntity.class, aabb, (entityx) -> {
            return !entityx.m_7307_(this) && entityx != this.m_21826_() && !(entityx instanceof KnightSpiderEntity);
         });
         Iterator var10 = list.iterator();

         while(var10.hasNext()) {
            LivingEntity target = (LivingEntity)var10.next();
            target.m_6469_(DamageSource.m_19370_(this), 30.0F);
            SkillHelper.knockBack(this, target, 1.0F);
         }

         if (this.f_19853_.m_46469_().m_46207_(GameRules.f_46132_)) {
            SkillHelper.launchBlock(this, this.m_20182_(), 7, 1, 0.5F, 0.3F, (blockState) -> {
               return this.m_217043_().m_188503_(3) != 1 ? false : blockState.m_204336_(TensuraTags.Blocks.EARTH_DOMINATING);
            }, (blockPos) -> {
               return true;
            });
         }

         this.f_19853_.m_7703_(this, DamageSource.m_19370_(this), (ExplosionDamageCalculator)null, (double)this.m_20097_().m_123341_(), (double)this.m_20097_().m_123342_(), (double)this.m_20097_().m_123343_(), 5.0F, false, BlockInteraction.NONE);
         return false;
      } else if (pFallDistance < 16.0F) {
         return false;
      } else {
         if (pFallDistance > 16.0F) {
            this.m_5496_(SoundEvents.f_12319_, 0.4F, 1.0F);
         }

         int i = this.m_5639_(pFallDistance - 10.0F, pMultiplier);
         if (i <= 0) {
            return false;
         } else {
            this.m_6469_(pSource, (float)i);
            if (this.m_20160_()) {
               Iterator var5 = this.m_146897_().iterator();

               while(var5.hasNext()) {
                  Entity entity = (Entity)var5.next();
                  entity.m_6469_(pSource, (float)i);
               }
            }

            this.m_21229_();
            return true;
         }
      }
   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_12432_;
   }

   protected SoundEvent m_7975_(DamageSource source) {
      return SoundEvents.f_12434_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_12433_;
   }

   public SoundSource m_5720_() {
      return SoundSource.HOSTILE;
   }

   protected void playJumpSound() {
      this.m_5496_((SoundEvent)TensuraSoundEvents.SMALL_JUMP_IMPACT.get(), 0.4F, 1.0F);
   }

   public void m_8119_() {
      super.m_8119_();
      this.targetingMovementHelper();
      if (this.m_5448_() != null) {
         if (!this.isSlamming()) {
            if (++this.slammingCoolDown == 100) {
               this.slammingCoolDown = 0;
               this.setSlamming(Boolean.TRUE);
            }
         } else if (this.m_20077_() || this.m_20072_()) {
            this.setSlammingFall(Boolean.FALSE);
            this.setSlamming(Boolean.FALSE);
         }
      }

      if (!this.f_19853_.m_5776_()) {
         LivingEntity controller = this.getControllingPassenger();
         if (!this.m_21824_() || controller != null && this.m_21830_(controller)) {
            this.breakBlocks(this, 1.0F, false);
         }
      }

      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         int animationTick = this.getMiscAnimation() == 3 ? 15 : (this.getMiscAnimation() == 2 ? 20 : 10);
         if (this.miscAnimationTicks > animationTick) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   public void mountAbility(Player rider) {
      if (this.getMiscAnimation() != 2) {
         if (this.getMiscAnimation() != 3) {
            if (this.getMiscAnimation() != 4) {
               if (!this.m_6162_()) {
                  if (!this.m_20096_() && !this.isInFluidType()) {
                     this.setSlammingFall(Boolean.TRUE);
                  } else {
                     LivingEntity target = SkillHelper.getTargetingEntity(rider, 8.0D, false);
                     if (target != null) {
                        this.m_7327_(target);
                     }
                  }

               }
            }
         }
      }
   }

   private boolean isFalling() {
      if (this.m_20096_()) {
         return false;
      } else if (this.isInFluidType()) {
         return false;
      } else {
         return this.f_19789_ > 3.0F;
      }
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.getMiscAnimation() != 2 && this.getMiscAnimation() != 3 && this.getMiscAnimation() != 4) {
         if (this.m_21224_()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.knight_spider.dead", EDefaultLoopTypes.LOOP));
         } else if (this.isFalling()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.knight_spider.falling", EDefaultLoopTypes.LOOP));
         } else if (this.m_21825_()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.knight_spider.stay", EDefaultLoopTypes.LOOP));
         } else if (event.isMoving()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.knight_spider.walk", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.knight_spider.idle", EDefaultLoopTypes.LOOP));
         }

         return PlayState.CONTINUE;
      } else {
         return PlayState.CONTINUE;
      }
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.knight_spider.bite", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.knight_spider.leap", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.knight_spider.jump", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 4) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.knight_spider.land", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   public void setPlayerJumping(boolean playerJumping) {
      this.playerJumping = playerJumping;
   }

   public boolean isPlayerJumping() {
      return this.playerJumping;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(KnightSpiderEntity.class, EntityDataSerializers.f_135028_);
      SLAMMING_ATTACK = SynchedEntityData.m_135353_(KnightSpiderEntity.class, EntityDataSerializers.f_135035_);
      SLAMMING_FALL = SynchedEntityData.m_135353_(KnightSpiderEntity.class, EntityDataSerializers.f_135035_);
      SADDLED = SynchedEntityData.m_135353_(KnightSpiderEntity.class, EntityDataSerializers.f_135035_);
   }

   static class KnightSpiderLeapGoal extends LeapWithStrengthGoal {
      private final KnightSpiderEntity spider;

      public KnightSpiderLeapGoal(KnightSpiderEntity spider) {
         super(spider, 0.7F, 3.0F, 10.0D, 20.0D, 20);
         this.spider = spider;
      }

      public void m_8056_() {
         super.m_8056_();
         this.spider.setMiscAnimation(2);
      }

      public boolean m_8045_() {
         if (this.spider.getMiscAnimation() != 0) {
            return false;
         } else {
            return !this.spider.m_20096_();
         }
      }
   }

   static class KnightSpiderAttackGoal extends MeleeAttackGoal {
      private final KnightSpiderEntity spider;

      public KnightSpiderAttackGoal(KnightSpiderEntity spider, double pSpeedModifier, boolean pFollowingTargetEvenIfNotSeen) {
         super(spider, pSpeedModifier, pFollowingTargetEvenIfNotSeen);
         this.spider = spider;
      }

      public boolean m_8036_() {
         return this.spider.m_21827_() ? false : super.m_8036_();
      }

      public boolean m_8045_() {
         return this.spider.m_21827_() ? false : super.m_8045_();
      }

      public void m_8037_() {
         LivingEntity target = this.spider.m_5448_();
         if (target != null) {
            if (this.spider.isSlamming() && !this.spider.isSlammingFall() && ++this.spider.ticksJump >= 40 && this.spider.m_20270_(target) <= 10.0F) {
               if (this.spider.m_20096_()) {
                  this.spider.setMiscAnimation(3);
                  this.spider.m_20256_(new Vec3(this.spider.m_20184_().f_82479_, this.spider.m_20184_().f_82480_ + 1.2000000476837158D, this.spider.m_20184_().f_82481_));
               } else if (!this.spider.m_20077_() && !this.spider.m_20072_()) {
                  this.spider.ticksJump = 0;
                  this.spider.setSlammingFall(Boolean.TRUE);
               }
            }

            super.m_8037_();
         }
      }

      protected double m_6639_(LivingEntity pAttackTarget) {
         return (double)(this.f_25540_.m_20205_() * this.f_25540_.m_20205_() * 3.0F + pAttackTarget.m_20205_());
      }
   }
}
