package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.IGiantMob;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.barrier.BarrierPart;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.entity.variant.SlimeType;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Registry;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerBossEvent;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.util.Mth;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.BossEvent.BossBarColor;
import net.minecraft.world.BossEvent.BossBarOverlay;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.monster.Monster;
import net.minecraft.world.entity.monster.Slime;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.storage.loot.BuiltInLootTables;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.event.ForgeEventFactory;

public class SupermassiveSlimeEntity extends MetalSlimeEntity implements IGiantMob {
   private final ServerBossEvent bossEvent;

   public SupermassiveSlimeEntity(EntityType<? extends SupermassiveSlimeEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.bossEvent = new ServerBossEvent(this.m_5446_(), BossBarColor.BLUE, BossBarOverlay.NOTCHED_20);
      this.regenAmount = 20;
      this.f_21364_ = 1000;
      this.setSize(this.m_217043_().m_216339_(18, 21), false, false, false);
      this.setMassive(true);
   }

   public static AttributeSupplier setAttributes() {
      return Monster.m_33035_().m_22268_(Attributes.f_22276_, 200.0D).m_22268_(Attributes.f_22284_, 2.0D).m_22268_(Attributes.f_22281_, 20.0D).m_22268_(Attributes.f_22278_, 1.0D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22288_, 3.0D).m_22268_(Attributes.f_22279_, 2.299999952316284D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 4.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new TensuraTamableEntity.WanderAroundPosGoal(this, 60, 1.0D, 10, 7));
      this.f_21345_.m_25352_(5, new RandomLookAroundGoal(this));
      this.f_21346_.m_25352_(2, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21345_.m_25352_(4, new LookAtPlayerGoal(this, Player.class, 16.0F));
      this.f_21346_.m_25352_(6, new NearestAttackableTargetGoal(this, Slime.class, false));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(1, new WanderingFollowOwnerGoal(this, 0.2D, 20.0F, 5.0F, false));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(4, new NearestAttackableTargetGoal(this, Player.class, 10, true, false, this::m_21674_));
   }

   public void m_7378_(CompoundTag pCompound) {
      super.m_7378_(pCompound);
      if (this.m_8077_()) {
         this.bossEvent.m_6456_(this.m_5446_());
      }

   }

   public void m_7332_(Entity passenger) {
      if (this.m_20363_(passenger)) {
         passenger.m_183634_();
         float radius = -0.25F;
         float angle = 0.017453292F * this.f_20883_;
         double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
         double extraZ = (double)(radius * Mth.m_14089_(angle));
         passenger.m_6034_(this.m_20185_() + extraX, this.m_20186_() + (double)this.m_20206_() * 0.25D, this.m_20189_() + extraZ);
      }

   }

   public boolean shouldRiderSit() {
      return false;
   }

   public boolean ignoreDamageSource(DamageSource source) {
      return false;
   }

   public boolean m_6469_(DamageSource pSource, float amount) {
      Entity sourceEntity = pSource.m_7639_();
      if (sourceEntity instanceof SlimeEntity) {
         return false;
      } else {
         Entity var6 = pSource.m_7640_();
         if (var6 instanceof Projectile) {
            Projectile projectile = (Projectile)var6;
            this.setHurt(Boolean.TRUE);
            this.m_5496_(SoundEvents.f_11803_, 1.0F, 0.8F);
            projectile.m_142687_(RemovalReason.KILLED);
            return false;
         } else {
            if (sourceEntity instanceof ServerPlayer) {
               ServerPlayer serverPlayer = (ServerPlayer)sourceEntity;
               this.m_6457_(serverPlayer);
            }

            return super.m_6469_(pSource, amount);
         }
      }
   }

   public void m_6593_(@Nullable Component pName) {
      super.m_6593_(pName);
      this.bossEvent.m_6456_(this.m_5446_());
   }

   public void m_6457_(ServerPlayer pPlayer) {
      super.m_6457_(pPlayer);
      this.bossEvent.m_6543_(pPlayer);
   }

   public void m_6452_(ServerPlayer pPlayer) {
      super.m_6452_(pPlayer);
      this.bossEvent.m_6539_(pPlayer);
   }

   public boolean isDyeable() {
      return true;
   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.f_19853_.f_46443_) {
         if (this.m_6084_()) {
            if (this.setWantedTarget(this, 36.0D)) {
               this.wasOnGround = true;
            }

            Optional<ManasSkillInstance> optional = SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)ExtraSkills.ULTRASPEED_REGENERATION.get());
            if (optional.isPresent() && ((ManasSkillInstance)optional.get()).isToggled()) {
               ((ManasSkillInstance)optional.get()).setToggled(false);
            }

         }
      }
   }

   public void waterMovement() {
      if (this.m_20069_()) {
         if (!this.m_20160_()) {
            this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.05D, 0.0D));
         }
      }
   }

   protected void m_8024_() {
      super.m_8024_();
      if (this.f_19797_ % 20 == 0) {
         List<BarrierPart> list = this.f_19853_.m_45976_(BarrierPart.class, this.m_20191_().m_82400_(1.0D));
         if (!list.isEmpty()) {
            Iterator var2 = list.iterator();

            while(var2.hasNext()) {
               BarrierPart barrier = (BarrierPart)var2.next();
               this.m_7327_(barrier);
            }
         }
      }

      if (!this.m_21824_() && this.breakBlocks(this, 1.0F, false)) {
         this.setJumpAnimation(Boolean.TRUE);
      }

      this.bossEvent.m_142711_(this.m_21223_() / this.m_21233_());
   }

   public boolean breakableBlocks(LivingEntity living, BlockPos pos, BlockState state) {
      return state.m_204336_(TensuraTags.Blocks.BOSS_IMMUNE) ? false : ForgeEventFactory.onEntityDestroyBlock(living, pos, state);
   }

   public boolean dropBlockLoot(LivingEntity entity, BlockState state) {
      return !state.m_204336_(TensuraTags.Blocks.SKILL_BREAK_EASY);
   }

   protected ResourceLocation m_7582_() {
      if (this.getSlimeType().equals(SlimeType.SUMMONED)) {
         return BuiltInLootTables.f_78712_;
      } else {
         return this.isChilled() ? new ResourceLocation(Registry.f_122826_.m_7981_(this.m_6095_()).m_135827_(), "entities/chilled_supermassive_slime") : this.m_6095_().m_20677_();
      }
   }

   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if (this.getSize() == 1) {
         this.setSize(this.m_217043_().m_216339_(18, 21), false, false, false);
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }
}
