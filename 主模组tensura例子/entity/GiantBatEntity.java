package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.api.entity.ai.FlyingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.navigator.NoSpinFlightPathNavigator;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.FLyingTamableEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import com.github.manasmods.tensura.util.damage.TensuraEntityDamageSource;
import java.util.EnumSet;
import java.util.Iterator;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.BlockPos.MutableBlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.util.RandomSource;
import net.minecraft.world.Difficulty;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.MenuProvider;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.control.MoveControl;
import net.minecraft.world.entity.ai.control.MoveControl.Operation;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.RandomStrollGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.ai.navigation.GroundPathNavigation;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.monster.Monster;
import net.minecraft.world.entity.npc.Villager;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ChestMenu;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ClipContext;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.ClipContext.Block;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.common.Tags.Items;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class GiantBatEntity extends FLyingTamableEntity implements IAnimatable {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Boolean> HANGING;
   private static final EntityDataAccessor<Boolean> CHESTED;
   private boolean validHangingPos = false;
   private int checkHangingTime;
   private BlockPos prevHangPos;
   public int timeHanging = 0;
   public int stayGround = 0;
   public SimpleContainer inventory;
   public MenuProvider inventoryMenu;
   private boolean hasChestVarChanged = false;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public GiantBatEntity(EntityType<? extends TamableAnimal> type, Level worldIn) {
      super(type, worldIn);
      this.switchNavigator(true);
      this.f_21364_ = 30;
      this.initInventory();
   }

   public static AttributeSupplier setAttributes() {
      return Monster.m_33035_().m_22268_(Attributes.f_22276_, 45.0D).m_22268_(Attributes.f_22281_, 8.0D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22279_, 0.30000001192092896D).m_22268_(Attributes.f_22278_, 0.8D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new FlyingFollowOwnerGoal(this, 1.0D, 10.0F, 4.0F, true, false) {
         public void m_8056_() {
            super.m_8056_();
            if (GiantBatEntity.this.isHanging()) {
               GiantBatEntity.this.setHanging(false);
               GiantBatEntity.this.setFlying(true);
            }
         }
      });
      this.f_21345_.m_25352_(3, new GiantBatEntity.GiantBatAttackGoal(this));
      this.f_21345_.m_25352_(4, new GiantBatEntity.GiantBatRandomWanderGoal(this));
      this.f_21345_.m_25352_(4, new GiantBatEntity.BatFlyAndHangGoal(this));
      this.f_21345_.m_25352_(4, new BreedGoal(this, 1.0D));
      this.f_21345_.m_25352_(5, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21345_.m_25352_(5, new RandomLookAroundGoal(this));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(2, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(3, new GiantBatEntity.BatTargetEntitiesGoal(this, Player.class, false, (Predicate)null));
      this.f_21346_.m_25352_(4, new GiantBatEntity.BatTargetEntitiesGoal(this, Villager.class, false, (Predicate)null));
      this.f_21346_.m_25352_(5, new GiantBatEntity.BatTargetEntitiesGoal(this, Animal.class, false, (entity) -> {
         return entity.m_6095_().m_204039_(TensuraTags.EntityTypes.ANIMAL_PREY);
      }));
      this.f_21346_.m_25352_(6, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void switchNavigator(boolean onLand) {
      if (onLand) {
         this.f_21342_ = new MoveControl(this);
         this.f_21344_ = new GroundPathNavigation(this, this.f_19853_);
         this.wasFlying = false;
      } else {
         this.f_21342_ = new GiantBatEntity.BatMoveControl();
         this.f_21344_ = new NoSpinFlightPathNavigator(this, this.f_19853_);
         this.wasFlying = true;
      }

   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(HANGING, false);
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(CHESTED, Boolean.FALSE);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128379_("Hanging", this.isHanging());
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128379_("Chested", this.isChested());
      if (this.inventory != null) {
         ListTag listTag = new ListTag();

         for(int i = 0; i < this.inventory.m_6643_(); ++i) {
            ItemStack itemstack = this.inventory.m_8020_(i);
            if (!itemstack.m_41619_()) {
               CompoundTag CompoundNBT = new CompoundTag();
               CompoundNBT.m_128344_("Slot", (byte)i);
               itemstack.m_41739_(CompoundNBT);
               listTag.add(CompoundNBT);
            }
         }

         compound.m_128365_("Items", listTag);
      }

   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(HANGING, compound.m_128471_("Hanging"));
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.setChested(compound.m_128471_("Chested"));
      ListTag listTag;
      int i;
      CompoundTag CompoundNBT;
      int j;
      if (this.inventory != null) {
         listTag = compound.m_128437_("Items", 10);
         this.initInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.inventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      } else {
         listTag = compound.m_128437_("Items", 10);
         this.initInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.initInventory();
            this.inventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      }

   }

   public boolean isHanging() {
      return (Boolean)this.f_19804_.m_135370_(HANGING);
   }

   public void setHanging(boolean hanging) {
      this.f_19804_.m_135381_(HANGING, hanging);
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public boolean isChested() {
      return (Boolean)this.f_19804_.m_135370_(CHESTED);
   }

   public void setChested(boolean chested) {
      this.f_19804_.m_135381_(CHESTED, chested);
      this.hasChestVarChanged = true;
   }

   public void m_7350_(EntityDataAccessor<?> pKey) {
      if (f_21798_.equals(pKey) || HANGING.equals(pKey)) {
         this.m_6210_();
      }

      super.m_7350_(pKey);
   }

   public boolean canHangFrom(BlockPos pos, BlockState state) {
      if (!this.f_19853_.m_46859_(pos.m_7495_())) {
         return false;
      } else {
         return !this.f_19853_.m_46859_(pos.m_6625_(2)) ? false : state.m_60783_(this.f_19853_, pos, Direction.DOWN);
      }
   }

   public BlockPos posAbove() {
      return new BlockPos(Mth.m_14107_(this.m_20185_()), Mth.m_14107_(this.m_20191_().f_82292_ + 0.10000000149011612D), Mth.m_14107_(this.m_20189_()));
   }

   public GiantBatEntity getBreedOffspring(ServerLevel pLevel, AgeableMob pOtherParent) {
      GiantBatEntity bat = (GiantBatEntity)((EntityType)TensuraEntityTypes.GIANT_BAT.get()).m_20615_(pLevel);
      if (bat == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            bat.m_21816_(uuid);
            bat.m_7105_(true);
         }

         return bat;
      }
   }

   public AABB m_6921_() {
      double scale = this.m_6162_() ? 1.5D : 3.0D;
      return this.m_20191_().m_82377_(scale, scale, scale);
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions entitydimensions = super.m_6972_(pPose);
      return !this.m_21825_() && !this.isHanging() ? entitydimensions : entitydimensions.m_20390_(1.0F, 1.5F);
   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.f_19853_.f_46443_) {
         if (this.isHanging()) {
            BlockPos above = this.posAbove();
            if (this.checkHangingTime-- < 0 || this.f_19796_.m_188501_() < 0.1F || this.prevHangPos != above) {
               this.validHangingPos = this.canHangFrom(above, this.f_19853_.m_8055_(above));
               this.checkHangingTime = 5 + this.f_19796_.m_188503_(5);
               this.prevHangPos = above;
            }

            if (this.validHangingPos) {
               this.m_20256_(this.m_20184_().m_82542_(0.10000000149011612D, 0.30000001192092896D, 0.10000000149011612D).m_82520_(0.0D, 0.08D, 0.0D));
            } else {
               this.setHanging(false);
               this.setFlying(true);
            }

            ++this.timeHanging;
         } else {
            this.timeHanging = 0;
            this.validHangingPos = false;
            this.prevHangPos = null;
         }
      }

      int i;
      if (this.hasChestVarChanged && this.inventory != null && !this.isChested()) {
         for(i = 3; i < 18; ++i) {
            if (!this.inventory.m_8020_(i).m_41619_()) {
               if (!this.f_19853_.f_46443_) {
                  this.m_5552_(this.inventory.m_8020_(i), 1.0F);
               }

               this.inventory.m_8016_(i);
            }
         }

         this.hasChestVarChanged = false;
      }

      if (this.stayGround > 0) {
         --this.stayGround;
      }

      if (this.getMiscAnimation() != 0) {
         i = this.getMiscAnimation() == 2 ? 24 : 15;
         if (this.miscAnimationTicks++ > i) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   protected void handleFlying() {
      if (!this.f_19853_.m_5776_()) {
         if (this.m_29443_()) {
            if (this.timeFlying % 10 == 0) {
               this.m_216990_(SoundEvents.f_12230_);
            }

            ++this.timeFlying;
            this.m_20242_(true);
            if (!this.wasFlying) {
               this.switchNavigator(false);
            }

            if (this.stayGround > 0 || this.m_21827_()) {
               this.setFlying(false);
            }
         } else {
            this.timeFlying = 0;
            this.m_20242_(false);
            if (this.wasFlying) {
               this.switchNavigator(true);
            }
         }

      }
   }

   public boolean hasUltrasonicWave() {
      return SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)IntrinsicSkills.ULTRASONIC_WAVES.get()).isPresent();
   }

   public void ultrasonicWave(LivingEntity target) {
      Level level = this.m_9236_();
      Vec3 targetPos = target.m_146892_();
      Vec3 source = this.m_20182_().m_82520_(0.0D, (double)this.m_20192_(), 0.0D);
      Vec3 sourceToTarget = targetPos.m_82546_(source);
      Vec3 normalizes = sourceToTarget.m_82541_();
      level.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_215771_, SoundSource.PLAYERS, 5.0F, 1.0F);

      for(int particleIndex = 1; particleIndex < Mth.m_14107_(sourceToTarget.m_82553_()); ++particleIndex) {
         Vec3 particlePos = source.m_82549_(normalizes.m_82490_((double)particleIndex));
         ((ServerLevel)level).m_8767_(ParticleTypes.f_235902_, particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_, 1, 0.0D, 0.0D, 0.0D, 0.0D);
         AABB aabb = (new AABB(new BlockPos(particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_))).m_82400_(2.0D);
         List<LivingEntity> list = level.m_6443_(LivingEntity.class, aabb, (livingx) -> {
            return !livingx.m_7306_(this);
         });
         if (!list.isEmpty()) {
            Iterator var11 = list.iterator();

            while(var11.hasNext()) {
               LivingEntity living = (LivingEntity)var11.next();
               DamageSource damagesource = (new TensuraEntityDamageSource("sonic_boom", this)).setMpCost(30.0D).setSkill(SkillUtils.getSkillOrNull(this, (ManasSkill)IntrinsicSkills.ULTRASONIC_WAVES.get())).m_19389_();
               living.m_6469_(damagesource, (float)this.m_21133_(Attributes.f_22281_) * 1.5F);
               double d0 = Math.max(0.0D, 1.0D - living.m_21133_(Attributes.f_22278_));
               Vec3 vec3 = this.m_20252_(1.0F).m_82541_().m_82490_(2.0D * d0 * 0.10000000149011612D);
               if (vec3.m_82556_() > 0.0D) {
                  living.m_5997_(vec3.f_82479_, vec3.f_82480_, vec3.f_82481_);
               }
            }
         }
      }

   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (this.f_19853_.f_46443_) {
            boolean flag = this.m_21830_(player) || this.m_21824_();
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         } else if (this.m_21824_() && this.m_21830_(player)) {
            if (!this.m_6162_()) {
               Item item = itemstack.m_41720_();
               if (!this.isChested() && itemstack.m_204117_(Items.CHESTS_WOODEN)) {
                  this.setChested(true);
                  this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  if (!player.m_150110_().f_35937_) {
                     itemstack.m_41774_(1);
                  }

                  return InteractionResult.m_19078_(this.f_19853_.f_46443_);
               }

               if (this.isChested() && item.equals(net.minecraft.world.item.Items.f_42574_)) {
                  this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  this.m_19998_(Blocks.f_50087_);

                  for(int i = 0; i < this.inventory.m_6643_(); ++i) {
                     this.m_19983_(this.inventory.m_8020_(i));
                  }

                  this.inventory.m_6211_();
                  this.setChested(false);
                  return InteractionResult.SUCCESS;
               }
            }

            if (!player.m_36341_() && this.isChested()) {
               if (this.isChested()) {
                  this.openCustomInventoryScreen(player);
               }
            } else {
               this.commanding(player);
            }

            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         } else {
            return super.m_6071_(player, hand);
         }
      }
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack)) {
         if (this.m_21223_() < this.m_21233_()) {
            if (!player.m_7500_()) {
               itemstack.m_41774_(1);
            }

            this.m_8035_();
            this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            return InteractionResult.SUCCESS;
         }

         int i = this.m_146764_();
         if (!this.f_19853_.m_5776_() && i == 0 && this.m_5957_()) {
            this.m_142075_(player, hand, itemstack);
            this.m_27595_(player);
            this.setMiscAnimation(1);
            return InteractionResult.SUCCESS;
         }

         if (this.m_6162_()) {
            this.m_142075_(player, hand, itemstack);
            this.m_146740_(m_216967_(-i), true);
            this.m_9236_().m_6269_(player, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }
      }

      return InteractionResult.PASS;
   }

   public void m_8035_() {
      super.m_8035_();
      this.setMiscAnimation(1);
      this.m_5634_(5.0F);
      if (this.m_6162_()) {
         this.m_146758_(60);
      }

   }

   public boolean m_6898_(ItemStack pStack) {
      FoodProperties food = pStack.getFoodProperties(this);
      return food != null && food.m_38746_();
   }

   private void initInventory() {
      SimpleContainer chest = this.inventory;
      this.inventory = new SimpleContainer(27) {
         public boolean m_6542_(Player player) {
            return GiantBatEntity.this.m_6084_() && !GiantBatEntity.this.f_19817_;
         }
      };
      if (chest != null) {
         int i = Math.min(chest.m_6643_(), this.inventory.m_6643_());

         for(int j = 0; j < i; ++j) {
            ItemStack itemstack = chest.m_8020_(j);
            if (!itemstack.m_41619_()) {
               this.inventory.m_6836_(j, itemstack.m_41777_());
            }
         }
      }

   }

   public void openCustomInventoryScreen(Player pPlayer) {
      if (this.isChested()) {
         if (this.inventory != null) {
            pPlayer.m_5893_(this.getMenu());
            if (!pPlayer.f_19853_.f_46443_) {
               this.m_146852_(GameEvent.f_157803_, pPlayer);
            }

         }
      }
   }

   public void m_6667_(DamageSource cause) {
      super.m_6667_(cause);
      if (!this.f_19853_.m_5776_()) {
         if (this.inventory != null) {
            if (!this.m_6084_()) {
               for(int i = 0; i < this.inventory.m_6643_(); ++i) {
                  ItemStack itemstack = this.inventory.m_8020_(i);
                  if (!itemstack.m_41619_()) {
                     this.m_5552_(itemstack, 0.0F);
                  }
               }

            }
         }
      }
   }

   public MenuProvider getMenu() {
      if (this.inventoryMenu == null) {
         this.inventoryMenu = new MenuProvider() {
            public AbstractContainerMenu m_7208_(int menu, Inventory inventory, Player player) {
               return new ChestMenu(MenuType.f_39959_, menu, inventory, GiantBatEntity.this.inventory, 3);
            }

            public Component m_5446_() {
               return Component.m_237115_("container.chest");
            }
         };
      }

      return this.inventoryMenu;
   }

   protected void m_5907_() {
      super.m_5907_();
      if (this.isChested()) {
         if (!this.f_19853_.f_46443_) {
            this.m_19998_(Blocks.f_50087_);

            for(int i = 0; i < this.inventory.m_6643_(); ++i) {
               this.m_19983_(this.inventory.m_8020_(i));
            }
         }

         this.inventory.m_6211_();
         this.setChested(false);
      }

   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor worldIn, DifficultyInstance difficultyIn, MobSpawnType reason, @Nullable SpawnGroupData spawnDataIn, @Nullable CompoundTag dataTag) {
      if (reason == MobSpawnType.NATURAL && this.m_20186_() <= 50.0D) {
         this.doSpawnPose(worldIn);
      }

      return super.m_6518_(worldIn, difficultyIn, reason, spawnDataIn, dataTag);
   }

   public static boolean checkBatSpawnRules(EntityType<GiantBatEntity> pType, ServerLevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      if (pLevel.m_46791_() == Difficulty.PEACEFUL) {
         return false;
      } else if (!m_217057_(pType, pLevel, pSpawnType, pPos, pRandom)) {
         return false;
      } else if (pPos.m_123342_() > 50) {
         return pLevel.m_6018_().m_46462_();
      } else {
         MutableBlockPos above = new MutableBlockPos();
         above.m_122190_(pPos);
         int k = 0;

         while(pLevel.m_46859_(above) && above.m_123342_() < pLevel.m_151558_()) {
            above.m_122184_(0, 1, 0);
            ++k;
            if (k > 5) {
               return true;
            }
         }

         return false;
      }
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.giantBatSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   private void doSpawnPose(LevelAccessor level) {
      BlockPos above = this.m_20183_();
      int upBy = 100;

      for(int k = 0; level.m_46859_(above) && above.m_123342_() < this.f_19853_.m_151558_() && k < upBy; ++k) {
         above = above.m_7494_();
      }

      if (level.m_46859_(above)) {
         this.setFlying(true);
      } else {
         this.setHanging(true);
      }

      this.m_6034_((double)((float)above.m_123341_() + 0.5F), (double)above.m_123342_() - this.m_20191_().m_82376_(), (double)((float)above.m_123343_() + 0.5F));
   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_11731_;
   }

   protected SoundEvent m_7975_(DamageSource source) {
      return SoundEvents.f_11733_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_11732_;
   }

   public SoundSource m_5720_() {
      return SoundSource.HOSTILE;
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      String name;
      if (this.isHanging()) {
         name = "animation.giant_bat.hang";
      } else if (this.m_21825_()) {
         name = "animation.giant_bat.stay";
      } else if (!this.m_20096_() && !this.isInFluidType()) {
         if (event.isMoving() && this.getMiscAnimation() != 2) {
            name = "animation.giant_bat.fly";
         } else {
            name = "animation.giant_bat.idle_fly";
         }
      } else if (event.isMoving()) {
         name = "animation.giant_bat.crawl";
      } else {
         name = "animation.giant_bat.stand";
      }

      event.getController().setAnimation((new AnimationBuilder()).addAnimation(name, EDefaultLoopTypes.LOOP));
      return PlayState.CONTINUE;
   }

   private <T extends IAnimatable> PlayState playOncePredicate(AnimationEvent<T> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         if (this.getMiscAnimation() == 1) {
            event.getController().markNeedsReload();
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.giant_bat.bite", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().markNeedsReload();
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.giant_bat.sonic_attack", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::playOncePredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(GiantBatEntity.class, EntityDataSerializers.f_135028_);
      HANGING = SynchedEntityData.m_135353_(GiantBatEntity.class, EntityDataSerializers.f_135035_);
      CHESTED = SynchedEntityData.m_135353_(GiantBatEntity.class, EntityDataSerializers.f_135035_);
   }

   static class GiantBatAttackGoal extends Goal {
      private final GiantBatEntity bat;
      private int sonicCoolDown = 100;
      private Vec3 startOrbitFrom;
      private int orbitTime;
      private int maxOrbitTime;
      private boolean clockwise;

      public GiantBatAttackGoal(GiantBatEntity entity) {
         this.m_7021_(EnumSet.of(Flag.MOVE));
         this.bat = entity;
      }

      public boolean m_8036_() {
         if (this.bat.m_21827_()) {
            return false;
         } else {
            LivingEntity target = this.bat.m_5448_();
            return target != null && target.m_6084_();
         }
      }

      public void m_8037_() {
         if (this.bat.stayGround <= 0) {
            if (this.bat.isHanging()) {
               this.bat.setHanging(false);
               this.bat.setFlying(true);
            } else if (!this.bat.m_29443_()) {
               this.bat.setFlying(true);
            }
         } else {
            this.bat.setFlying(false);
         }

         LivingEntity target = this.bat.m_5448_();
         if (target != null && target.m_6084_()) {
            double distance = (double)this.bat.m_20270_(target);
            float f = this.bat.m_20205_() + target.m_20205_();
            if (this.startOrbitFrom == null) {
               if (this.bat.getMiscAnimation() != 2) {
                  this.bat.m_21573_().m_5624_(target, this.bat.m_29443_() ? 2.5D : 1.0D);
                  this.bat.m_7618_(Anchor.EYES, target.m_146892_());
               }
            } else if (this.orbitTime < this.maxOrbitTime && this.bat.stayGround <= 0) {
               ++this.orbitTime;
               --this.sonicCoolDown;
               float zoomIn = 1.0F - (float)this.orbitTime / (float)this.maxOrbitTime;
               Vec3 orbitPos = this.orbitAroundPos(3.0F + zoomIn * 5.0F).m_82520_(0.0D, (double)(4.0F + zoomIn * 3.0F), 0.0D);
               this.bat.m_21573_().m_26519_(orbitPos.f_82479_, orbitPos.f_82480_, orbitPos.f_82481_, this.bat.m_29443_() ? 2.5D : 1.0D);
               if (this.bat.m_29443_() && this.sonicCoolDown <= 25 && distance < 12.0D && this.bat.hasUltrasonicWave()) {
                  if (this.sonicCoolDown <= 15) {
                     this.bat.setMiscAnimation(2);
                  }

                  this.bat.m_7618_(Anchor.EYES, target.m_146892_());
               } else {
                  this.bat.m_7618_(Anchor.EYES, orbitPos);
               }
            } else {
               this.orbitTime = 0;
               this.startOrbitFrom = null;
            }

            if (distance < (double)f + 0.5D) {
               if (this.bat.m_142582_(target)) {
                  this.bat.setMiscAnimation(1);
                  if (target.m_6469_(DamageSource.m_19370_(this.bat), (float)this.bat.m_21133_(Attributes.f_22281_))) {
                     this.bat.m_5634_(5.0F);
                  }

                  this.maxOrbitTime = 60 + this.bat.m_217043_().m_188503_(80);
                  this.startOrbitFrom = target.m_146892_();
               }
            } else if (this.bat.getMiscAnimation() == 2 && this.sonicCoolDown <= 0) {
               this.bat.m_7618_(Anchor.EYES, target.m_146892_());
               this.sonicCoolDown = 100;
               this.bat.ultrasonicWave(target);
               this.bat.setMiscAnimation(0);
            }

         }
      }

      public void m_8056_() {
         this.orbitTime = 0;
         this.maxOrbitTime = 80;
         this.startOrbitFrom = null;
      }

      public Vec3 orbitAroundPos(float circleDistance) {
         float angle = 3.0F * (float)Math.toRadians((double)((float)(this.clockwise ? -this.orbitTime : this.orbitTime) * 3.0F));
         double extraX = (double)(circleDistance * Mth.m_14031_(angle));
         double extraZ = (double)(circleDistance * Mth.m_14089_(angle));
         return this.startOrbitFrom.m_82520_(extraX, 0.0D, extraZ);
      }
   }

   static class GiantBatRandomWanderGoal extends RandomStrollGoal {
      private final GiantBatEntity entity;

      public GiantBatRandomWanderGoal(GiantBatEntity bat) {
         super(bat, 1.0D, 15, false);
         this.entity = bat;
      }

      public boolean m_8036_() {
         if (this.entity.m_21827_()) {
            return false;
         } else if (this.entity.m_29443_()) {
            return false;
         } else {
            return this.entity.isHanging() ? false : super.m_8036_();
         }
      }

      public boolean m_8045_() {
         if (this.entity.m_21827_()) {
            return false;
         } else if (this.entity.m_29443_()) {
            return false;
         } else {
            return this.entity.isHanging() ? false : super.m_8045_();
         }
      }

      @Nullable
      protected Vec3 m_7037_() {
         Vec3 pos = Vec3.m_82512_(this.entity.getWanderPos());
         double distance = (Double)TensuraConfig.INSTANCE.entitiesConfig.tamedWanderRadius.get();
         return this.entity.isWandering() && this.entity.m_20238_(pos) >= distance * distance ? pos : super.m_7037_();
      }
   }

   static class BatFlyAndHangGoal extends Goal {
      private final GiantBatEntity entity;
      private boolean shouldHang = false;
      private double x;
      private double y;
      private double z;

      public BatFlyAndHangGoal(GiantBatEntity entity) {
         this.m_7021_(EnumSet.of(Flag.MOVE));
         this.entity = entity;
      }

      public boolean m_8036_() {
         if (this.entity.m_21827_()) {
            return false;
         } else if (this.entity.m_20160_()) {
            return false;
         } else if (this.entity.m_5448_() != null && this.entity.m_5448_().m_6084_()) {
            return false;
         } else if (this.entity.m_20159_()) {
            return false;
         } else if (this.entity.isHanging()) {
            return false;
         } else if (this.entity.stayGround > 0) {
            return false;
         } else if (!this.entity.m_29443_() && this.entity.m_217043_().m_188503_(70) != 0) {
            return false;
         } else {
            this.shouldHang = this.entity.timeFlying > 300;
            Vec3 target = this.getPosition();
            if (target == null) {
               return false;
            } else {
               this.x = target.f_82479_;
               this.y = target.f_82480_;
               this.z = target.f_82481_;
               return true;
            }
         }
      }

      @Nullable
      private Vec3 getPosition() {
         return this.shouldHang ? this.findHangFromPos() : this.findFlightPos();
      }

      public void m_8056_() {
         this.entity.setFlying(true);
         this.entity.setHanging(false);
         this.entity.m_21573_().m_26519_(this.x, this.y, this.z, 1.0D);
      }

      public void m_8037_() {
         if (this.shouldHang) {
            boolean canHang = this.entity.canHangFrom(this.entity.posAbove(), this.entity.f_19853_.m_8055_(this.entity.posAbove()));
            if (!this.entity.isHanging() && canHang) {
               this.entity.setHanging(true);
               this.entity.setFlying(false);
            }
         }

         if (this.entity.m_29443_() && this.entity.m_20096_() && this.entity.timeFlying > 40) {
            this.entity.setFlying(false);
         }

      }

      public boolean m_8045_() {
         if (this.shouldHang) {
            return !this.entity.m_21573_().m_26571_() && !this.entity.isHanging() && this.entity.stayGround <= 0;
         } else {
            return this.entity.m_29443_() && !this.entity.m_21573_().m_26571_() && this.entity.stayGround <= 0;
         }
      }

      public void m_8041_() {
         if (this.shouldHang) {
            this.entity.m_21573_().m_26573_();
         }

         this.shouldHang = false;
      }

      private Vec3 findFlightPos() {
         int range = 13;
         Vec3 heightAdjusted = this.entity.m_20182_().m_82520_((double)(this.entity.m_217043_().m_188503_(range * 2) - range), 0.0D, (double)(this.entity.m_217043_().m_188503_(range * 2) - range));
         Vec3 ground;
         if (this.entity.f_19853_.m_45527_(new BlockPos(Mth.m_14107_(heightAdjusted.f_82479_), Mth.m_14107_(heightAdjusted.f_82480_), Mth.m_14107_(heightAdjusted.f_82481_)))) {
            ground = this.groundPosition(heightAdjusted);
            heightAdjusted = new Vec3(heightAdjusted.f_82479_, ground.f_82480_ + 4.0D + (double)this.entity.m_217043_().m_188503_(3), heightAdjusted.f_82481_);
         } else {
            ground = this.groundPosition(heightAdjusted);

            BlockPos ceiling;
            for(ceiling = (new BlockPos(Mth.m_14107_(ground.f_82479_), Mth.m_14107_(ground.f_82480_), Mth.m_14107_(ground.f_82481_))).m_6630_(2); ceiling.m_123342_() < this.entity.f_19853_.m_151558_() && !this.entity.f_19853_.m_8055_(ceiling).m_60804_(this.entity.f_19853_, ceiling); ceiling = ceiling.m_7494_()) {
            }

            float randCeilVal = 0.3F + this.entity.m_217043_().m_188501_() * 0.5F;
            heightAdjusted = new Vec3(heightAdjusted.f_82479_, ground.f_82480_ + ((double)ceiling.m_123342_() - ground.f_82480_) * (double)randCeilVal, heightAdjusted.f_82481_);
         }

         BlockHitResult result = this.entity.f_19853_.m_45547_(new ClipContext(this.entity.m_146892_(), heightAdjusted, Block.COLLIDER, Fluid.NONE, this.entity));
         return result.m_6662_() == Type.MISS ? heightAdjusted : result.m_82450_();
      }

      public Vec3 groundPosition(Vec3 airPosition) {
         MutableBlockPos ground = new MutableBlockPos();
         ground.m_122169_(airPosition.f_82479_, airPosition.f_82480_, airPosition.f_82481_);

         boolean flag;
         for(flag = false; ground.m_123342_() < this.entity.f_19853_.m_151558_() && !this.entity.f_19853_.m_8055_(ground).m_60804_(this.entity.f_19853_, ground) && this.entity.f_19853_.m_6425_(ground).m_76178_(); flag = true) {
            ground.m_122184_(0, 1, 0);
         }

         ground.m_122184_(0, -1, 0);

         while(ground.m_123342_() > this.entity.f_19853_.m_141937_() && !this.entity.f_19853_.m_8055_(ground).m_60804_(this.entity.f_19853_, ground) && this.entity.f_19853_.m_6425_(ground).m_76178_()) {
            ground.m_122184_(0, -1, 0);
         }

         return Vec3.m_82512_(flag ? ground.m_7494_() : ground.m_7495_());
      }

      @Nullable
      public Vec3 findHangFromPos() {
         BlockPos blockpos = null;
         Random random = new Random();
         int range = 14;

         for(int i = 0; i < 15; ++i) {
            BlockPos pos = this.entity.m_20183_().m_7918_(random.nextInt(range) - range / 2, 0, random.nextInt(range) - range / 2);
            if (this.entity.f_19853_.m_46859_(pos) && this.entity.f_19853_.m_46749_(pos)) {
               while(this.entity.f_19853_.m_46859_(pos) && pos.m_123342_() < this.entity.f_19853_.m_151558_()) {
                  pos = pos.m_7494_();
               }

               if ((double)pos.m_123342_() > this.entity.m_20186_() - 1.0D && this.entity.canHangFrom(pos, this.entity.f_19853_.m_8055_(pos)) && this.hasLineOfToPos(pos)) {
                  blockpos = pos;
                  break;
               }
            }
         }

         return blockpos == null ? null : Vec3.m_82512_(blockpos);
      }

      public boolean hasLineOfToPos(BlockPos in) {
         BlockHitResult clip = this.entity.f_19853_.m_45547_(new ClipContext(this.entity.m_20299_(1.0F), new Vec3((double)in.m_123341_() + 0.5D, (double)in.m_123342_() + 0.5D, (double)in.m_123343_() + 0.5D), Block.COLLIDER, Fluid.NONE, this.entity));
         BlockPos pos = clip.m_82425_();
         return pos.equals(in) || this.entity.f_19853_.m_46859_(pos);
      }
   }

   static class BatTargetEntitiesGoal<T extends LivingEntity> extends NonTameRandomTargetGoal<T> {
      private final GiantBatEntity entity;

      public BatTargetEntitiesGoal(GiantBatEntity mob, Class<T> pClass, boolean pMustSee, @Nullable Predicate<LivingEntity> pTargetPredicate) {
         super(mob, pClass, pMustSee, pTargetPredicate);
         this.entity = mob;
      }

      protected AABB m_7255_(double distance) {
         if (this.entity.isHanging()) {
            AABB aabb = this.entity.m_20191_();
            double newDistance = distance / 8.0D;
            return new AABB(aabb.f_82288_ - newDistance, (double)(this.entity.f_19853_.m_141937_() - 5), aabb.f_82290_ - newDistance, aabb.f_82291_ + newDistance, aabb.f_82292_ + 1.0D, aabb.f_82293_ + newDistance);
         } else {
            return this.entity.m_20191_().m_82377_(distance, distance, distance);
         }
      }
   }

   class BatMoveControl extends MoveControl {
      private final Mob parentEntity = GiantBatEntity.this;

      public BatMoveControl() {
         super(GiantBatEntity.this);
      }

      public void m_8126_() {
         if (this.f_24981_ == Operation.MOVE_TO) {
            Vec3 vector3d = new Vec3(this.f_24975_ - this.parentEntity.m_20185_(), this.f_24976_ - this.parentEntity.m_20186_(), this.f_24977_ - this.parentEntity.m_20189_());
            double d0 = vector3d.m_82553_();
            double width = this.parentEntity.m_20191_().m_82309_();
            Vec3 vector3d1 = vector3d.m_82490_(this.f_24978_ * 0.05D / d0);
            this.parentEntity.m_20256_(this.parentEntity.m_20184_().m_82549_(vector3d1).m_82490_(0.95D).m_82520_(0.0D, -0.01D, 0.0D));
            if (d0 < width) {
               this.f_24981_ = Operation.WAIT;
            } else if (d0 >= width) {
               float yaw = -((float)Mth.m_14136_(vector3d1.f_82479_, vector3d1.f_82481_)) * 57.295776F;
               this.parentEntity.m_146922_(Mth.m_14148_(this.parentEntity.m_146908_(), yaw, 8.0F));
            }
         }

      }
   }
}
