package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.api.entity.ai.CrossbowAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.UndergroundTargetingEntitiesGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.misc.ChaosEaterProjectile;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Nullable;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerBossEvent;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.BossEvent.BossBarColor;
import net.minecraft.world.BossEvent.BossBarOverlay;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.RangedBowAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;

public class OrcDisasterEntity extends OrcLordEntity {
   private final ServerBossEvent bossEvent;

   public OrcDisasterEntity(EntityType<? extends OrcDisasterEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.bossEvent = (ServerBossEvent)(new ServerBossEvent(this.m_5446_(), BossBarColor.RED, BossBarOverlay.NOTCHED_20)).m_7005_(true).m_7003_(true);
      this.f_21364_ = 8000;
      this.f_19793_ = 2.0F;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 20.0D).m_22268_(Attributes.f_22281_, 50.0D).m_22268_(Attributes.f_22276_, 700.0D).m_22268_(Attributes.f_22279_, 0.20000000298023224D).m_22268_(Attributes.f_22277_, 64.0D).m_22268_(Attributes.f_22278_, 1.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 3.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 4.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(3, new CrossbowAttackGoal(this, 1.2D, 10.0F));
      this.f_21345_.m_25352_(3, new RangedBowAttackGoal(this, 1.0D, 20, 15.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.SpearTypeAttackGoal(this, 1.0D, 20, 15.0F));
      this.f_21345_.m_25352_(3, new OrcDisasterEntity.OrcDisasterAttackGoal(this));
      this.f_21345_.m_25352_(4, new OrcDisasterEntity.SummonOrcKnightsGoal(this, 15, 400));
      this.f_21345_.m_25352_(5, new OrcLordEntity.RecoveryMagicGoal(this, 15, 100, 50.0F));
      this.f_21345_.m_25352_(6, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(7, new TensuraTamableEntity.WanderAroundPosGoal(this));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{OrcEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new UndergroundTargetingEntitiesGoal(this, LivingEntity.class, false, 8.0F, this::shouldAttack));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      if (this.m_8077_()) {
         this.bossEvent.m_6456_(this.m_5446_());
      }

   }

   public boolean m_7301_(MobEffectInstance instance) {
      return instance.m_19544_() == TensuraMobEffects.CORROSION.get() ? false : super.m_7301_(instance);
   }

   public boolean m_6673_(DamageSource source) {
      if (DamageSourceHelper.isCorrosion(source)) {
         return true;
      } else if (DamageSourceHelper.isPoison(source)) {
         return true;
      } else {
         return source == DamageSource.f_19315_ || source == DamageSource.f_19312_ || source == DamageSource.f_19310_ || source == DamageSource.f_19314_ || source == DamageSource.f_146703_ || source == DamageSource.f_19311_ || source == DamageSource.f_19325_ || super.m_6673_(source);
      }
   }

   public void m_6593_(@Nullable Component pName) {
      super.m_6593_(pName);
      this.bossEvent.m_6456_(this.m_5446_());
   }

   public void m_6457_(ServerPlayer pPlayer) {
      super.m_6457_(pPlayer);
      this.bossEvent.m_6543_(pPlayer);
   }

   public void m_6452_(ServerPlayer pPlayer) {
      super.m_6452_(pPlayer);
      this.bossEvent.m_6539_(pPlayer);
   }

   protected void m_8024_() {
      super.m_8024_();
      this.bossEvent.m_142711_(this.m_21223_() / this.m_21233_());
   }

   protected void healAndEat() {
      this.m_5634_(10.0F);
      this.selfRegen = 20;
      if (this.m_146895_() != null && this.getMiscAnimation() <= 1) {
         this.setMiscAnimation(7);
      } else if (this.getMiscAnimation() <= 1 && this.m_146895_() == null) {
         boolean shouldEat = this.m_21223_() < this.m_21233_() / 4.0F && (double)this.f_19796_.m_188501_() <= 0.2D || this.m_21223_() < this.m_21233_() / 8.0F;
         if (!shouldEat) {
            return;
         }

         List<OrcEntity> list = this.f_19853_.m_45976_(OrcEntity.class, this.m_20191_().m_82400_(4.0D));
         if (!list.isEmpty()) {
            Iterator var3 = list.iterator();

            while(var3.hasNext()) {
               OrcEntity sacrifice = (OrcEntity)var3.next();
               if (sacrifice != this) {
                  this.setMiscAnimation(7);
                  sacrifice.m_7998_(this, true);
                  return;
               }
            }
         }
      }

   }

   protected void miscAnimationHandler() {
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (this.m_6084_() && !this.f_19853_.f_46443_) {
            if (this.getMiscAnimation() == 3 && this.miscAnimationTicks == 15) {
               this.areaAttack(2.0F, 1.25F);
               TensuraParticleHelper.spawnServerGroundSlamParticle(this, 5, 5.0F);
               TensuraParticleHelper.spawnServerGroundSlamParticle(this, 5, 3.0F);
               TensuraParticleHelper.spawnServerGroundSlamParticle(this, 10, 3.5F);
               this.m_5496_(SoundEvents.f_215778_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
               if (this.f_19853_.m_46469_().m_46207_(GameRules.f_46132_)) {
                  SkillHelper.launchBlock(this, this.m_20182_(), 4, 1, 0.5F, 0.4F, (blockState) -> {
                     return this.m_217043_().m_188503_(3) != 1 ? false : blockState.m_204336_(TensuraTags.Blocks.EARTH_MANIPULATING);
                  }, (blockPos) -> {
                     return !blockPos.equals(this.m_20097_().m_7495_());
                  });
               }
            } else {
               LivingEntity passenger;
               LivingEntity target;
               if (this.getMiscAnimation() == 4) {
                  passenger = this.m_5448_();
                  if (this.miscAnimationTicks == 10 && this.m_146895_() == null) {
                     if (passenger != null && passenger.m_20270_(this) <= 5.0F && passenger != this) {
                        passenger.m_7998_(this, true);
                     }
                  } else if (this.miscAnimationTicks == 23) {
                     Entity var3 = this.m_146895_();
                     if (var3 instanceof LivingEntity) {
                        target = (LivingEntity)var3;
                        target.m_8127_();
                        target.m_6469_(DamageSource.m_19370_(this), (float)(this.m_21133_(Attributes.f_22281_) * 2.5D));
                        this.m_5496_(SoundEvents.f_11913_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
                     }
                  }
               } else if (this.getMiscAnimation() == 7) {
                  if (this.miscAnimationTicks == 15 && this.m_146895_() == null) {
                     target = this.m_5448_();
                     if (target != null && target.m_20270_(this) <= 5.0F && target != this) {
                        target.m_7998_(this, true);
                     }
                  } else if (this.miscAnimationTicks == 30) {
                     Entity var2 = this.m_146895_();
                     if (var2 instanceof LivingEntity) {
                        passenger = (LivingEntity)var2;
                        passenger.m_8127_();
                        float HP = passenger.m_21223_();
                        passenger.m_6469_(DamageSource.m_19370_(this).m_19380_(), (float)(this.m_21133_(Attributes.f_22281_) * 3.5D));
                        if (!passenger.m_6084_()) {
                           this.m_5634_(HP * 3.0F);
                        } else {
                           this.m_5634_(75.0F);
                        }

                        this.m_5496_(SoundEvents.f_215769_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
                     }
                  }
               } else if (this.getMiscAnimation() == 9 && this.miscAnimationTicks == 25) {
                  int amount = this.m_21223_() <= this.m_21233_() / 2.0F ? 6 : 4;
                  this.spawnChaosEater(this, amount, 1.2F);
                  TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.CHAOS_EATER_EFFECT.get());
                  this.m_5496_(SoundEvents.f_215769_, 10.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
               }
            }
         }

         if (this.miscAnimationTicks >= this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   private int getAnimationTick(int miscAnimation) {
      byte var10000;
      switch(miscAnimation) {
      case 2:
         var10000 = 15;
         break;
      case 3:
         var10000 = 20;
         break;
      case 4:
         var10000 = 30;
         break;
      case 5:
      case 6:
      case 7:
      case 9:
         var10000 = 35;
         break;
      case 8:
         var10000 = 25;
         break;
      default:
         var10000 = 11;
      }

      return var10000;
   }

   private void spawnChaosEater(LivingEntity entity, int amount, float distance) {
      int rot = 360 / amount;

      for(int i = 0; i < amount; ++i) {
         Vec3 offset = (new Vec3(0.0D, (double)distance, 0.0D)).m_82535_(((float)(rot * i) - (float)rot / 2.0F) * 0.017453292F).m_82496_(-entity.m_146909_() * 0.017453292F).m_82524_(-entity.m_146908_() * 0.017453292F);
         Vec3 offPos = entity.m_146892_().m_82549_(offset);
         ChaosEaterProjectile chaosEater = new ChaosEaterProjectile(entity.m_9236_(), entity);
         chaosEater.m_146884_(offPos);
         chaosEater.setUpStartPos(amount, i, distance);
         LivingEntity target = this.m_5448_();
         List<LivingEntity> list = this.getTargetList();
         if (i != 0 && !list.isEmpty()) {
            target = (LivingEntity)list.get(this.m_217043_().m_188503_(list.size()));
         }

         chaosEater.setTarget(target);
         chaosEater.setSpeed(1.0F);
         chaosEater.shootFromRot(entity.m_20154_());
         chaosEater.setLife(300);
         chaosEater.setDamage((float)entity.m_21133_(Attributes.f_22281_));
         chaosEater.setMobEffect(new MobEffectInstance((MobEffect)TensuraMobEffects.CORROSION.get(), 200, 3, false, false, false));
         chaosEater.setEffectRange(1.5F);
         chaosEater.setMpCost(200.0D);
         chaosEater.setSkill(SkillUtils.getSkillOrNull(this, (ManasSkill)UniqueSkills.STARVED.get()));
         entity.m_9236_().m_7967_(chaosEater);
      }

   }

   private List<LivingEntity> getTargetList() {
      AABB box = this.m_20191_().m_82400_(Math.min(50.0D, this.m_21133_(Attributes.f_22277_)));
      return this.m_9236_().m_6443_(LivingEntity.class, box, this::shouldAttack);
   }

   protected void m_6153_() {
      if (++this.f_20919_ >= 35) {
         this.m_142687_(RemovalReason.KILLED);
         this.m_5496_(SoundEvents.f_11913_, 10.0F, 1.0F);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.CHAOS_EATER_EFFECT.get());
         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123765_);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.CHAOS_EATER_EFFECT.get(), 2.0D);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123765_, 2.0D);
      }

   }

   public void m_6667_(DamageSource source) {
      super.m_6667_(source);
      if (!this.m_6084_()) {
         if (this.m_20089_() == Pose.DYING) {
            this.setMiscAnimation(10);
         }

      }
   }

   protected float m_21519_(EquipmentSlot pSlot) {
      if (this.m_21824_()) {
         return 0.0F;
      } else {
         return pSlot.equals(EquipmentSlot.MAINHAND) ? 1.0F : super.m_21519_(pSlot);
      }
   }

   protected void dropBlood() {
      int amount = this.m_217043_().m_216339_(1, 4);

      for(int i = 0; i < amount; ++i) {
         this.m_19998_((ItemLike)TensuraMobDropItems.ROYAL_BLOOD.get());
      }

   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (event.isMoving()) {
         if (this.m_20096_() && this.m_21660_()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_disaster.run", EDefaultLoopTypes.LOOP));
         } else if (this.m_20096_() || !this.isInFluidType()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_disaster.walk", EDefaultLoopTypes.LOOP));
         }
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_disaster.idle", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (this.getMiscAnimation() == 10) {
         event.getController().markNeedsReload();
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_disaster.cry", EDefaultLoopTypes.PLAY_ONCE));
         return PlayState.CONTINUE;
      } else {
         if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
            event.getController().markNeedsReload();
            switch(this.getMiscAnimation()) {
            case 1:
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_disaster.punch", EDefaultLoopTypes.PLAY_ONCE));
               break;
            case 2:
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_disaster.cleaver_swing", EDefaultLoopTypes.PLAY_ONCE));
               break;
            case 3:
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_disaster.slam", EDefaultLoopTypes.PLAY_ONCE));
               break;
            case 4:
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_disaster.crush", EDefaultLoopTypes.PLAY_ONCE));
               break;
            case 5:
            case 9:
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_disaster.yell", EDefaultLoopTypes.PLAY_ONCE));
               break;
            case 6:
            case 7:
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_disaster.eat", EDefaultLoopTypes.PLAY_ONCE));
               break;
            case 8:
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_disaster.recover", EDefaultLoopTypes.PLAY_ONCE));
            }
         }

         return PlayState.CONTINUE;
      }
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   public class OrcDisasterAttackGoal extends HumanoidNPCEntity.NPCMeleeAttackGoal {
      public final OrcDisasterEntity orc;

      public OrcDisasterAttackGoal(OrcDisasterEntity beast) {
         super(beast, 2.0D, true);
         this.orc = beast;
      }

      public void m_8037_() {
         if (this.orc.getMiscAnimation() <= 2) {
            super.m_8037_();
         }
      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double d0 = this.m_6639_(pEnemy);
         if (this.orc.getMiscAnimation() == 0 || this.orc.getMiscAnimation() == 1) {
            int randomAttack = this.randomAttack(pEnemy, pDistToEnemySqr);
            double var10000;
            switch(randomAttack) {
            case 2:
               var10000 = d0 + 4.0D;
               break;
            case 3:
               var10000 = d0 + 16.0D;
               break;
            case 4:
               var10000 = d0 + 9.0D;
               break;
            case 5:
            case 6:
            case 7:
            case 8:
            default:
               var10000 = d0;
               break;
            case 9:
               var10000 = 2500.0D;
            }

            double attackRange = var10000;
            if (pDistToEnemySqr <= attackRange && this.m_25564_()) {
               this.m_25563_();
               this.orc.setMiscAnimation(randomAttack);
               if (randomAttack == 3 || randomAttack == 7) {
                  this.orc.m_21573_().m_26573_();
               }

               if (randomAttack == 1 || randomAttack == 2) {
                  this.orc.m_7327_(pEnemy);
               }
            }
         }

      }

      protected int randomAttack(LivingEntity target, double distSqr) {
         if ((double)this.orc.f_19796_.m_188501_() <= 0.2D && distSqr <= 20.0D && !target.m_20159_() && !target.m_20160_() && target != this.orc) {
            return target.m_21223_() <= target.m_21233_() / 5.0F ? 7 : 4;
         } else if ((double)this.orc.f_19796_.m_188501_() <= 0.2D && distSqr <= 2500.0D && (distSqr >= 10.0D || target.m_20186_() >= this.orc.m_20186_() + 6.0D) && this.orc.getStarved() != null) {
            return 9;
         } else if ((double)this.orc.f_19796_.m_188501_() <= 0.3D) {
            return 3;
         } else {
            return this.orc.m_6844_(EquipmentSlot.MAINHAND).m_41619_() ? 1 : 2;
         }
      }

      protected double m_6639_(LivingEntity pAttackTarget) {
         double attackRange = this.orc.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get());
         return super.m_6639_(pAttackTarget) + attackRange * attackRange;
      }
   }

   public static class SummonOrcKnightsGoal extends OrcLordEntity.SummonOrcsGoal {
      private final OrcLordEntity orcLord;

      public SummonOrcKnightsGoal(OrcLordEntity lord, int castWarmUpTime, int castingInterval) {
         super(lord, castWarmUpTime, castingInterval);
         this.orcLord = lord;
      }

      protected void performSpellCasting() {
         if (!this.summonAtSpawn) {
            this.summonAtSpawn = true;
         }

         this.summonOrcRandomPos(5, 0, 10);
      }

      protected void makeKnight(OrcEntity orc) {
         ItemStack helmet = new ItemStack(Items.f_42480_);
         ItemStack chest = new ItemStack(Items.f_42481_);
         ItemStack leggings = new ItemStack(Items.f_42482_);
         ItemStack boots = new ItemStack(Items.f_42483_);
         ItemStack weapon = new ItemStack((ItemLike)(this.orcLord.m_217043_().m_188499_() ? (ItemLike)TensuraToolItems.IRON_SPEAR.get() : Items.f_42386_));
         if ((double)this.orcLord.m_217043_().m_188501_() <= 0.3D) {
            if (this.orcLord.m_217043_().m_188499_()) {
               weapon = new ItemStack((ItemLike)TensuraToolItems.WAR_BOW.get());
            } else {
               weapon = new ItemStack(Items.f_42717_);
            }
         }

         orc.m_8061_(EquipmentSlot.HEAD, helmet);
         orc.m_8061_(EquipmentSlot.CHEST, chest);
         orc.m_8061_(EquipmentSlot.LEGS, leggings);
         orc.m_8061_(EquipmentSlot.FEET, boots);
         orc.m_8061_(EquipmentSlot.MAINHAND, weapon);
         if ((double)this.orcLord.m_217043_().m_188501_() <= 0.2D) {
            orc.m_8061_(EquipmentSlot.OFFHAND, new ItemStack(Items.f_42740_));
         }

      }
   }
}
