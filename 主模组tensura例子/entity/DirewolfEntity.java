package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.skill.extra.HakiSkill;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.IRanking;
import com.github.manasmods.tensura.api.entity.subclass.ITensuraMount;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.lightning.BlackLightningBolt;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.entity.variant.DirewolfVariant;
import com.github.manasmods.tensura.event.NamingEvent;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.network.play2server.RequestNamingGUIPacket;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import com.github.manasmods.tensura.util.TensuraAdvancementsHelper;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.github.manasmods.tensura.util.damage.TensuraEntityDamageSource;
import java.util.Comparator;
import java.util.EnumSet;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Holder;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.core.particles.SimpleParticleType;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.tags.BiomeTags;
import net.minecraft.util.Mth;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.MenuProvider;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.HasCustomInventoryScreen;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.PlayerRideableJumping;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.AgeableMob.AgeableMobGroupData;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.ai.targeting.TargetingConditions;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ChestMenu;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.biome.Biome;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeHooks;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.common.Tags.Biomes;
import net.minecraftforge.common.Tags.Items;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class DirewolfEntity extends TensuraTamableEntity implements IAnimatable, IRanking, ITensuraMount, HasCustomInventoryScreen, PlayerRideableJumping {
   private static final EntityDataAccessor<Integer> DATA_ID_TYPE_VARIANT;
   private static final EntityDataAccessor<Integer> EVOLUTION_STATE;
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Integer> LEADER_TICK;
   private static final EntityDataAccessor<Boolean> INTERESTED;
   private static final EntityDataAccessor<Boolean> CHESTED;
   private static final EntityDataAccessor<Boolean> LEADER;
   private static final EntityDataAccessor<Boolean> ALPHA;
   private static final EntityDataAccessor<Boolean> STAR;
   private static final EntityDataAccessor<Float> MAX_SIZE;
   private static final EntityDataAccessor<Float> SIZE;
   protected static final EntityDataAccessor<Optional<UUID>> LEADER_UUID;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);
   public int miscAnimationTicks = 0;
   public SimpleContainer inventory;
   public MenuProvider inventoryMenu;
   private boolean hasChestVarChanged = false;
   protected boolean playerJumping;
   protected float playerJumpPendingScale;

   public DirewolfEntity(EntityType<? extends DirewolfEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21364_ = 10;
      this.f_19793_ = 2.0F;
      this.initInventory();
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22281_, 6.0D).m_22268_(Attributes.f_22276_, 35.0D).m_22268_(Attributes.f_22279_, 0.20000000298023224D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22284_, 0.0D).m_22268_(Attributes.f_22278_, 0.20000000298023224D).m_22268_(Attributes.f_22288_, 1.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 3.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(3, new DirewolfEntity.DirewolfAttackGoal());
      this.f_21345_.m_25352_(4, new DirewolfEntity.FollowLeaderGoal(this, 1.5D));
      this.f_21345_.m_25352_(5, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(6, new BreedGoal(this, 1.2D, DirewolfEntity.class));
      this.f_21345_.m_25352_(7, new TamableFollowParentGoal(this, 1.0D));
      this.f_21345_.m_25352_(8, new TensuraTamableEntity.WanderAroundPosGoal(this));
      this.f_21345_.m_25352_(9, new DirewolfEntity.TiltingHeadGoal(this, 8.0F));
      this.f_21345_.m_25352_(10, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(11, new LookAtPlayerGoal(this, Player.class, 8.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(5, new NonTameRandomTargetGoal(this, Player.class, false, (Predicate)null));
      this.f_21346_.m_25352_(6, new NonTameRandomTargetGoal(this, Animal.class, false, (e) -> {
         return e.m_6095_().m_204039_(TensuraTags.EntityTypes.ANIMAL_PREY);
      }));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(DATA_ID_TYPE_VARIANT, 0);
      this.f_19804_.m_135372_(EVOLUTION_STATE, 0);
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(LEADER_TICK, 0);
      this.f_19804_.m_135372_(INTERESTED, Boolean.FALSE);
      this.f_19804_.m_135372_(CHESTED, Boolean.FALSE);
      this.f_19804_.m_135372_(LEADER, Boolean.FALSE);
      this.f_19804_.m_135372_(ALPHA, Boolean.FALSE);
      this.f_19804_.m_135372_(STAR, Boolean.FALSE);
      this.f_19804_.m_135372_(MAX_SIZE, 1.0F);
      this.f_19804_.m_135372_(SIZE, 1.0F);
      this.f_19804_.m_135372_(LEADER_UUID, Optional.empty());
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("Variant", this.getTypeVariant());
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128405_("EvoState", this.getCurrentEvolutionState());
      compound.m_128405_("LeaderTick", this.getLeaderTick());
      if (this.getLeaderUUID() != null) {
         compound.m_128362_("Leader", this.getLeaderUUID());
      }

      compound.m_128379_("Chested", this.isChested());
      compound.m_128379_("Leader", this.isLeader());
      compound.m_128379_("Alpha", this.isAlpha());
      compound.m_128379_("Star", this.isStar());
      compound.m_128350_("MaxSize", this.getMaxSize());
      compound.m_128350_("Size", this.getSize());
      if (this.inventory != null) {
         ListTag listTag = new ListTag();

         for(int i = 0; i < this.inventory.m_6643_(); ++i) {
            ItemStack itemstack = this.inventory.m_8020_(i);
            if (!itemstack.m_41619_()) {
               CompoundTag CompoundNBT = new CompoundTag();
               CompoundNBT.m_128344_("Slot", (byte)i);
               itemstack.m_41739_(CompoundNBT);
               listTag.add(CompoundNBT);
            }
         }

         compound.m_128365_("Items", listTag);
      }

   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.f_19804_.m_135381_(DATA_ID_TYPE_VARIANT, compound.m_128451_("Variant"));
      this.setCurrentEvolutionState(compound.m_128451_("EvoState"));
      this.setLeaderTick(compound.m_128451_("LeaderTick"));
      if (compound.m_128403_("Leader")) {
         this.setLeaderUUID(compound.m_128342_("Leader"));
      }

      this.setChested(compound.m_128471_("Chested"));
      this.setLeader(compound.m_128471_("Leader"));
      this.setAlpha(compound.m_128471_("Alpha"));
      this.setStar(compound.m_128471_("Star"));
      this.setMaxSize(compound.m_128457_("MaxSize"));
      this.setSize(compound.m_128457_("Size"));
      ListTag listTag;
      int i;
      CompoundTag CompoundNBT;
      int j;
      if (this.inventory != null) {
         listTag = compound.m_128437_("Items", 10);
         this.initInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.inventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      } else {
         listTag = compound.m_128437_("Items", 10);
         this.initInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.initInventory();
            this.inventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      }

   }

   public DirewolfVariant getVariant() {
      return DirewolfVariant.byId(this.getTypeVariant() & 255);
   }

   private int getTypeVariant() {
      return (Integer)this.f_19804_.m_135370_(DATA_ID_TYPE_VARIANT);
   }

   public void setVariant(DirewolfVariant variant) {
      this.f_19804_.m_135381_(DATA_ID_TYPE_VARIANT, variant.getId() & 255);
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public int getLeaderTick() {
      return (Integer)this.f_19804_.m_135370_(LEADER_TICK);
   }

   public void setLeaderTick(int tick) {
      this.f_19804_.m_135381_(LEADER_TICK, tick);
   }

   public boolean isInterested() {
      return (Boolean)this.f_19804_.m_135370_(INTERESTED);
   }

   public void setInterested(boolean pInterested) {
      this.f_19804_.m_135381_(INTERESTED, pInterested);
   }

   public boolean isChested() {
      return (Boolean)this.f_19804_.m_135370_(CHESTED);
   }

   public void setChested(boolean chested) {
      this.f_19804_.m_135381_(CHESTED, chested);
      this.hasChestVarChanged = true;
   }

   public boolean isLeader() {
      return (Boolean)this.f_19804_.m_135370_(LEADER);
   }

   public void setLeader(boolean alpha) {
      this.f_19804_.m_135381_(LEADER, alpha);
   }

   public boolean isAlpha() {
      return (Boolean)this.f_19804_.m_135370_(ALPHA);
   }

   public void setAlpha(boolean alpha) {
      this.f_19804_.m_135381_(ALPHA, alpha);
   }

   public boolean isStar() {
      return (Boolean)this.f_19804_.m_135370_(STAR);
   }

   public void setStar(boolean star) {
      this.f_19804_.m_135381_(STAR, star);
   }

   public float getMaxSize() {
      return (Float)this.f_19804_.m_135370_(MAX_SIZE);
   }

   public void setMaxSize(float size) {
      this.f_19804_.m_135381_(MAX_SIZE, size);
   }

   public float getSize() {
      return (Float)this.f_19804_.m_135370_(SIZE);
   }

   public void setSize(float size) {
      this.f_19804_.m_135381_(SIZE, size);
   }

   @Nullable
   public UUID getLeaderUUID() {
      return (UUID)((Optional)this.f_19804_.m_135370_(LEADER_UUID)).orElse((Object)null);
   }

   public void setLeaderUUID(@Nullable UUID pUuid) {
      this.f_19804_.m_135381_(LEADER_UUID, Optional.ofNullable(pUuid));
   }

   public void m_7350_(EntityDataAccessor<?> pKey) {
      if (SIZE.equals(pKey)) {
         this.m_6210_();
      }

      super.m_7350_(pKey);
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions entitydimensions = super.m_6972_(pPose);
      return entitydimensions.m_20388_(this.getSize());
   }

   public boolean canSleep() {
      return true;
   }

   public double m_20204_() {
      return super.m_20204_() + (double)(this.m_20206_() / 2.0F);
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19314_ || source == DamageSource.f_19325_ || super.m_6673_(source);
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      if (!(pFallDistance < 8.0F) && this.getVariant() != DirewolfVariant.TEMPEST_STAR_WOLF) {
         int i = this.m_5639_(pFallDistance - 16.0F, pMultiplier);
         if (i <= 0) {
            return false;
         } else {
            this.m_6469_(pSource, (float)i);
            this.m_21229_();
            return true;
         }
      } else {
         return false;
      }
   }

   public boolean m_7307_(Entity entity) {
      if (super.m_7307_(entity)) {
         return true;
      } else if (entity instanceof DirewolfEntity) {
         DirewolfEntity wolf = (DirewolfEntity)entity;
         return wolf.m_21824_() == this.m_21824_();
      } else {
         return false;
      }
   }

   public void m_7334_(Entity pEntity) {
      if (pEntity instanceof DirewolfEntity) {
         DirewolfEntity wolf = (DirewolfEntity)pEntity;
         if (wolf.getLeaderUUID() == this.m_20148_() || wolf.m_20148_() == this.getLeaderUUID()) {
            return;
         }
      }

      super.m_7334_(pEntity);
   }

   public boolean canBeNamed() {
      return !this.isAlpha();
   }

   public int getMaxEvolutionState() {
      return 3;
   }

   public int getCurrentEvolutionState() {
      return (Integer)this.f_19804_.m_135370_(EVOLUTION_STATE);
   }

   public void setCurrentEvolutionState(int state) {
      this.f_19804_.m_135381_(EVOLUTION_STATE, state);
   }

   public void onNamed(NamingEvent event) {
      if (this.isLeader()) {
         Player owner = event.getNamer();
         if (owner != null) {
            AtomicInteger namedPack = new AtomicInteger(1);
            List<DirewolfEntity> list = this.f_19853_.m_6443_(DirewolfEntity.class, this.m_20191_().m_82377_(32.0D, 32.0D, 32.0D), (entity) -> {
               return entity.m_6084_() && entity != this && entity.getLeaderUUID() == this.m_20148_();
            });
            Iterator var5 = list.iterator();

            while(true) {
               DirewolfEntity wolf;
               do {
                  if (!var5.hasNext()) {
                     event.setCalculatedCost(event.getOriginalCost() * (double)namedPack.get());
                     return;
                  }

                  wolf = (DirewolfEntity)var5.next();
               } while(wolf.m_21824_() && !this.m_21824_());

               wolf.m_21573_().m_26573_();
               wolf.m_21661_();
               if (!wolf.m_21824_()) {
                  wolf.m_21828_(owner);
                  wolf.m_21839_(true);
                  wolf.m_9236_().m_7605_(wolf, (byte)7);
               }

               TensuraEPCapability.getFrom(wolf).ifPresent((namingCap) -> {
                  if (namingCap.getName() == null) {
                     UUID uuid = TensuraEPCapability.getPermanentOwner(wolf);
                     if (uuid == null || uuid.equals(owner.m_20148_())) {
                        namedPack.getAndIncrement();
                        namingCap.setName(String.valueOf(wolf.m_7755_()));
                        namingCap.setPermanentOwner(owner.m_20148_());
                        owner.f_19853_.m_6263_((Player)null, wolf.m_20185_(), wolf.m_20186_(), wolf.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
                        wolf.m_7292_(new MobEffectInstance(MobEffects.f_19619_, 40, 0, false, false, false));
                        TensuraParticleHelper.addServerParticlesAroundSelf(wolf, ParticleTypes.f_123767_, 2.0D);
                        TensuraParticleHelper.addServerParticlesAroundSelf(wolf, ParticleTypes.f_123747_, 1.0D);
                        RequestNamingGUIPacket.namingReward(wolf, Math.min(1000000.0D, event.getOriginalCost()), event.getType());
                        wolf.m_5634_(wolf.m_21233_());
                        TensuraEPCapability.sync(wolf);
                     }
                  }
               });
            }
         }
      }
   }

   public void evolve() {
      int current = this.getCurrentEvolutionState();
      this.setSize(this.getSize() + 0.5F);
      this.setMaxSize(this.getMaxSize() + 0.5F);
      if (current < this.getMaxEvolutionState()) {
         this.setCurrentEvolutionState(current + 1);
         this.evolveBuff();
         if (this.getVariant() == DirewolfVariant.DIREWOLF) {
            this.setVariant(this.getEvolutionByBiomes());
         } else if (this.getVariant().getId() >= 14) {
            this.setVariant(DirewolfVariant.TEMPEST_STAR_WOLF);
            if (this.isStar()) {
               SkillStorage storage = SkillAPI.getSkillsFrom(this);
               storage.learnSkill((ManasSkill)ExtraSkills.BLACK_LIGHTNING.get());
               ManasSkillInstance instance = new ManasSkillInstance((ManasSkill)ExtraSkills.ULTRA_INSTINCT.get());
               instance.setToggled(true);
               storage.learnSkill(instance);
            }
         } else if (this.getVariant().getId() % 2 != 0) {
            this.setVariant(DirewolfVariant.byId(this.getVariant().getId() + 1));
         }

         if (this.getVariant().getId() >= 14) {
            SkillAPI.getSkillsFrom(this).learnSkill((ManasSkill)ExtraSkills.SHADOW_MOTION.get());
         }
      }

   }

   private void evolveBuff() {
      this.gainMaxHealth(this, 10.0D);
      this.gainAttackDamage(this, 12.0D);
      this.gainMovementSpeed(this, 0.05D);
      this.gainSwimSpeed(this, 1.0D);
      this.gainJumpStrength(this, 0.5D);
   }

   private DirewolfVariant getEvolutionByBiomes() {
      if (this.isStar()) {
         return DirewolfVariant.STAR_WOLF;
      } else {
         Holder<Biome> biomes = this.f_19853_.m_204166_(this.m_20097_());
         if (!biomes.m_203656_(Biomes.IS_DRY_OVERWORLD) && !biomes.m_203656_(Biomes.IS_DRY_NETHER)) {
            if (!biomes.m_203656_(BiomeTags.f_207603_) && !biomes.m_203656_(BiomeTags.f_207604_) && !biomes.m_203656_(Biomes.IS_SWAMP)) {
               if (!biomes.m_203656_(BiomeTags.f_207611_) && !biomes.m_203656_(BiomeTags.f_207610_)) {
                  if (!biomes.m_203656_(Biomes.IS_CAVE) && !biomes.m_203656_(Biomes.IS_SLOPE)) {
                     return !biomes.m_203656_(BiomeTags.f_215818_) && !biomes.m_203656_(Biomes.IS_PEAK) ? DirewolfVariant.BLACK_FANG : DirewolfVariant.PURPLE_FANG;
                  } else {
                     return DirewolfVariant.BROWN_FANG;
                  }
               } else {
                  return DirewolfVariant.GREEN_FANG;
               }
            } else {
               return DirewolfVariant.BLUE_FANG;
            }
         } else {
            return DirewolfVariant.RED_FANG;
         }
      }
   }

   @Nullable
   private DamageSource getAdditionalDamageSource() {
      DamageSource var10000;
      switch(this.getVariant()) {
      case DIREWOLF:
         var10000 = null;
         break;
      case RED_FANG:
         var10000 = TensuraDamageSources.elementalAttack("tensura.fire_attack", this, true);
         break;
      case GREEN_FANG:
         var10000 = TensuraDamageSources.elementalAttack("tensura.wind_attack", this, true);
         break;
      case BLUE_FANG:
         var10000 = TensuraDamageSources.elementalAttack("tensura.water_attack", this, true);
         break;
      case BROWN_FANG:
         var10000 = TensuraDamageSources.elementalAttack("tensura.earth_attack", this, true);
         break;
      case PURPLE_FANG:
         var10000 = TensuraDamageSources.elementalAttack("tensura.space_attack", this, true);
         break;
      default:
         var10000 = TensuraDamageSources.genericMagic(this);
      }

      return var10000;
   }

   public boolean m_7327_(Entity pEntity) {
      float damage = (float)this.m_21133_(Attributes.f_22281_);
      float knockBack = (float)this.m_21133_(Attributes.f_22282_);
      if (pEntity instanceof LivingEntity) {
         LivingEntity entity = (LivingEntity)pEntity;
         damage += EnchantmentHelper.m_44833_(this.m_21205_(), entity.m_6336_());
         knockBack += (float)EnchantmentHelper.m_44894_(this);
      }

      int i = EnchantmentHelper.m_44914_(this);
      if (i > 0) {
         pEntity.m_20254_(i * 4);
      }

      DamageSource source = this.getAdditionalDamageSource();
      boolean flag;
      if (source == null) {
         flag = pEntity.m_6469_(DamageSource.m_19370_(this), damage);
      } else {
         flag = DamageSourceHelper.dealSplitDamage(pEntity, DamageSource.m_19370_(this), 0.5F, source, damage);
      }

      if (flag) {
         if (knockBack > 0.0F && pEntity instanceof LivingEntity) {
            LivingEntity entity = (LivingEntity)pEntity;
            entity.m_147240_((double)(knockBack * 0.5F), (double)Mth.m_14031_(this.m_146908_() * 0.017453292F), (double)(-Mth.m_14089_(this.m_146908_() * 0.017453292F)));
            this.m_20256_(this.m_20184_().m_82542_(0.6D, 1.0D, 0.6D));
         }

         this.m_19970_(this, pEntity);
         this.m_21335_(pEntity);
         if (this.getMiscAnimation() == 0) {
            this.setMiscAnimation(2);
            if (!pEntity.m_6084_()) {
               this.m_8035_();
            }
         }
      }

      return flag;
   }

   @Nullable
   private ManasSkillInstance getBlackLightning() {
      Optional<ManasSkillInstance> skill = SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)ExtraSkills.BLACK_LIGHTNING.get());
      if (skill.isEmpty()) {
         return null;
      } else {
         return !((ManasSkillInstance)skill.get()).canInteractSkill(this) ? null : (ManasSkillInstance)skill.get();
      }
   }

   @Nullable
   private ManasSkillInstance getShadowMotion() {
      Optional<ManasSkillInstance> skill = SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)ExtraSkills.SHADOW_MOTION.get());
      if (skill.isEmpty()) {
         return null;
      } else {
         return !((ManasSkillInstance)skill.get()).canInteractSkill(this) ? null : (ManasSkillInstance)skill.get();
      }
   }

   protected void sleepHandler() {
      if (!this.m_9236_().m_5776_()) {
         if (this.m_5803_() && (this.m_5448_() != null || this.m_27593_() || this.isInFluidType() || this.m_20160_() || this.m_20159_() || ++this.sleepingTime > this.maxSleepTime && this.f_19853_.m_46461_() || this.shouldFollowOwner())) {
            this.setSleeping(false);
         }

         if (this.f_19853_.m_46462_() && this.f_19853_.m_46941_() == 0) {
            if (this.m_5803_()) {
               this.setSleeping(false);
            }

            if (this.f_19853_.m_46468_() >= 17000L && this.f_19853_.m_46468_() <= 19000L && this.f_19796_.m_188503_(200) == 1) {
               this.setMiscAnimation(4);
            }

         } else {
            if (this.m_5448_() == null && this.f_19853_.m_46462_() && !this.shouldFollowOwner() && !this.m_5803_() && !this.isInFluidType() && !this.m_20160_() && !this.m_20159_() && this.f_19796_.m_188503_(100) == 0) {
               if (this.m_217043_().m_188499_()) {
                  this.setSleeping(true);
               } else {
                  this.sleepingTime = 0;
                  this.maxSleepTime = 100 + this.f_19796_.m_188503_(550);
               }
            }

         }
      }
   }

   public void m_8119_() {
      super.m_8119_();
      this.targetingMovementHelper();
      if (this.getSize() <= 0.0F) {
         this.setSize(1.0F);
      }

      if (this.isLeader()) {
         this.setLeaderTick(this.getLeaderTick() - 1);
         if (this.getLeaderTick() <= 0) {
            this.setLeader(false);
         }
      }

      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         if (this.getMiscAnimation() == 3 && this.miscAnimationTicks == 5) {
            this.coercion(this.m_146892_().m_82549_(this.m_20154_().m_82490_(15.0D)));
         } else {
            LivingEntity target;
            if (this.getMiscAnimation() == 4) {
               if (this.miscAnimationTicks == 30) {
                  target = this.m_5448_();
                  if (target != null) {
                     this.blackLightning(target.m_20182_());
                  }
               } else if (this.miscAnimationTicks == 1) {
                  this.m_5496_(SoundEvents.f_12620_, 3.0F, 1.0F);
               }
            } else if (this.getMiscAnimation() == 5) {
               if (this.miscAnimationTicks % 3 == 0) {
                  TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123765_);
               }

               if (this.miscAnimationTicks >= 5 && this.miscAnimationTicks <= 20) {
                  this.m_21573_().m_26573_();
               } else if (this.miscAnimationTicks == 30) {
                  target = this.m_5448_();
                  if (target != null && target.m_20270_(this) < 5.0F) {
                     this.m_7327_(target);
                  }
               }
            }
         }

         if (this.miscAnimationTicks >= this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

      if (this.hasChestVarChanged && this.inventory != null && !this.isChested()) {
         for(int i = 3; i < 18; ++i) {
            if (!this.inventory.m_8020_(i).m_41619_()) {
               if (!this.f_19853_.f_46443_) {
                  this.m_5552_(this.inventory.m_8020_(i), 1.0F);
               }

               this.inventory.m_8016_(i);
            }
         }

         this.hasChestVarChanged = false;
      }

   }

   private int getAnimationTick(int miscAnimation) {
      byte var10000;
      switch(miscAnimation) {
      case 2:
         var10000 = 5;
         break;
      case 3:
         var10000 = 10;
         break;
      case 4:
         var10000 = 60;
         break;
      case 5:
         var10000 = 40;
         break;
      default:
         var10000 = 20;
      }

      return var10000;
   }

   private void coercion(Vec3 targetPos) {
      Level level = this.m_9236_();
      if (!level.m_5776_()) {
         Vec3 source = this.m_146892_().m_82549_(this.m_20154_().m_82490_(2.0D));
         Vec3 sourceToTarget = targetPos.m_82546_(source);
         Vec3 normalizes = sourceToTarget.m_82541_();
         level.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_215771_, SoundSource.PLAYERS, 5.0F, 1.0F);

         for(int particleIndex = 1; particleIndex < Mth.m_14107_(sourceToTarget.m_82553_()); ++particleIndex) {
            Vec3 particlePos = source.m_82549_(normalizes.m_82490_((double)particleIndex));
            ((ServerLevel)level).m_8767_((SimpleParticleType)TensuraParticles.SONIC_SOUND.get(), particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_, 1, 0.0D, 0.0D, 0.0D, 0.0D);
            AABB aabb = (new AABB(new BlockPos(particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_))).m_82400_(4.0D);
            List<LivingEntity> list = level.m_6443_(LivingEntity.class, aabb, (targetx) -> {
               return !targetx.m_7306_(this) && !this.m_7307_(targetx);
            });
            if (!list.isEmpty()) {
               double ownerEP = TensuraEPCapability.getEP(this);
               LivingEntity owner = this.getControllingPassenger() != null ? this.getControllingPassenger() : this;
               Iterator var13 = list.iterator();

               while(var13.hasNext()) {
                  LivingEntity target = (LivingEntity)var13.next();
                  if (!this.m_20363_(target)) {
                     DamageSource damagesource = (new TensuraEntityDamageSource("sonic_boom", this)).setMpCost(100.0D).m_19380_().m_238403_();
                     target.m_6469_(damagesource, (float)this.m_21133_(Attributes.f_22281_) * 0.25F);
                     double targetEP = TensuraEPCapability.getEP(target);
                     double difference = ownerEP / targetEP;
                     if (!(difference <= 2.0D)) {
                        int fearLevel = (int)(difference * 0.25D - 0.5D);
                        fearLevel = Math.min(fearLevel, (Integer)TensuraConfig.INSTANCE.mobEffectConfig.maxFear.get());
                        SkillHelper.checkThenAddEffectSource(target, (Entity)owner, (MobEffect)TensuraMobEffects.FEAR.get(), 200, fearLevel);
                        target.m_147207_(new MobEffectInstance(MobEffects.f_19613_, 200, fearLevel, false, false), this);
                        HakiSkill.hakiPush(target, this, fearLevel);
                     }
                  }
               }
            }
         }

      }
   }

   private void blackLightning(Vec3 postion) {
      ManasSkillInstance instance = this.getBlackLightning();
      if (instance != null) {
         BlackLightningBolt bolt = new BlackLightningBolt(this.f_19853_, this);
         bolt.setSkill(instance);
         bolt.setMpCost(500.0D);
         bolt.setRadius(3.0F);
         bolt.setTensuraDamage(50.0F);
         bolt.setAdditionalVisual(4);
         bolt.m_146884_(postion);
         this.f_19853_.m_7967_(bolt);
      }
   }

   private void initInventory() {
      SimpleContainer chest = this.inventory;
      this.inventory = new SimpleContainer(27) {
         public boolean m_6542_(Player player) {
            return DirewolfEntity.this.m_6084_() && !DirewolfEntity.this.f_19817_;
         }
      };
      if (chest != null) {
         int i = Math.min(chest.m_6643_(), this.inventory.m_6643_());

         for(int j = 0; j < i; ++j) {
            ItemStack itemstack = chest.m_8020_(j);
            if (!itemstack.m_41619_()) {
               this.inventory.m_6836_(j, itemstack.m_41777_());
            }
         }
      }

   }

   public void m_213583_(Player pPlayer) {
      if (this.isChested()) {
         if (this.inventory != null) {
            pPlayer.m_5893_(this.getMenu());
            if (!pPlayer.f_19853_.f_46443_) {
               this.m_146852_(GameEvent.f_157803_, pPlayer);
            }

         }
      }
   }

   public void m_6667_(DamageSource cause) {
      super.m_6667_(cause);
      if (!this.f_19853_.m_5776_()) {
         if (!this.m_6084_()) {
            if (this.inventory != null) {
               for(int i = 0; i < this.inventory.m_6643_(); ++i) {
                  ItemStack itemstack = this.inventory.m_8020_(i);
                  if (!itemstack.m_41619_()) {
                     this.m_5552_(itemstack, 0.0F);
                  }
               }
            }

            if (this.isAlpha() || this.isLeader()) {
               Entity var7 = cause.m_7639_();
               if (var7 instanceof Player) {
                  Player player = (Player)var7;
                  List<DirewolfEntity> list = this.f_19853_.m_6443_(DirewolfEntity.class, this.m_20191_().m_82377_(32.0D, 32.0D, 32.0D), (entity) -> {
                     return entity.m_6084_() && entity != this && entity.getLeaderUUID() == this.m_20148_();
                  });
                  Iterator var4 = list.iterator();

                  while(true) {
                     DirewolfEntity wolf;
                     do {
                        if (!var4.hasNext()) {
                           return;
                        }

                        wolf = (DirewolfEntity)var4.next();
                     } while(wolf.m_21824_() && !this.m_21824_());

                     TensuraEPCapability.getFrom(wolf).ifPresent((cap) -> {
                        wolf.f_19802_ = 10;
                        if (wolf.m_5448_() == player) {
                           wolf.m_21661_();
                        }

                        cap.addNeutralTarget(player.m_20148_());
                        TensuraEPCapability.sync(wolf);
                     });
                     if (this.isAlpha() && wolf.m_21023_((MobEffect)TensuraMobEffects.FEAR.get()) && !wolf.m_21824_()) {
                        wolf.m_21195_((MobEffect)TensuraMobEffects.FEAR.get());
                        wolf.m_21828_(player);
                        wolf.m_21573_().m_26573_();
                        wolf.m_21661_();
                        wolf.m_21839_(false);
                        wolf.m_9236_().m_7605_(wolf, (byte)7);
                        TensuraEPCapability.getFrom(wolf).ifPresent((namingCap) -> {
                           namingCap.setPermanentOwner(player.m_20148_());
                           TensuraEPCapability.sync(wolf);
                        });
                     }
                  }
               }
            }

         }
      }
   }

   public MenuProvider getMenu() {
      if (this.inventoryMenu == null) {
         this.inventoryMenu = new MenuProvider() {
            public AbstractContainerMenu m_7208_(int menu, Inventory inventory, Player player) {
               return new ChestMenu(MenuType.f_39959_, menu, inventory, DirewolfEntity.this.inventory, 3);
            }

            public Component m_5446_() {
               return Component.m_237115_("container.chest");
            }
         };
      }

      return this.inventoryMenu;
   }

   public boolean m_6898_(ItemStack pStack) {
      FoodProperties food = pStack.getFoodProperties(this);
      return food != null && food.m_38746_();
   }

   @Nullable
   private DirewolfVariant getVariantFromCore(ItemStack core) {
      if (this.getVariant() != DirewolfVariant.BLACK_FANG) {
         return null;
      } else if (core.m_150930_((Item)TensuraMaterialItems.ELEMENT_CORE_EARTH.get())) {
         return DirewolfVariant.BROWN_FANG;
      } else if (core.m_150930_((Item)TensuraMaterialItems.ELEMENT_CORE_FIRE.get())) {
         return DirewolfVariant.RED_FANG;
      } else if (core.m_150930_((Item)TensuraMaterialItems.ELEMENT_CORE_WIND.get())) {
         return DirewolfVariant.GREEN_FANG;
      } else if (core.m_150930_((Item)TensuraMaterialItems.ELEMENT_CORE_WATER.get())) {
         return DirewolfVariant.BLUE_FANG;
      } else {
         return core.m_150930_((Item)TensuraMaterialItems.ELEMENT_CORE_SPACE.get()) ? DirewolfVariant.PURPLE_FANG : null;
      }
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (this.f_19853_.f_46443_) {
            boolean flag = this.m_21830_(player) || this.m_21824_() || this.getVariantFromCore(itemstack) != null;
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         } else if (!this.m_21824_()) {
            return super.m_6071_(player, hand);
         } else {
            if (this.m_21830_(player)) {
               DirewolfVariant coreVariant = this.getVariantFromCore(itemstack);
               if (coreVariant != null) {
                  this.setVariant(coreVariant);
                  this.m_5496_(SoundEvents.f_11887_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_175830_);
                  if (!player.m_150110_().f_35937_) {
                     itemstack.m_41774_(1);
                  }

                  return InteractionResult.m_19078_(this.f_19853_.f_46443_);
               }

               if (!this.m_6162_()) {
                  if (!this.isChested() && itemstack.m_204117_(Items.CHESTS_WOODEN) && !this.m_6162_()) {
                     this.setChested(true);
                     this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                     if (!player.m_150110_().f_35937_) {
                        itemstack.m_41774_(1);
                     }

                     return InteractionResult.m_19078_(this.f_19853_.f_46443_);
                  }

                  if (this.isChested() && itemstack.m_150930_(net.minecraft.world.item.Items.f_42574_)) {
                     this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                     this.m_19998_(Blocks.f_50087_);

                     for(int i = 0; i < this.inventory.m_6643_(); ++i) {
                        this.m_19983_(this.inventory.m_8020_(i));
                     }

                     this.inventory.m_6211_();
                     this.setChested(false);
                     return InteractionResult.SUCCESS;
                  }
               }

               if (!player.m_36341_() && !this.m_6162_()) {
                  if (this.getControllingPassenger() == null) {
                     this.m_21839_(false);
                     this.setWandering(false);
                     if (this.m_20197_().size() > (int)((double)this.getSize() + 0.25D) - 1) {
                        Iterator var6 = this.m_20197_().iterator();

                        while(var6.hasNext()) {
                           Entity passenger = (Entity)var6.next();
                           if (passenger != this.getControllingPassenger()) {
                              passenger.m_19877_();
                              break;
                           }
                        }
                     }

                     if (player.m_7998_(this, false) && this.isStar() && this.getVariant().equals(DirewolfVariant.TEMPEST_STAR_WOLF) && player instanceof ServerPlayer) {
                        ServerPlayer serverPlayer = (ServerPlayer)player;
                        TensuraAdvancementsHelper.grant(serverPlayer, TensuraAdvancementsHelper.Advancements.GOOD_BOY);
                     }
                  } else if (this.isChested()) {
                     this.m_213583_(player);
                  }
               } else {
                  this.commanding(player);
               }
            } else if (this.m_7310_(player)) {
               player.m_7998_(this, false);
            }

            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }
      }
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_21824_() && itemstack.m_150930_(net.minecraft.world.item.Items.f_42500_)) {
         if (!player.m_7500_()) {
            itemstack.m_41774_(1);
         }

         this.m_8035_();
         this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
         return InteractionResult.SUCCESS;
      } else {
         if (this.m_6898_(itemstack)) {
            if (this.m_21223_() < this.m_21233_()) {
               if (!player.m_7500_()) {
                  itemstack.m_41774_(1);
               }

               this.m_8035_();
               this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
               return InteractionResult.SUCCESS;
            }

            int i = this.m_146764_();
            if (!this.f_19853_.m_5776_() && i == 0 && this.m_5957_()) {
               this.m_142075_(player, hand, itemstack);
               this.m_27595_(player);
               this.setMiscAnimation(1);
               return InteractionResult.SUCCESS;
            }

            if (this.m_6162_()) {
               this.m_142075_(player, hand, itemstack);
               this.m_146740_(m_216967_(-this.m_146764_()), true);
               this.m_9236_().m_6269_(player, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
               this.setMiscAnimation(1);
               return InteractionResult.m_19078_(this.f_19853_.f_46443_);
            }
         }

         return InteractionResult.PASS;
      }
   }

   public void m_8035_() {
      super.m_8035_();
      this.setMiscAnimation(1);
      this.m_5634_(3.0F);
   }

   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      if (!(pOtherParent instanceof DirewolfEntity)) {
         return null;
      } else {
         DirewolfEntity direwolf = (DirewolfEntity)pOtherParent;
         DirewolfEntity baby = (DirewolfEntity)((EntityType)TensuraEntityTypes.DIREWOLF.get()).m_20615_(pLevel);
         if (baby == null) {
            return null;
         } else {
            UUID uuid = this.m_21805_();
            if (uuid != null) {
               baby.m_21816_(uuid);
               baby.m_7105_(true);
            }

            if (this.getCurrentEvolutionState() >= 1 && direwolf.getCurrentEvolutionState() >= 1) {
               baby.evolve();
               baby.setCurrentEvolutionState(1);
               int tempestParent = this.getTypeVariant() >= 13 ? 1 : 0;
               if (direwolf.getTypeVariant() >= 13) {
                  ++tempestParent;
               }

               if ((double)pLevel.m_213780_().m_188501_() <= (Double)SpawnRateConfig.INSTANCE.tempestWolfPack.get() * (double)tempestParent / 100.0D) {
                  baby.setVariant(DirewolfVariant.TEMPEST_WOLF);
                  if (this.isStar() && SpawnRateConfig.rollChance((Integer)SpawnRateConfig.INSTANCE.starBirthmark.get(), pLevel.m_213780_())) {
                     baby.setStar(true);
                  } else if (direwolf.isStar() && SpawnRateConfig.rollChance((Integer)SpawnRateConfig.INSTANCE.starBirthmark.get(), pLevel.m_213780_())) {
                     baby.setStar(true);
                  }
               } else {
                  int fatherVariant = this.getTypeVariant();
                  int motherVariant = direwolf.getTypeVariant();
                  if (fatherVariant == motherVariant) {
                     baby.setVariant(baby.getVariant());
                  } else if (fatherVariant >= 13) {
                     baby.setVariant(direwolf.getVariant());
                  } else if (motherVariant >= 13) {
                     baby.setVariant(this.getVariant());
                  } else if ((double)(pLevel.m_213780_().m_188501_() * 100.0F) <= (Double)SpawnRateConfig.INSTANCE.elementalFangPack.get()) {
                     baby.setVariant(this.getEvolutionByBiomes());
                  } else {
                     baby.setVariant(DirewolfVariant.BLACK_FANG);
                  }
               }

               if (baby.getTypeVariant() >= 14) {
                  baby.setVariant(DirewolfVariant.TEMPEST_WOLF);
               }
            }

            return baby;
         }
      }
   }

   public boolean m_7132_() {
      return this.m_20160_() && !this.m_20068_();
   }

   public double getCustomJump() {
      return this.m_21133_(Attributes.f_22288_);
   }

   public boolean m_6146_() {
      return true;
   }

   public boolean hasScrollAbility() {
      if (this.getCurrentEvolutionState() <= 0) {
         return false;
      } else {
         return this.getControllingPassenger() != null;
      }
   }

   public boolean m_7310_(Entity other) {
      if (this.getControllingPassenger() == null && this.m_21826_() != other) {
         return false;
      } else if ((double)this.getSize() < 1.75D) {
         return super.m_7310_(other);
      } else {
         return this.m_20197_().size() < (int)((double)this.getSize() + 0.25D);
      }
   }

   protected void m_20351_(Entity pPassenger) {
      super.m_20351_(pPassenger);
      if (this.m_20068_() && this.getControllingPassenger() == null) {
         this.m_20242_(false);
      }

   }

   @Nullable
   public LivingEntity getControllingPassenger() {
      Iterator var1 = this.m_20197_().iterator();

      while(var1.hasNext()) {
         Entity passenger = (Entity)var1.next();
         if (passenger instanceof Player) {
            Player player = (Player)passenger;
            if (player.equals(this.m_21826_())) {
               return player;
            }
         }
      }

      return null;
   }

   public void m_7332_(Entity passenger) {
      if (this.m_20363_(passenger)) {
         passenger.m_183634_();
         int index = this.m_20197_().indexOf(passenger);
         float radius = -0.35F - (float)index;
         float angle = 0.017453292F * this.f_20883_;
         double yOffset = this.m_20186_() + this.m_6048_() + passenger.m_6049_();
         double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
         double extraZ = (double)(radius * Mth.m_14089_(angle));
         passenger.m_6034_(this.m_20185_() + extraX, yOffset, this.m_20189_() + extraZ);
      }
   }

   public double m_6048_() {
      return super.m_6048_() + 0.25D * (double)(this.getSize() - 1.0F);
   }

   public void m_7888_(int pJumpPower) {
      if (pJumpPower >= 90) {
         this.playerJumpPendingScale = 1.0F;
      } else {
         if (pJumpPower < 0) {
            pJumpPower = 0;
         }

         this.playerJumpPendingScale = 0.4F + 0.4F * (float)pJumpPower / 90.0F;
      }

   }

   public void m_7199_(int pJumpPower) {
      if (this.m_20096_()) {
         this.playJumpSound();
      } else if (this.getVariant() == DirewolfVariant.TEMPEST_STAR_WOLF && !this.m_21023_((MobEffect)TensuraMobEffects.MAGIC_INTERFERENCE.get())) {
         this.m_20242_(true);
      }

   }

   public void m_8012_() {
   }

   public void m_7023_(Vec3 pTravelVector) {
      if (this.m_6084_()) {
         LivingEntity controller = this.getControllingPassenger();
         if (this.m_20160_() && controller != null) {
            this.m_146922_(controller.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(controller.m_146909_() * 0.5F);
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.f_20883_;
            float f = controller.f_20900_ * 0.5F;
            float f1 = controller.f_20902_;
            if (f1 <= 0.0F) {
               f1 *= 0.25F;
            }

            double threshold = Math.min(this.m_20204_(), 0.9D);
            boolean canJump = this.m_20096_() || this.isInFluidType((fluidType, height) -> {
               return height <= threshold;
            }) && this.getVariant() == DirewolfVariant.TEMPEST_STAR_WOLF;
            if (this.playerJumpPendingScale > 0.0F && !this.isPlayerJumping() && canJump) {
               double d0 = this.getCustomJump() * (double)this.playerJumpPendingScale * (double)this.m_20098_();
               double d1 = d0 + this.m_182332_();
               Vec3 vec3 = this.m_20184_();
               this.m_20334_(vec3.f_82479_, d1, vec3.f_82481_);
               this.setPlayerJumping(true);
               this.f_19812_ = true;
               ForgeHooks.onLivingJump(this);
               if (f1 > 0.0F) {
                  float f2 = Mth.m_14031_(this.m_146908_() * 0.017453292F);
                  float f3 = Mth.m_14089_(this.m_146908_() * 0.017453292F);
                  this.m_20256_(this.m_20184_().m_82520_((double)(-0.4F * f2 * this.playerJumpPendingScale), 0.0D, (double)(0.4F * f3 * this.playerJumpPendingScale)));
               }

               this.playerJumpPendingScale = 0.0F;
            }

            float flyingMultiplier = this.m_20068_() ? 0.5F : 0.1F;
            this.f_20887_ = this.m_6113_() * flyingMultiplier;
            if (this.m_6109_()) {
               float speed = (float)this.m_21133_(Attributes.f_22279_);
               if (controller.m_20142_()) {
                  speed = (float)((double)speed * 2.5D);
               }

               this.m_7910_(speed);
               if (this.m_20068_()) {
                  if (controller.f_20899_) {
                     this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.1D, 0.0D));
                  } else if (TensuraKeybinds.MOUNT_DESCENDING.m_90857_()) {
                     this.descending(this, controller);
                  }
               } else if (this.isInFluidType((fluidType, height) -> {
                  return height > threshold;
               }) && f1 > 0.0F) {
                  this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.03D, 0.0D));
               }

               super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
            } else if (controller instanceof Player) {
               this.m_20256_(Vec3.f_82478_);
            }

            if (this.f_19861_) {
               this.playerJumpPendingScale = 0.0F;
               this.setPlayerJumping(false);
               this.m_20242_(false);
            }

            this.m_146872_();
         } else {
            this.f_20887_ = 0.02F;
            super.m_7023_(pTravelVector);
         }
      }

   }

   public void mountAbility(Player rider) {
      if (this.getMiscAnimation() == 0 || this.getMiscAnimation() == 1 || this.getMiscAnimation() == 2) {
         this.m_8127_();
         if (this.getSize() >= this.getMaxSize() && this.getBlackLightning() != null) {
            Entity target = SkillHelper.getTargetingEntity(rider, 50.0D, true, false);
            Vec3 pos;
            if (target != null) {
               pos = target.m_20182_();
            } else {
               pos = SkillHelper.getPlayerPOVHitResultFromPos(this.f_19853_, rider, Fluid.NONE, 50.0D, rider.m_20182_().m_82520_(0.0D, (double)rider.m_20206_(), 0.0D)).m_82450_();
            }

            this.blackLightning(pos);
            this.setMiscAnimation(4);
         } else {
            this.setMiscAnimation(3);
         }

      }
   }

   public void mountScrollAbility(Player rider, double scrollChange) {
      float newSize = Mth.m_14036_(this.getSize() + (float)scrollChange * 0.1F, 0.75F, this.getMaxSize());
      this.setSize(newSize);
      if (this.m_20197_().size() > (int)((double)newSize + 0.25D)) {
         Iterator var5 = this.m_20197_().iterator();

         while(var5.hasNext()) {
            Entity passenger = (Entity)var5.next();
            if (passenger != this.getControllingPassenger()) {
               passenger.m_19877_();
               break;
            }
         }
      }

   }

   protected void m_5907_() {
      super.m_5907_();
      if (this.isChested()) {
         if (!this.f_19853_.m_5776_()) {
            this.m_19998_(Blocks.f_50087_);

            for(int i = 0; i < this.inventory.m_6643_(); ++i) {
               this.m_19983_(this.inventory.m_8020_(i));
            }
         }

         this.inventory.m_6211_();
         this.setChested(false);
      }

   }

   public int m_5792_() {
      return 12;
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.direwolfSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      DirewolfEntity.DirewolfPackData packData;
      if (pSpawnData instanceof DirewolfEntity.DirewolfPackData) {
         DirewolfEntity.DirewolfPackData data = (DirewolfEntity.DirewolfPackData)pSpawnData;
         packData = data;
      } else {
         packData = new DirewolfEntity.DirewolfPackData();
      }

      this.setSize(1.0F);
      this.setMaxSize(1.2F);
      if (packData.getVariant() == null) {
         if ((double)(pLevel.m_213780_().m_188501_() * 100.0F) <= (Double)SpawnRateConfig.INSTANCE.evolvedPack.get()) {
            if ((double)(pLevel.m_213780_().m_188501_() * 100.0F) <= (Double)SpawnRateConfig.INSTANCE.tempestWolfPack.get()) {
               packData.setVariant(DirewolfVariant.TEMPEST_WOLF);
            } else if ((double)(pLevel.m_213780_().m_188501_() * 100.0F) <= (Double)SpawnRateConfig.INSTANCE.elementalFangPack.get()) {
               packData.setVariant(this.getEvolutionByBiomes());
            } else {
               packData.setVariant(DirewolfVariant.BLACK_FANG);
            }
         } else {
            packData.setVariant(DirewolfVariant.DIREWOLF);
         }
      }

      DirewolfVariant variant = packData.getVariant();
      if (variant.getId() > 0) {
         this.setSize(this.getSize() + 0.25F);
         this.setMaxSize(1.25F);
         this.setCurrentEvolutionState(this.getCurrentEvolutionState() + 1);
         this.evolveBuff();
         if (variant == DirewolfVariant.TEMPEST_WOLF && packData.getStar() == null && SpawnRateConfig.rollChance((Integer)SpawnRateConfig.INSTANCE.starBirthmark.get(), pLevel.m_213780_())) {
            this.setStar(true);
            packData.setStar(this);
         }
      }

      this.setVariant(packData.getVariant());
      if (packData.getAlpha() == null && SpawnRateConfig.rollChance((Integer)SpawnRateConfig.INSTANCE.alphaWolf.get(), pLevel.m_213780_())) {
         packData.setAlpha(this);
         this.setAlpha(true);
         this.setSize(this.getVariant() != DirewolfVariant.DIREWOLF ? 1.75F : 1.5F);
         this.setMaxSize(this.getMaxSize() + 0.5F);
         this.gainAttackDamage(this, 20.0D);
         this.gainMovementSpeed(this, 0.10000000149011612D);
         this.gainSwimSpeed(this, 1.0D);
         this.gainMaxHealth(this, 30.0D);
      }

      if (this.getShadowMotion() != null && (pReason == MobSpawnType.MOB_SUMMONED || pReason == MobSpawnType.SPAWN_EGG)) {
         this.setMiscAnimation(5);
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, packData, pDataTag);
   }

   protected SoundEvent m_7515_() {
      return !this.m_21660_() && !this.isAlpha() ? SoundEvents.f_12617_ : SoundEvents.f_12619_;
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return SoundEvents.f_12621_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_12618_;
   }

   protected void playJumpSound() {
      this.m_5496_((SoundEvent)TensuraSoundEvents.SMALL_JUMP_IMPACT.get(), 0.4F, 1.0F);
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_5803_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.direwolf.sleep", EDefaultLoopTypes.LOOP));
         return PlayState.CONTINUE;
      } else if (this.m_21825_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.direwolf.sit", EDefaultLoopTypes.LOOP));
         return PlayState.CONTINUE;
      } else {
         if (event.isMoving()) {
            if (this.isInFluidType()) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.direwolf.swim", EDefaultLoopTypes.LOOP));
            } else if (!this.m_21660_() && (this.getControllingPassenger() == null || !this.getControllingPassenger().m_20142_())) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.direwolf.walk", EDefaultLoopTypes.LOOP));
            } else {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.direwolf.run", EDefaultLoopTypes.LOOP));
            }
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.direwolf.idle", EDefaultLoopTypes.LOOP));
         }

         return PlayState.CONTINUE;
      }
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.direwolf.eat", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.direwolf.bite", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.direwolf.bark", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 4) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.direwolf.howl", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 5) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.direwolf.shadow_motion", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState tiltPredicate(AnimationEvent<E> event) {
      if (this.getMiscAnimation() != 0) {
         return PlayState.STOP;
      } else if (this.isInterested()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.direwolf.head_tilt", EDefaultLoopTypes.LOOP));
         return PlayState.CONTINUE;
      } else {
         return PlayState.STOP;
      }
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
      data.addAnimationController(new AnimationController(this, "tiltController", 0.0F, this::tiltPredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   public boolean isPlayerJumping() {
      return this.playerJumping;
   }

   public void setPlayerJumping(boolean playerJumping) {
      this.playerJumping = playerJumping;
   }

   static {
      DATA_ID_TYPE_VARIANT = SynchedEntityData.m_135353_(DirewolfEntity.class, EntityDataSerializers.f_135028_);
      EVOLUTION_STATE = SynchedEntityData.m_135353_(DirewolfEntity.class, EntityDataSerializers.f_135028_);
      MISC_ANIMATION = SynchedEntityData.m_135353_(DirewolfEntity.class, EntityDataSerializers.f_135028_);
      LEADER_TICK = SynchedEntityData.m_135353_(DirewolfEntity.class, EntityDataSerializers.f_135028_);
      INTERESTED = SynchedEntityData.m_135353_(DirewolfEntity.class, EntityDataSerializers.f_135035_);
      CHESTED = SynchedEntityData.m_135353_(DirewolfEntity.class, EntityDataSerializers.f_135035_);
      LEADER = SynchedEntityData.m_135353_(DirewolfEntity.class, EntityDataSerializers.f_135035_);
      ALPHA = SynchedEntityData.m_135353_(DirewolfEntity.class, EntityDataSerializers.f_135035_);
      STAR = SynchedEntityData.m_135353_(DirewolfEntity.class, EntityDataSerializers.f_135035_);
      MAX_SIZE = SynchedEntityData.m_135353_(DirewolfEntity.class, EntityDataSerializers.f_135029_);
      SIZE = SynchedEntityData.m_135353_(DirewolfEntity.class, EntityDataSerializers.f_135029_);
      LEADER_UUID = SynchedEntityData.m_135353_(DirewolfEntity.class, EntityDataSerializers.f_135041_);
   }

   class DirewolfAttackGoal extends MeleeAttackGoal {
      private final DirewolfEntity wolf = DirewolfEntity.this;

      public DirewolfAttackGoal() {
         super(DirewolfEntity.this, 2.5D, true);
      }

      public boolean m_8036_() {
         return this.wolf.m_21827_() ? false : super.m_8036_();
      }

      public boolean m_8045_() {
         return this.wolf.m_21827_() ? false : super.m_8045_();
      }

      public void m_8037_() {
         if (this.wolf.getMiscAnimation() == 0) {
            super.m_8037_();
         }

      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double distance = this.m_6639_(pEnemy);
         if (this.wolf.getMiscAnimation() == 0) {
            int randomAttack = this.randomAttack(pDistToEnemySqr, pEnemy);
            double var10000;
            switch(randomAttack) {
            case 3:
               this.wolf.m_21573_().m_26573_();
               var10000 = 225.0D;
               break;
            case 4:
            case 5:
               this.wolf.m_21573_().m_26573_();
               var10000 = 3600.0D;
               break;
            default:
               var10000 = distance;
            }

            double attackRange = var10000;
            if (pDistToEnemySqr <= attackRange && this.m_25564_()) {
               this.m_25563_();
               this.wolf.setMiscAnimation(randomAttack);
               if (randomAttack == 2) {
                  this.wolf.m_7327_(pEnemy);
               } else if (randomAttack == 5) {
                  this.wolf.m_8127_();
                  this.wolf.m_20219_(pEnemy.m_20182_());
                  TensuraParticleHelper.addServerParticlesAroundSelf(this.wolf, ParticleTypes.f_123765_);
                  this.wolf.f_19864_ = true;
               }
            }
         }

      }

      protected int randomAttack(double distance, LivingEntity entity) {
         if (this.wolf.m_6162_()) {
            return 2;
         } else {
            if (this.wolf.f_19796_.m_188503_(10) == 1) {
               if (entity.m_20096_() && this.wolf.m_20096_() && distance >= 200.0D && this.wolf.f_19796_.m_188503_(10) == 1 && this.wolf.getShadowMotion() != null) {
                  return 5;
               }

               if ((distance >= 200.0D || this.wolf.f_19796_.m_188503_(15) == 1) && this.wolf.getBlackLightning() != null) {
                  return 4;
               }

               if (distance >= 36.0D || this.wolf.f_19796_.m_188503_(20) == 1) {
                  return 3;
               }
            }

            return 2;
         }
      }

      protected double m_6639_(LivingEntity pAttackTarget) {
         return (double)(this.f_25540_.m_20205_() * this.f_25540_.m_20205_() * 3.0F + pAttackTarget.m_20205_());
      }
   }

   static class FollowLeaderGoal extends Goal {
      private final DirewolfEntity wolf;
      @Nullable
      private DirewolfEntity leader;
      private final double speedModifier;
      private int timeToRePath;
      private int nextStartTick;

      public FollowLeaderGoal(DirewolfEntity wolf, double pSpeedModifier) {
         this.wolf = wolf;
         this.speedModifier = pSpeedModifier;
         this.nextStartTick = this.nextStartTick(wolf);
      }

      protected int nextStartTick(DirewolfEntity pTaskOwner) {
         return m_186073_(100 + pTaskOwner.m_217043_().m_188503_(100) % 20);
      }

      public boolean m_8036_() {
         if (this.wolf.m_21824_()) {
            return false;
         } else if (!this.wolf.isAlpha() && !this.wolf.isLeader()) {
            if (this.nextStartTick > 0) {
               --this.nextStartTick;
               return false;
            } else {
               UUID uuid = this.wolf.getLeaderUUID();
               if (uuid != null) {
                  Level var4 = this.wolf.f_19853_;
                  if (var4 instanceof ServerLevel) {
                     ServerLevel serverLevel = (ServerLevel)var4;
                     Entity var6 = serverLevel.m_8791_(uuid);
                     if (var6 instanceof DirewolfEntity) {
                        DirewolfEntity direwolf = (DirewolfEntity)var6;
                        this.leader = direwolf;
                        return true;
                     }
                  }
               }

               List<DirewolfEntity> list = this.wolf.f_19853_.m_45976_(DirewolfEntity.class, this.wolf.m_20191_().m_82377_(32.0D, 32.0D, 32.0D)).stream().filter((entity) -> {
                  return entity != this.wolf;
               }).toList();
               if (this.searchForLeader(list, DirewolfEntity::isAlpha)) {
                  return true;
               } else if (this.searchForLeader(list, DirewolfEntity::isLeader)) {
                  return true;
               } else {
                  return this.searchForLeader(list, DirewolfEntity::isStar) ? true : this.searchForLeader(list, (entity) -> {
                     return true;
                  });
               }
            }
         } else {
            return false;
         }
      }

      private boolean searchForLeader(List<DirewolfEntity> list, Predicate<DirewolfEntity> predicate) {
         List<DirewolfEntity> filtered = list.stream().filter(predicate).sorted(Comparator.comparingInt(DirewolfEntity::getCurrentEvolutionState)).sorted(Comparator.comparingDouble(DirewolfEntity::getMaxSize)).toList();
         if (filtered.isEmpty()) {
            return false;
         } else {
            DirewolfEntity leader = (DirewolfEntity)filtered.get(0);
            this.wolf.setLeaderUUID(leader.m_20148_());
            this.wolf.setLeader(false);
            this.leader = leader;
            this.leader.setLeader(true);
            this.leader.setLeaderUUID((UUID)null);
            return true;
         }
      }

      public boolean m_8045_() {
         if (this.wolf.m_21824_()) {
            return false;
         } else {
            return this.leader == null ? false : this.leader.m_6084_();
         }
      }

      public void m_8056_() {
         this.timeToRePath = 0;
         if (this.wolf.m_5803_()) {
            this.wolf.setSleeping(false);
         }

      }

      public void m_8041_() {
         this.leader = null;
         this.wolf.m_21573_().m_26573_();
      }

      public void m_8037_() {
         if (this.leader != null) {
            this.leader.setLeaderTick(200);
            this.wolf.setLeaderUUID(this.leader.m_20148_());
            if (!this.wolf.m_21523_() && !this.wolf.m_20159_()) {
               if (this.wolf.m_5448_() == null) {
                  if (--this.timeToRePath <= 0) {
                     double d0 = this.wolf.m_20280_(this.leader);
                     if (d0 >= 36.0D && d0 <= 400.0D) {
                        if (this.leader.m_5448_() != null && this.wolf.m_5448_() != null && this.leader.m_5448_() != this.wolf && this.leader.m_5448_().m_20270_(this.wolf) <= 20.0F) {
                           this.wolf.m_6710_(this.leader.m_5448_());
                        }

                        this.timeToRePath = this.m_183277_(10);
                        this.wolf.m_21573_().m_5624_(this.leader, this.speedModifier);
                     }

                  }
               }
            }
         }
      }
   }

   static class TiltingHeadGoal extends Goal {
      private final DirewolfEntity wolf;
      @Nullable
      private Player player;
      private final Level level;
      private final float lookDistance;
      private int lookTime;
      private final TargetingConditions begTargeting;

      public TiltingHeadGoal(DirewolfEntity entity, float pLookDistance) {
         this.wolf = entity;
         this.level = entity.f_19853_;
         this.lookDistance = pLookDistance;
         this.begTargeting = TargetingConditions.m_148353_().m_26883_((double)pLookDistance);
         this.m_7021_(EnumSet.of(Flag.LOOK));
      }

      public boolean m_8036_() {
         this.player = this.level.m_45946_(this.begTargeting, this.wolf);
         return this.player == null ? false : this.playerHoldingInteresting(this.player);
      }

      public boolean m_8045_() {
         if (this.player == null) {
            return false;
         } else if (!this.player.m_6084_()) {
            return false;
         } else if (this.wolf.m_20280_(this.player) > (double)(this.lookDistance * this.lookDistance)) {
            return false;
         } else {
            return this.lookTime > 0 && this.playerHoldingInteresting(this.player);
         }
      }

      public void m_8056_() {
         this.wolf.setInterested(true);
         this.lookTime = this.m_183277_(40 + this.wolf.m_217043_().m_188503_(40));
      }

      public void m_8041_() {
         this.wolf.setInterested(false);
         this.player = null;
      }

      public void m_8037_() {
         if (this.player != null) {
            this.wolf.m_21563_().m_24950_(this.player.m_20185_(), this.player.m_20188_(), this.player.m_20189_(), 10.0F, (float)this.wolf.m_8132_());
            --this.lookTime;
         }
      }

      private boolean playerHoldingInteresting(Player pPlayer) {
         if (this.wolf.m_20363_(pPlayer)) {
            return false;
         } else {
            InteractionHand[] var2 = InteractionHand.values();
            int var3 = var2.length;

            for(int var4 = 0; var4 < var3; ++var4) {
               InteractionHand interactionhand = var2[var4];
               ItemStack itemstack = pPlayer.m_21120_(interactionhand);
               if (this.wolf.m_21824_()) {
                  if (itemstack.m_150930_(net.minecraft.world.item.Items.f_42500_)) {
                     return true;
                  }

                  if (this.wolf.m_6898_(itemstack) && this.wolf.m_21223_() < this.wolf.m_21233_()) {
                     return true;
                  }
               }
            }

            return false;
         }
      }
   }

   public static class DirewolfPackData extends AgeableMobGroupData {
      @Nullable
      private DirewolfEntity alpha;
      @Nullable
      private DirewolfEntity star;
      @Nullable
      private DirewolfVariant variant;

      public DirewolfPackData() {
         super(false);
      }

      public void setAlpha(@Nullable DirewolfEntity alpha) {
         this.alpha = alpha;
      }

      public void setStar(@Nullable DirewolfEntity star) {
         this.star = star;
      }

      public void setVariant(@Nullable DirewolfVariant variant) {
         this.variant = variant;
      }

      @Nullable
      public DirewolfEntity getAlpha() {
         return this.alpha;
      }

      @Nullable
      public DirewolfEntity getStar() {
         return this.star;
      }

      @Nullable
      public DirewolfVariant getVariant() {
         return this.variant;
      }
   }
}
