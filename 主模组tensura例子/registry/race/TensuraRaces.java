package com.github.manasmods.tensura.registry.race;

import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.beastfolk.BeastLordRace;
import com.github.manasmods.tensura.race.beastfolk.BeastfolkRace;
import com.github.manasmods.tensura.race.beastfolk.DivineBeastRace;
import com.github.manasmods.tensura.race.beastfolk.SpiritBeastRace;
import com.github.manasmods.tensura.race.daemon.ArchDaemonRace;
import com.github.manasmods.tensura.race.daemon.DaemonLordRace;
import com.github.manasmods.tensura.race.daemon.DevilLordRace;
import com.github.manasmods.tensura.race.daemon.GreaterDaemonRace;
import com.github.manasmods.tensura.race.daemon.LesserDaemonRace;
import com.github.manasmods.tensura.race.dwarf.DivineDwarfRace;
import com.github.manasmods.tensura.race.dwarf.DwarfRace;
import com.github.manasmods.tensura.race.dwarf.DwarfSaintRace;
import com.github.manasmods.tensura.race.dwarf.EnlightenedDwarfRace;
import com.github.manasmods.tensura.race.elf.DivineElfRace;
import com.github.manasmods.tensura.race.elf.ElfRace;
import com.github.manasmods.tensura.race.elf.ElfSaintRace;
import com.github.manasmods.tensura.race.elf.EnlightenedElfRace;
import com.github.manasmods.tensura.race.goblin.EnlightenedHobgoblinRace;
import com.github.manasmods.tensura.race.goblin.GoblinRace;
import com.github.manasmods.tensura.race.goblin.HobgoblinRace;
import com.github.manasmods.tensura.race.goblin.HobgoblinSaintRace;
import com.github.manasmods.tensura.race.human.DivineHumanRace;
import com.github.manasmods.tensura.race.human.EnlightenedHumanRace;
import com.github.manasmods.tensura.race.human.HumanRace;
import com.github.manasmods.tensura.race.human.HumanSaintRace;
import com.github.manasmods.tensura.race.lizardman.DivineDragonRace;
import com.github.manasmods.tensura.race.lizardman.DragonewtRace;
import com.github.manasmods.tensura.race.lizardman.LizardmanRace;
import com.github.manasmods.tensura.race.lizardman.TrueDragonewtRace;
import com.github.manasmods.tensura.race.merfolk.DivineFishRace;
import com.github.manasmods.tensura.race.merfolk.EnlightenedMerfolkRace;
import com.github.manasmods.tensura.race.merfolk.MerfolkRace;
import com.github.manasmods.tensura.race.merfolk.MerfolkSaintRace;
import com.github.manasmods.tensura.race.ogre.DeathOniRace;
import com.github.manasmods.tensura.race.ogre.DivineFighterRace;
import com.github.manasmods.tensura.race.ogre.DivineOniRace;
import com.github.manasmods.tensura.race.ogre.EnlightenedOgreRace;
import com.github.manasmods.tensura.race.ogre.KijinRace;
import com.github.manasmods.tensura.race.ogre.MysticOniRace;
import com.github.manasmods.tensura.race.ogre.OgreRace;
import com.github.manasmods.tensura.race.ogre.SpiritOniRace;
import com.github.manasmods.tensura.race.ogre.WickedOniRace;
import com.github.manasmods.tensura.race.orc.DivineBoarRace;
import com.github.manasmods.tensura.race.orc.HighOrcRace;
import com.github.manasmods.tensura.race.orc.OrcDisasterRace;
import com.github.manasmods.tensura.race.orc.OrcLordRace;
import com.github.manasmods.tensura.race.orc.OrcRace;
import com.github.manasmods.tensura.race.orc.SpiritBoarRace;
import com.github.manasmods.tensura.race.slime.DemonSlimeRace;
import com.github.manasmods.tensura.race.slime.GodSlimeRace;
import com.github.manasmods.tensura.race.slime.MetalSlimeRace;
import com.github.manasmods.tensura.race.slime.SlimeRace;
import com.github.manasmods.tensura.race.vampire.DivineVampireRace;
import com.github.manasmods.tensura.race.vampire.GhoulRace;
import com.github.manasmods.tensura.race.vampire.VampireLordRace;
import com.github.manasmods.tensura.race.vampire.VampireOvercomerRace;
import com.github.manasmods.tensura.race.vampire.VampireRace;
import com.github.manasmods.tensura.race.wight.DivineSkeletonRace;
import com.github.manasmods.tensura.race.wight.SpiritSkeletonRace;
import com.github.manasmods.tensura.race.wight.WightKingRace;
import com.github.manasmods.tensura.race.wight.WightRace;
import java.util.function.Supplier;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.IForgeRegistry;
import net.minecraftforge.registries.RegistryBuilder;
import net.minecraftforge.registries.RegistryObject;

public class TensuraRaces {
   private static final ResourceLocation REGISTRY_KEY = new ResourceLocation("tensura", "races");
   private static final DeferredRegister<Race> registry;
   public static final Supplier<IForgeRegistry<Race>> RACE_REGISTRY;
   public static final RegistryObject<Race> BEASTFOLK;
   public static final RegistryObject<Race> BEAST_LORD;
   public static final RegistryObject<Race> SPIRIT_BEAST;
   public static final RegistryObject<Race> DIVINE_BEAST;
   public static final RegistryObject<Race> OGRE;
   public static final RegistryObject<Race> KIJIN;
   public static final RegistryObject<Race> ENLIGHTENED_OGRE;
   public static final RegistryObject<Race> MYSTIC_ONI;
   public static final RegistryObject<Race> WICKED_ONI;
   public static final RegistryObject<Race> SPIRIT_ONI;
   public static final RegistryObject<Race> DEATH_ONI;
   public static final RegistryObject<Race> DIVINE_ONI;
   public static final RegistryObject<Race> DIVINE_FIGHTER;
   public static final RegistryObject<Race> GOBLIN;
   public static final RegistryObject<Race> HOBGOBLIN;
   public static final RegistryObject<Race> ENLIGHTENED_HOBGOBLIN;
   public static final RegistryObject<Race> HOBGOBLIN_SAINT;
   public static final RegistryObject<Race> ORC;
   public static final RegistryObject<Race> HIGH_ORC;
   public static final RegistryObject<Race> SPIRIT_BOAR;
   public static final RegistryObject<Race> ORC_LORD;
   public static final RegistryObject<Race> ORC_DISASTER;
   public static final RegistryObject<Race> DIVINE_BOAR;
   public static final RegistryObject<Race> LIZARDMAN;
   public static final RegistryObject<Race> DRAGONEWT;
   public static final RegistryObject<Race> TRUE_DRAGONEWT;
   public static final RegistryObject<Race> DIVINE_DRAGON;
   public static final RegistryObject<Race> MERFOLK;
   public static final RegistryObject<Race> ENLIGHTENED_MERFOLK;
   public static final RegistryObject<Race> MERFOLK_SAINT;
   public static final RegistryObject<Race> DIVINE_FISH;
   public static final RegistryObject<Race> ELF;
   public static final RegistryObject<Race> ENLIGHTENED_ELF;
   public static final RegistryObject<Race> ELF_SAINT;
   public static final RegistryObject<Race> DIVINE_ELF;
   public static final RegistryObject<Race> HUMAN;
   public static final RegistryObject<Race> ENLIGHTENED_HUMAN;
   public static final RegistryObject<Race> HUMAN_SAINT;
   public static final RegistryObject<Race> DIVINE_HUMAN;
   public static final RegistryObject<Race> DWARF;
   public static final RegistryObject<Race> ENLIGHTENED_DWARF;
   public static final RegistryObject<Race> DWARF_SAINT;
   public static final RegistryObject<Race> DIVINE_DWARF;
   public static final RegistryObject<Race> SLIME;
   public static final RegistryObject<Race> METAL_SLIME;
   public static final RegistryObject<Race> DEMON_SLIME;
   public static final RegistryObject<Race> GOD_SLIME;
   public static final RegistryObject<Race> GHOUL;
   public static final RegistryObject<Race> VAMPIRE;
   public static final RegistryObject<Race> VAMPIRE_OVERCOMER;
   public static final RegistryObject<Race> VAMPIRE_LORD;
   public static final RegistryObject<Race> DIVINE_VAMPIRE;
   public static final RegistryObject<Race> WIGHT;
   public static final RegistryObject<Race> WIGHT_KING;
   public static final RegistryObject<Race> SPIRIT_SKELETON;
   public static final RegistryObject<Race> DIVINE_SKELETON;
   public static final RegistryObject<Race> LESSER_DAEMON;
   public static final RegistryObject<Race> GREATER_DAEMON;
   public static final RegistryObject<Race> ARCH_DAEMON;
   public static final RegistryObject<Race> DAEMON_LORD;
   public static final RegistryObject<Race> DEVIL_LORD;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(REGISTRY_KEY, "tensura");
      RACE_REGISTRY = registry.makeRegistry(RegistryBuilder::new);
      BEASTFOLK = registry.register("beastfolk", BeastfolkRace::new);
      BEAST_LORD = registry.register("beast_lord", BeastLordRace::new);
      SPIRIT_BEAST = registry.register("spirit_beast", SpiritBeastRace::new);
      DIVINE_BEAST = registry.register("divine_beast", DivineBeastRace::new);
      OGRE = registry.register("ogre", OgreRace::new);
      KIJIN = registry.register("kijin", KijinRace::new);
      ENLIGHTENED_OGRE = registry.register("enlightened_ogre", EnlightenedOgreRace::new);
      MYSTIC_ONI = registry.register("mystic_oni", MysticOniRace::new);
      WICKED_ONI = registry.register("wicked_oni", WickedOniRace::new);
      SPIRIT_ONI = registry.register("spirit_oni", SpiritOniRace::new);
      DEATH_ONI = registry.register("death_oni", DeathOniRace::new);
      DIVINE_ONI = registry.register("divine_oni", DivineOniRace::new);
      DIVINE_FIGHTER = registry.register("divine_fighter", DivineFighterRace::new);
      GOBLIN = registry.register("goblin", GoblinRace::new);
      HOBGOBLIN = registry.register("hobgoblin", HobgoblinRace::new);
      ENLIGHTENED_HOBGOBLIN = registry.register("enlightened_hobgoblin", EnlightenedHobgoblinRace::new);
      HOBGOBLIN_SAINT = registry.register("hobgoblin_saint", HobgoblinSaintRace::new);
      ORC = registry.register("orc", OrcRace::new);
      HIGH_ORC = registry.register("high_orc", HighOrcRace::new);
      SPIRIT_BOAR = registry.register("spirit_boar", SpiritBoarRace::new);
      ORC_LORD = registry.register("orc_lord", OrcLordRace::new);
      ORC_DISASTER = registry.register("orc_disaster", OrcDisasterRace::new);
      DIVINE_BOAR = registry.register("divine_boar", DivineBoarRace::new);
      LIZARDMAN = registry.register("lizardman", LizardmanRace::new);
      DRAGONEWT = registry.register("dragonewt", DragonewtRace::new);
      TRUE_DRAGONEWT = registry.register("true_dragonewt", TrueDragonewtRace::new);
      DIVINE_DRAGON = registry.register("divine_dragon", DivineDragonRace::new);
      MERFOLK = registry.register("merfolk", MerfolkRace::new);
      ENLIGHTENED_MERFOLK = registry.register("enlightened_merfolk", EnlightenedMerfolkRace::new);
      MERFOLK_SAINT = registry.register("merfolk_saint", MerfolkSaintRace::new);
      DIVINE_FISH = registry.register("divine_fish", DivineFishRace::new);
      ELF = registry.register("elf", ElfRace::new);
      ENLIGHTENED_ELF = registry.register("enlightened_elf", EnlightenedElfRace::new);
      ELF_SAINT = registry.register("elf_saint", ElfSaintRace::new);
      DIVINE_ELF = registry.register("divine_elf", DivineElfRace::new);
      HUMAN = registry.register("human", HumanRace::new);
      ENLIGHTENED_HUMAN = registry.register("enlightened_human", EnlightenedHumanRace::new);
      HUMAN_SAINT = registry.register("human_saint", HumanSaintRace::new);
      DIVINE_HUMAN = registry.register("divine_human", DivineHumanRace::new);
      DWARF = registry.register("dwarf", DwarfRace::new);
      ENLIGHTENED_DWARF = registry.register("enlightened_dwarf", EnlightenedDwarfRace::new);
      DWARF_SAINT = registry.register("dwarf_saint", DwarfSaintRace::new);
      DIVINE_DWARF = registry.register("divine_dwarf", DivineDwarfRace::new);
      SLIME = registry.register("slime", SlimeRace::new);
      METAL_SLIME = registry.register("metal_slime", MetalSlimeRace::new);
      DEMON_SLIME = registry.register("demon_slime", DemonSlimeRace::new);
      GOD_SLIME = registry.register("god_slime", GodSlimeRace::new);
      GHOUL = registry.register("ghoul", GhoulRace::new);
      VAMPIRE = registry.register("vampire", VampireRace::new);
      VAMPIRE_OVERCOMER = registry.register("vampire_overcomer", VampireOvercomerRace::new);
      VAMPIRE_LORD = registry.register("vampire_lord", VampireLordRace::new);
      DIVINE_VAMPIRE = registry.register("divine_vampire", DivineVampireRace::new);
      WIGHT = registry.register("wight", WightRace::new);
      WIGHT_KING = registry.register("wight_king", WightKingRace::new);
      SPIRIT_SKELETON = registry.register("spirit_skeleton", SpiritSkeletonRace::new);
      DIVINE_SKELETON = registry.register("divine_skeleton", DivineSkeletonRace::new);
      LESSER_DAEMON = registry.register("lesser_daemon", LesserDaemonRace::new);
      GREATER_DAEMON = registry.register("greater_daemon", GreaterDaemonRace::new);
      ARCH_DAEMON = registry.register("arch_daemon", ArchDaemonRace::new);
      DAEMON_LORD = registry.register("daemon_lord", DaemonLordRace::new);
      DEVIL_LORD = registry.register("devil_lord", DevilLordRace::new);
   }
}
