package com.github.manasmods.tensura.registry.skill;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.ability.skill.common.CoercionSkill;
import com.github.manasmods.tensura.ability.skill.common.CorrosionSkill;
import com.github.manasmods.tensura.ability.skill.common.FarsightSkill;
import com.github.manasmods.tensura.ability.skill.common.GravityFieldSkill;
import com.github.manasmods.tensura.ability.skill.common.GravityFlightSkill;
import com.github.manasmods.tensura.ability.skill.common.HydraulicPropulsionSkill;
import com.github.manasmods.tensura.ability.skill.common.ParalysisSkill;
import com.github.manasmods.tensura.ability.skill.common.PoisonSkill;
import com.github.manasmods.tensura.ability.skill.common.RangedBarrierSkill;
import com.github.manasmods.tensura.ability.skill.common.SelfRegenerationSkill;
import com.github.manasmods.tensura.ability.skill.common.StrengthSkill;
import com.github.manasmods.tensura.ability.skill.common.TelepathySkill;
import com.github.manasmods.tensura.ability.skill.common.ThoughtCommunicationSkill;
import com.github.manasmods.tensura.ability.skill.common.WaterBladeSkill;
import com.github.manasmods.tensura.ability.skill.common.WaterCurrentControlSkill;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

public class CommonSkills {
   private static final DeferredRegister<ManasSkill> registry = DeferredRegister.create(SkillAPI.getSkillRegistryKey(), "tensura");
   public static final RegistryObject<CoercionSkill> COERCION;
   public static final RegistryObject<CorrosionSkill> CORROSION;
   public static final RegistryObject<FarsightSkill> FARSIGHT;
   public static final RegistryObject<GravityFieldSkill> GRAVITY_FIELD;
   public static final RegistryObject<GravityFlightSkill> GRAVITY_FLIGHT;
   public static final RegistryObject<HydraulicPropulsionSkill> HYDRAULIC_PROPULSION;
   public static final RegistryObject<ParalysisSkill> PARALYSIS;
   public static final RegistryObject<PoisonSkill> POISON;
   public static final RegistryObject<RangedBarrierSkill> RANGED_BARRIER;
   public static final RegistryObject<SelfRegenerationSkill> SELF_REGENERATION;
   public static final RegistryObject<StrengthSkill> STRENGTH;
   public static final RegistryObject<WaterBladeSkill> WATER_BLADE;
   public static final RegistryObject<Skill> TELEPATHY;
   public static final RegistryObject<ThoughtCommunicationSkill> THOUGHT_COMMUNICATION;
   public static final RegistryObject<WaterCurrentControlSkill> WATER_CURRENT_CONTROL;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      COERCION = registry.register("coercion", CoercionSkill::new);
      CORROSION = registry.register("corrosion", CorrosionSkill::new);
      FARSIGHT = registry.register("farsight", FarsightSkill::new);
      GRAVITY_FIELD = registry.register("gravity_field", GravityFieldSkill::new);
      GRAVITY_FLIGHT = registry.register("gravity_flight", GravityFlightSkill::new);
      HYDRAULIC_PROPULSION = registry.register("hydraulic_propulsion", HydraulicPropulsionSkill::new);
      PARALYSIS = registry.register("paralysis", ParalysisSkill::new);
      POISON = registry.register("poison", PoisonSkill::new);
      RANGED_BARRIER = registry.register("ranged_barrier", RangedBarrierSkill::new);
      SELF_REGENERATION = registry.register("self_regeneration", SelfRegenerationSkill::new);
      STRENGTH = registry.register("strength", StrengthSkill::new);
      WATER_BLADE = registry.register("water_blade", WaterBladeSkill::new);
      TELEPATHY = registry.register("telepathy", TelepathySkill::new);
      THOUGHT_COMMUNICATION = registry.register("thought_communication", ThoughtCommunicationSkill::new);
      WATER_CURRENT_CONTROL = registry.register("water_current_control", WaterCurrentControlSkill::new);
   }
}
