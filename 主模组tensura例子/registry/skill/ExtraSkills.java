package com.github.manasmods.tensura.registry.skill;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.skill.extra.AllSeeingEyeSkill;
import com.github.manasmods.tensura.ability.skill.extra.AnalyticalAppraisalSkill;
import com.github.manasmods.tensura.ability.skill.extra.BlackFlameSkill;
import com.github.manasmods.tensura.ability.skill.extra.BlackLightningSkill;
import com.github.manasmods.tensura.ability.skill.extra.BodyDoubleSkill;
import com.github.manasmods.tensura.ability.skill.extra.ChantAnnulmentSkill;
import com.github.manasmods.tensura.ability.skill.extra.DangerSenseSkill;
import com.github.manasmods.tensura.ability.skill.extra.DemonLordHakiSkill;
import com.github.manasmods.tensura.ability.skill.extra.EarthDominationSkill;
import com.github.manasmods.tensura.ability.skill.extra.EarthManipulationSkill;
import com.github.manasmods.tensura.ability.skill.extra.FlameDominationSkill;
import com.github.manasmods.tensura.ability.skill.extra.FlameManipulationSkill;
import com.github.manasmods.tensura.ability.skill.extra.GodwolfSenseSkill;
import com.github.manasmods.tensura.ability.skill.extra.GravityDominationSkill;
import com.github.manasmods.tensura.ability.skill.extra.GravityManipulationSkill;
import com.github.manasmods.tensura.ability.skill.extra.HakiSkill;
import com.github.manasmods.tensura.ability.skill.extra.HeatWaveSkill;
import com.github.manasmods.tensura.ability.skill.extra.HeavenlyEyeSkill;
import com.github.manasmods.tensura.ability.skill.extra.HeroHakiSkill;
import com.github.manasmods.tensura.ability.skill.extra.InfiniteRegenerationSkill;
import com.github.manasmods.tensura.ability.skill.extra.LightningDominationSkill;
import com.github.manasmods.tensura.ability.skill.extra.LightningManipulationSkill;
import com.github.manasmods.tensura.ability.skill.extra.MagicAuraSkill;
import com.github.manasmods.tensura.ability.skill.extra.MagicDarknessTransformSkill;
import com.github.manasmods.tensura.ability.skill.extra.MagicEarthTransformSkill;
import com.github.manasmods.tensura.ability.skill.extra.MagicFlameTransformSkill;
import com.github.manasmods.tensura.ability.skill.extra.MagicJammingSkill;
import com.github.manasmods.tensura.ability.skill.extra.MagicLightTransformSkill;
import com.github.manasmods.tensura.ability.skill.extra.MagicSenseSkill;
import com.github.manasmods.tensura.ability.skill.extra.MagicSpaceTransformSkill;
import com.github.manasmods.tensura.ability.skill.extra.MagicWaterTransformSkill;
import com.github.manasmods.tensura.ability.skill.extra.MagicWindTransformSkill;
import com.github.manasmods.tensura.ability.skill.extra.MajestySkill;
import com.github.manasmods.tensura.ability.skill.extra.MolecularManipulationSkill;
import com.github.manasmods.tensura.ability.skill.extra.MortalFearSkill;
import com.github.manasmods.tensura.ability.skill.extra.MultilayerBarrierSkill;
import com.github.manasmods.tensura.ability.skill.extra.SacredHakiSkill;
import com.github.manasmods.tensura.ability.skill.extra.SageSkill;
import com.github.manasmods.tensura.ability.skill.extra.SenseHeatSourceSkill;
import com.github.manasmods.tensura.ability.skill.extra.SenseSoundwaveSkill;
import com.github.manasmods.tensura.ability.skill.extra.ShadowMotionSkill;
import com.github.manasmods.tensura.ability.skill.extra.SnakeEyeSkill;
import com.github.manasmods.tensura.ability.skill.extra.SoundDominationSkill;
import com.github.manasmods.tensura.ability.skill.extra.SoundManipulationSkill;
import com.github.manasmods.tensura.ability.skill.extra.SpatialDominationSkill;
import com.github.manasmods.tensura.ability.skill.extra.SpatialManipulationSkill;
import com.github.manasmods.tensura.ability.skill.extra.SpatialMotionSkill;
import com.github.manasmods.tensura.ability.skill.extra.SteelStrengthSkill;
import com.github.manasmods.tensura.ability.skill.extra.StickySteelThreadSkill;
import com.github.manasmods.tensura.ability.skill.extra.StrengthenBodySkill;
import com.github.manasmods.tensura.ability.skill.extra.ThoughtAccelerationSkill;
import com.github.manasmods.tensura.ability.skill.extra.UltraInstinctSkill;
import com.github.manasmods.tensura.ability.skill.extra.UltraspeedRegenerationSkill;
import com.github.manasmods.tensura.ability.skill.extra.UniversalPerceptionSkill;
import com.github.manasmods.tensura.ability.skill.extra.WaterDominationSkill;
import com.github.manasmods.tensura.ability.skill.extra.WaterManipulationSkill;
import com.github.manasmods.tensura.ability.skill.extra.WeatherDominationSkill;
import com.github.manasmods.tensura.ability.skill.extra.WeatherManipulationSkill;
import com.github.manasmods.tensura.ability.skill.extra.WindDominationSkill;
import com.github.manasmods.tensura.ability.skill.extra.WindManipulationSkill;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

public class ExtraSkills {
   private static final DeferredRegister<ManasSkill> registry = DeferredRegister.create(SkillAPI.getSkillRegistryKey(), "tensura");
   public static final RegistryObject<AllSeeingEyeSkill> ALL_SEEING_EYE;
   public static final RegistryObject<AnalyticalAppraisalSkill> ANALYTICAL_APPRAISAL;
   public static final RegistryObject<BlackFlameSkill> BLACK_FLAME;
   public static final RegistryObject<BlackLightningSkill> BLACK_LIGHTNING;
   public static final RegistryObject<BodyDoubleSkill> BODY_DOUBLE;
   public static final RegistryObject<ChantAnnulmentSkill> CHANT_ANNULMENT;
   public static final RegistryObject<DangerSenseSkill> DANGER_SENSE;
   public static final RegistryObject<DemonLordHakiSkill> DEMON_LORD_HAKI;
   public static final RegistryObject<EarthDominationSkill> EARTH_DOMINATION;
   public static final RegistryObject<EarthManipulationSkill> EARTH_MANIPULATION;
   public static final RegistryObject<FlameDominationSkill> FLAME_DOMINATION;
   public static final RegistryObject<FlameManipulationSkill> FLAME_MANIPULATION;
   public static final RegistryObject<GodwolfSenseSkill> GODWOLF_SENSE;
   public static final RegistryObject<GravityDominationSkill> GRAVITY_DOMINATION;
   public static final RegistryObject<GravityManipulationSkill> GRAVITY_MANIPULATION;
   public static final RegistryObject<HakiSkill> HAKI;
   public static final RegistryObject<HeatWaveSkill> HEAT_WAVE;
   public static final RegistryObject<HeavenlyEyeSkill> HEAVENLY_EYE;
   public static final RegistryObject<HeroHakiSkill> HERO_HAKI;
   public static final RegistryObject<InfiniteRegenerationSkill> INFINITE_REGENERATION;
   public static final RegistryObject<LightningDominationSkill> LIGHTNING_DOMINATION;
   public static final RegistryObject<LightningManipulationSkill> LIGHTNING_MANIPULATION;
   public static final RegistryObject<MagicAuraSkill> MAGIC_AURA;
   public static final RegistryObject<MagicDarknessTransformSkill> MAGIC_DARKNESS_TRANSFORM;
   public static final RegistryObject<MagicEarthTransformSkill> MAGIC_EARTH_TRANSFORM;
   public static final RegistryObject<MagicFlameTransformSkill> MAGIC_FLAME_TRANSFORM;
   public static final RegistryObject<MagicJammingSkill> MAGIC_JAMMING;
   public static final RegistryObject<MagicLightTransformSkill> MAGIC_LIGHT_TRANSFORM;
   public static final RegistryObject<MagicSenseSkill> MAGIC_SENSE;
   public static final RegistryObject<MagicSpaceTransformSkill> MAGIC_SPACE_TRANSFORM;
   public static final RegistryObject<MagicWaterTransformSkill> MAGIC_WATER_TRANSFORM;
   public static final RegistryObject<MagicWindTransformSkill> MAGIC_WIND_TRANSFORM;
   public static final RegistryObject<MajestySkill> MAJESTY;
   public static final RegistryObject<MolecularManipulationSkill> MOLECULAR_MANIPULATION;
   public static final RegistryObject<MortalFearSkill> MORTAL_FEAR;
   public static final RegistryObject<MultilayerBarrierSkill> MULTILAYER_BARRIER;
   public static final RegistryObject<SacredHakiSkill> SACRED_HAKI;
   public static final RegistryObject<SageSkill> SAGE;
   public static final RegistryObject<SenseHeatSourceSkill> SENSE_HEAT_SOURCE;
   public static final RegistryObject<SenseSoundwaveSkill> SENSE_SOUNDWAVE;
   public static final RegistryObject<ShadowMotionSkill> SHADOW_MOTION;
   public static final RegistryObject<SnakeEyeSkill> SNAKE_EYE;
   public static final RegistryObject<SoundDominationSkill> SOUND_DOMINATION;
   public static final RegistryObject<SoundManipulationSkill> SOUND_MANIPULATION;
   public static final RegistryObject<SpatialDominationSkill> SPATIAL_DOMINATION;
   public static final RegistryObject<SpatialManipulationSkill> SPATIAL_MANIPULATION;
   public static final RegistryObject<SpatialMotionSkill> SPATIAL_MOTION;
   public static final RegistryObject<StickySteelThreadSkill> STICKY_STEEL_THREAD;
   public static final RegistryObject<SteelStrengthSkill> STEEL_STRENGTH;
   public static final RegistryObject<StrengthenBodySkill> STRENGTHEN_BODY;
   public static final RegistryObject<ThoughtAccelerationSkill> THOUGHT_ACCELERATION;
   public static final RegistryObject<UltraInstinctSkill> ULTRA_INSTINCT;
   public static final RegistryObject<UltraspeedRegenerationSkill> ULTRASPEED_REGENERATION;
   public static final RegistryObject<UniversalPerceptionSkill> UNIVERSAL_PERCEPTION;
   public static final RegistryObject<WaterDominationSkill> WATER_DOMINATION;
   public static final RegistryObject<WaterManipulationSkill> WATER_MANIPULATION;
   public static final RegistryObject<WindDominationSkill> WIND_DOMINATION;
   public static final RegistryObject<WindManipulationSkill> WIND_MANIPULATION;
   public static final RegistryObject<WeatherDominationSkill> WEATHER_DOMINATION;
   public static final RegistryObject<WeatherManipulationSkill> WEATHER_MANIPULATION;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      ALL_SEEING_EYE = registry.register("all_seeing_eye", AllSeeingEyeSkill::new);
      ANALYTICAL_APPRAISAL = registry.register("analytical_appraisal", AnalyticalAppraisalSkill::new);
      BLACK_FLAME = registry.register("black_flame", BlackFlameSkill::new);
      BLACK_LIGHTNING = registry.register("black_lightning", BlackLightningSkill::new);
      BODY_DOUBLE = registry.register("body_double", BodyDoubleSkill::new);
      CHANT_ANNULMENT = registry.register("chant_annulment", ChantAnnulmentSkill::new);
      DANGER_SENSE = registry.register("danger_sense", DangerSenseSkill::new);
      DEMON_LORD_HAKI = registry.register("demon_lord_haki", DemonLordHakiSkill::new);
      EARTH_DOMINATION = registry.register("earth_domination", EarthDominationSkill::new);
      EARTH_MANIPULATION = registry.register("earth_manipulation", EarthManipulationSkill::new);
      FLAME_DOMINATION = registry.register("flame_domination", FlameDominationSkill::new);
      FLAME_MANIPULATION = registry.register("flame_manipulation", FlameManipulationSkill::new);
      GODWOLF_SENSE = registry.register("godwolf_sense", GodwolfSenseSkill::new);
      GRAVITY_DOMINATION = registry.register("gravity_domination", GravityDominationSkill::new);
      GRAVITY_MANIPULATION = registry.register("gravity_manipulation", GravityManipulationSkill::new);
      HAKI = registry.register("haki", HakiSkill::new);
      HEAT_WAVE = registry.register("heat_wave", HeatWaveSkill::new);
      HEAVENLY_EYE = registry.register("heavenly_eye", HeavenlyEyeSkill::new);
      HERO_HAKI = registry.register("hero_haki", HeroHakiSkill::new);
      INFINITE_REGENERATION = registry.register("infinite_regeneration", InfiniteRegenerationSkill::new);
      LIGHTNING_DOMINATION = registry.register("lightning_domination", LightningDominationSkill::new);
      LIGHTNING_MANIPULATION = registry.register("lightning_manipulation", LightningManipulationSkill::new);
      MAGIC_AURA = registry.register("magic_aura", MagicAuraSkill::new);
      MAGIC_DARKNESS_TRANSFORM = registry.register("magic_darkness_transform", MagicDarknessTransformSkill::new);
      MAGIC_EARTH_TRANSFORM = registry.register("magic_earth_transform", MagicEarthTransformSkill::new);
      MAGIC_FLAME_TRANSFORM = registry.register("magic_flame_transform", MagicFlameTransformSkill::new);
      MAGIC_JAMMING = registry.register("magic_jamming", MagicJammingSkill::new);
      MAGIC_LIGHT_TRANSFORM = registry.register("magic_light_transform", MagicLightTransformSkill::new);
      MAGIC_SENSE = registry.register("magic_sense", MagicSenseSkill::new);
      MAGIC_SPACE_TRANSFORM = registry.register("magic_space_transform", MagicSpaceTransformSkill::new);
      MAGIC_WATER_TRANSFORM = registry.register("magic_water_transform", MagicWaterTransformSkill::new);
      MAGIC_WIND_TRANSFORM = registry.register("magic_wind_transform", MagicWindTransformSkill::new);
      MAJESTY = registry.register("majesty", MajestySkill::new);
      MOLECULAR_MANIPULATION = registry.register("molecular_manipulation", MolecularManipulationSkill::new);
      MORTAL_FEAR = registry.register("mortal_fear", MortalFearSkill::new);
      MULTILAYER_BARRIER = registry.register("multilayer_barrier", MultilayerBarrierSkill::new);
      SACRED_HAKI = registry.register("sacred_haki", SacredHakiSkill::new);
      SAGE = registry.register("sage", SageSkill::new);
      SENSE_HEAT_SOURCE = registry.register("sense_heat_source", SenseHeatSourceSkill::new);
      SENSE_SOUNDWAVE = registry.register("sense_soundwave", SenseSoundwaveSkill::new);
      SHADOW_MOTION = registry.register("shadow_motion", ShadowMotionSkill::new);
      SNAKE_EYE = registry.register("snake_eye", SnakeEyeSkill::new);
      SOUND_DOMINATION = registry.register("sound_domination", SoundDominationSkill::new);
      SOUND_MANIPULATION = registry.register("sound_manipulation", SoundManipulationSkill::new);
      SPATIAL_DOMINATION = registry.register("spatial_domination", SpatialDominationSkill::new);
      SPATIAL_MANIPULATION = registry.register("spatial_manipulation", SpatialManipulationSkill::new);
      SPATIAL_MOTION = registry.register("spatial_motion", SpatialMotionSkill::new);
      STICKY_STEEL_THREAD = registry.register("sticky_steel_thread", StickySteelThreadSkill::new);
      STEEL_STRENGTH = registry.register("steel_strength", SteelStrengthSkill::new);
      STRENGTHEN_BODY = registry.register("strengthen_body", StrengthenBodySkill::new);
      THOUGHT_ACCELERATION = registry.register("thought_acceleration", ThoughtAccelerationSkill::new);
      ULTRA_INSTINCT = registry.register("ultra_instinct", UltraInstinctSkill::new);
      ULTRASPEED_REGENERATION = registry.register("ultraspeed_regeneration", UltraspeedRegenerationSkill::new);
      UNIVERSAL_PERCEPTION = registry.register("universal_perception", UniversalPerceptionSkill::new);
      WATER_DOMINATION = registry.register("water_domination", WaterDominationSkill::new);
      WATER_MANIPULATION = registry.register("water_manipulation", WaterManipulationSkill::new);
      WIND_DOMINATION = registry.register("wind_domination", WindDominationSkill::new);
      WIND_MANIPULATION = registry.register("wind_manipulation", WindManipulationSkill::new);
      WEATHER_DOMINATION = registry.register("weather_domination", WeatherDominationSkill::new);
      WEATHER_MANIPULATION = registry.register("weather_manipulation", WeatherManipulationSkill::new);
   }
}
