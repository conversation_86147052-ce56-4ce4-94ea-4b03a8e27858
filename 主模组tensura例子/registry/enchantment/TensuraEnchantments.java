package com.github.manasmods.tensura.registry.enchantment;

import com.github.manasmods.tensura.enchantment.BarrierPiercingEnchantment;
import com.github.manasmods.tensura.enchantment.BreathingSupportEnchantment;
import com.github.manasmods.tensura.enchantment.CrushingEnchantment;
import com.github.manasmods.tensura.enchantment.DeadEndRainbowEnchantment;
import com.github.manasmods.tensura.enchantment.ElementalBoostEnchantment;
import com.github.manasmods.tensura.enchantment.ElementalResistanceEnchantment;
import com.github.manasmods.tensura.enchantment.EnergyStealEnchantment;
import com.github.manasmods.tensura.enchantment.EngravingEnchantment;
import com.github.manasmods.tensura.enchantment.HolyCoatEnchantment;
import com.github.manasmods.tensura.enchantment.HolyWeaponEnchantment;
import com.github.manasmods.tensura.enchantment.MagicInterferenceEnchantment;
import com.github.manasmods.tensura.enchantment.MagicWeaponEnchantment;
import com.github.manasmods.tensura.enchantment.SeveranceEnchantment;
import com.github.manasmods.tensura.enchantment.SlottingEnchantment;
import com.github.manasmods.tensura.enchantment.SoulEaterEnchantment;
import com.github.manasmods.tensura.enchantment.SturdyEnchantment;
import com.github.manasmods.tensura.enchantment.SwiftEnchantment;
import com.github.manasmods.tensura.enchantment.TsukumogamiEnchantment;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraEnchantments {
   private static final DeferredRegister<Enchantment> registry;
   public static final RegistryObject<EngravingEnchantment> BARRIER_PIERCING;
   public static final RegistryObject<EngravingEnchantment> BREATHING_SUPPORT;
   public static final RegistryObject<EngravingEnchantment> CRUSHING;
   public static final RegistryObject<EngravingEnchantment> ELEMENTAL_BOOST;
   public static final RegistryObject<EngravingEnchantment> ELEMENTAL_RESISTANCE;
   public static final RegistryObject<EngravingEnchantment> ENERGY_STEAL;
   public static final RegistryObject<EngravingEnchantment> HOLY_WEAPON;
   public static final RegistryObject<EngravingEnchantment> MAGIC_WEAPON;
   public static final RegistryObject<EngravingEnchantment> SEVERANCE;
   public static final RegistryObject<EngravingEnchantment> SLOTTING;
   public static final RegistryObject<EngravingEnchantment> SOUL_EATER;
   public static final RegistryObject<EngravingEnchantment> STURDY;
   public static final RegistryObject<EngravingEnchantment> SWIFT;
   public static final RegistryObject<EngravingEnchantment> DEAD_END_RAINBOW;
   public static final RegistryObject<EngravingEnchantment> HOLY_COAT;
   public static final RegistryObject<EngravingEnchantment> MAGIC_INTERFERENCE;
   public static final RegistryObject<EngravingEnchantment> TSUKUMOGAMI;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.ENCHANTMENTS, "tensura");
      BARRIER_PIERCING = registry.register("barrier_piercing", BarrierPiercingEnchantment::new);
      BREATHING_SUPPORT = registry.register("breathing_support", BreathingSupportEnchantment::new);
      CRUSHING = registry.register("crushing", CrushingEnchantment::new);
      ELEMENTAL_BOOST = registry.register("elemental_boost", ElementalBoostEnchantment::new);
      ELEMENTAL_RESISTANCE = registry.register("elemental_resistance", ElementalResistanceEnchantment::new);
      ENERGY_STEAL = registry.register("energy_steal", EnergyStealEnchantment::new);
      HOLY_WEAPON = registry.register("holy_weapon", HolyWeaponEnchantment::new);
      MAGIC_WEAPON = registry.register("magic_weapon", MagicWeaponEnchantment::new);
      SEVERANCE = registry.register("severance", SeveranceEnchantment::new);
      SLOTTING = registry.register("slotting", SlottingEnchantment::new);
      SOUL_EATER = registry.register("soul_eater", SoulEaterEnchantment::new);
      STURDY = registry.register("sturdy", SturdyEnchantment::new);
      SWIFT = registry.register("swift", SwiftEnchantment::new);
      DEAD_END_RAINBOW = registry.register("dead_end_rainbow", DeadEndRainbowEnchantment::new);
      HOLY_COAT = registry.register("holy_coat", HolyCoatEnchantment::new);
      MAGIC_INTERFERENCE = registry.register("magic_interference", MagicInterferenceEnchantment::new);
      TSUKUMOGAMI = registry.register("tsukumogami", TsukumogamiEnchantment::new);
   }
}
