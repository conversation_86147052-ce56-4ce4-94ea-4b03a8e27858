package com.github.manasmods.tensura.registry;

import com.github.manasmods.tensura.data.TensuraTags;
import java.util.Iterator;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.core.Registry;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.stats.StatType;
import net.minecraft.stats.StatsCounter;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class TensuraStats {
   private static final DeferredRegister<StatType<?>> registry;
   public static final RegistryObject<StatType<EntityType<?>>> BOSS_KILLED;

   private static ResourceLocation makeCustomStat(String pKey) {
      RegistryObject<StatType<ResourceLocation>> register = registry.register(pKey, () -> {
         return new StatType(Registry.f_122832_);
      });
      return register.getId();
   }

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   public static int getBossKilled(Player player) {
      Object statsCounter;
      if (player instanceof LocalPlayer) {
         LocalPlayer localPlayer = (LocalPlayer)player;
         statsCounter = localPlayer.m_108630_();
      } else {
         if (!(player instanceof ServerPlayer)) {
            return 0;
         }

         ServerPlayer serverPlayer = (ServerPlayer)player;
         statsCounter = serverPlayer.m_8951_();
      }

      int i = 0;
      Iterator var4 = Registry.f_122826_.iterator();

      while(var4.hasNext()) {
         EntityType<?> type = (EntityType)var4.next();
         if (type.m_204039_(TensuraTags.EntityTypes.HERO_BOSS) && ((StatsCounter)statsCounter).m_13015_(((StatType)BOSS_KILLED.get()).m_12902_(type)) > 0) {
            ++i;
         }
      }

      return i;
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.STAT_TYPES, "tensura");
      BOSS_KILLED = registry.register("boss_killed", () -> {
         return new StatType(Registry.f_122826_);
      });
   }
}
