package com.github.manasmods.tensura.registry.structure;

import com.github.manasmods.tensura.world.structure.JigsawMinHeightStructure;
import net.minecraft.core.Registry;
import net.minecraft.world.level.levelgen.structure.StructureType;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;

public class TensuraStructureTypes {
   public static final DeferredRegister<StructureType<?>> registry;
   public static final RegistryObject<StructureType<JigsawMinHeightStructure>> JIGSAW_MIN_HEIGHT;

   public static void init(IEventBus bus) {
      registry.register(bus);
   }

   static {
      registry = DeferredRegister.create(Registry.f_235739_, "tensura");
      JIGSAW_MIN_HEIGHT = registry.register("jigsaw_min_height", () -> {
         return () -> {
            return JigsawMinHeightStructure.CODEC;
         };
      });
   }
}
