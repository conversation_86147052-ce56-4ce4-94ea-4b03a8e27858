package com.github.manasmods.tensura.registry.items;

import com.github.manasmods.manascore.api.data.gen.annotation.GenerateItemModels;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateItemModels.SingleTextureModel;
import com.github.manasmods.tensura.item.TensuraCreativeTab;
import com.github.manasmods.tensura.item.custom.HolyWaterItem;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.item.food.HolyMilkBucketItem;
import com.github.manasmods.tensura.item.food.HolyMilkItem;
import com.github.manasmods.tensura.item.food.MagicBottleItem;
import com.github.manasmods.tensura.item.food.SimpleFoodItem;
import com.github.manasmods.tensura.item.food.TensuraFoodProperties;
import com.github.manasmods.tensura.util.TensuraRarity;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.MilkBucketItem;
import net.minecraft.world.item.Rarity;
import net.minecraft.world.item.Item.Properties;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

@GenerateItemModels
public class TensuraConsumableItems {
   private static final DeferredRegister<Item> registry;
   @SingleTextureModel
   public static final RegistryObject<Item> DUBIOUS_FOOD;
   @SingleTextureModel
   public static final RegistryObject<Item> RAW_BLADE_TIGER_MEAT;
   @SingleTextureModel
   public static final RegistryObject<Item> BLADE_TIGER_STEAK;
   @SingleTextureModel
   public static final RegistryObject<Item> RAW_ARMOURSAURUS_MEAT;
   @SingleTextureModel
   public static final RegistryObject<Item> COOKED_ARMOURSAURUS_MEAT;
   @SingleTextureModel
   public static final RegistryObject<Item> CHILLED_SLIME;
   @SingleTextureModel
   public static final RegistryObject<Item> BUCKET_OF_BULLDEER_MILK;
   @SingleTextureModel
   public static final RegistryObject<Item> BULLDEER_BEEF;
   @SingleTextureModel
   public static final RegistryObject<Item> BULLDEER_STEAK;
   @SingleTextureModel
   public static final RegistryObject<Item> RAW_CHARYBDIS_MEAT;
   @SingleTextureModel
   public static final RegistryObject<Item> COOKED_CHARYBDIS_MEAT;
   @SingleTextureModel
   public static final RegistryObject<Item> GIANT_ANT_LEG;
   @SingleTextureModel
   public static final RegistryObject<Item> COOKED_GIANT_ANT_LEG;
   @SingleTextureModel
   public static final RegistryObject<Item> RAW_GIANT_BAT_MEAT;
   @SingleTextureModel
   public static final RegistryObject<Item> COOKED_GIANT_BAT_MEAT;
   @SingleTextureModel
   public static final RegistryObject<Item> KNIGHT_SPIDER_LEG;
   @SingleTextureModel
   public static final RegistryObject<Item> COOKED_KNIGHT_SPIDER_LEG;
   @SingleTextureModel
   public static final RegistryObject<Item> RAW_MEGALODON_MEAT;
   @SingleTextureModel
   public static final RegistryObject<Item> COOKED_MEGALODON_MEAT;
   @SingleTextureModel
   public static final RegistryObject<Item> RAW_SERPENT_MEAT;
   @SingleTextureModel
   public static final RegistryObject<Item> COOKED_SERPENT_MEAT;
   @SingleTextureModel
   public static final RegistryObject<Item> RAW_SPEAR_TORO_MEAT;
   @SingleTextureModel
   public static final RegistryObject<Item> COOKED_SPEAR_TORO_MEAT;
   @SingleTextureModel
   public static final RegistryObject<Item> SPEAR_TORO_FIN;
   @SingleTextureModel
   public static final RegistryObject<Item> COOKED_SPEAR_TORO_FIN;
   @SingleTextureModel
   public static final RegistryObject<Item> RAW_SISSIE_MEAT;
   @SingleTextureModel
   public static final RegistryObject<Item> COOKED_SISSIE_MEAT;
   @SingleTextureModel
   public static final RegistryObject<Item> SISSIE_FIN;
   @SingleTextureModel
   public static final RegistryObject<Item> COOKED_SISSIE_FIN;
   @SingleTextureModel
   public static final RegistryObject<Item> SILVER_APPLE;
   @SingleTextureModel
   public static final RegistryObject<Item> ENCHANTED_SILVER_APPLE;
   @SingleTextureModel
   public static final RegistryObject<Item> MAGIC_BOTTLE;
   @SingleTextureModel
   public static final RegistryObject<Item> WATER_MAGIC_BOTTLE;
   @SingleTextureModel
   public static final RegistryObject<Item> VACUUMED_WATER_MAGIC_BOTTLE;
   @SingleTextureModel
   public static final RegistryObject<Item> LOW_POTION;
   @SingleTextureModel
   public static final RegistryObject<Item> HIGH_POTION;
   @SingleTextureModel
   public static final RegistryObject<Item> FULL_POTION;
   @SingleTextureModel
   public static final RegistryObject<Item> REVIVAL_ELIXER;
   @SingleTextureModel
   public static final RegistryObject<Item> HOLY_WATER;
   @SingleTextureModel
   public static final RegistryObject<Item> GREATER_HOLY_WATER;
   @SingleTextureModel
   public static final RegistryObject<Item> HOLY_MILK;
   @SingleTextureModel
   public static final RegistryObject<Item> HOLY_MILK_BUCKET;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.ITEMS, "tensura");
      DUBIOUS_FOOD = registry.register("dubious_food", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.DUBIOUS_FOOD);
      });
      RAW_BLADE_TIGER_MEAT = registry.register("raw_blade_tiger_meat", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.RAW_BLADE_TIGER_MEAT);
      });
      BLADE_TIGER_STEAK = registry.register("blade_tiger_steak", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.BLADE_TIGER_STEAK);
      });
      RAW_ARMOURSAURUS_MEAT = registry.register("raw_armoursaurus_meat", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.RAW_ARMOURSAURUS_MEAT);
      });
      COOKED_ARMOURSAURUS_MEAT = registry.register("cooked_armoursaurus_meat", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.COOKED_ARMOURSAURUS_MEAT);
      });
      CHILLED_SLIME = registry.register("chilled_slime", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.CONSUMABLES).m_41489_(TensuraFoodProperties.CHILLED_SLIME));
      });
      BUCKET_OF_BULLDEER_MILK = registry.register("bucket_of_bulldeer_milk", () -> {
         return new MilkBucketItem((new Properties()).m_41491_(TensuraCreativeTab.CONSUMABLES).m_41487_(1));
      });
      BULLDEER_BEEF = registry.register("bulldeer_beef", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.BULLDEER_BEEF);
      });
      BULLDEER_STEAK = registry.register("bulldeer_steak", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.BULLDEER_STEAK);
      });
      RAW_CHARYBDIS_MEAT = registry.register("raw_charybdis_meat", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.RAW_CHARYBDIS_MEAT);
      });
      COOKED_CHARYBDIS_MEAT = registry.register("cooked_charybdis_meat", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.COOKED_CHARYBDIS_MEAT);
      });
      GIANT_ANT_LEG = registry.register("giant_ant_leg", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.RAW_GIANT_ANT_LEG);
      });
      COOKED_GIANT_ANT_LEG = registry.register("cooked_giant_ant_leg", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.COOKED_GIANT_ANT_LEG);
      });
      RAW_GIANT_BAT_MEAT = registry.register("raw_giant_bat_meat", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.RAW_GIANT_BAT_MEAT);
      });
      COOKED_GIANT_BAT_MEAT = registry.register("cooked_giant_bat_meat", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.COOKED_GIANT_BAT_MEAT);
      });
      KNIGHT_SPIDER_LEG = registry.register("knight_spider_leg", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.RAW_KNIGHT_SPIDER_LEG);
      });
      COOKED_KNIGHT_SPIDER_LEG = registry.register("cooked_knight_spider_leg", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.COOKED_KNIGHT_SPIDER_LEG);
      });
      RAW_MEGALODON_MEAT = registry.register("raw_megalodon_meat", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.RAW_MEGALODON_MEAT);
      });
      COOKED_MEGALODON_MEAT = registry.register("cooked_megalodon_meat", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.COOKED_MEGALODON_MEAT);
      });
      RAW_SERPENT_MEAT = registry.register("raw_serpent_meat", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.RAW_SERPENT_MEAT);
      });
      COOKED_SERPENT_MEAT = registry.register("cooked_serpent_meat", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.COOKED_SERPENT_MEAT);
      });
      RAW_SPEAR_TORO_MEAT = registry.register("raw_spear_toro_meat", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.RAW_SPEAR_TORO_MEAT);
      });
      COOKED_SPEAR_TORO_MEAT = registry.register("cooked_spear_toro_meat", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.COOKED_SPEAR_TORO_MEAT);
      });
      SPEAR_TORO_FIN = registry.register("spear_toro_fin", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.SPEAR_TORO_FIN);
      });
      COOKED_SPEAR_TORO_FIN = registry.register("cooked_spear_toro_fin", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.COOKED_SPEAR_TORO_FIN);
      });
      RAW_SISSIE_MEAT = registry.register("raw_sissie_meat", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.RAW_SISSIE_MEAT);
      });
      COOKED_SISSIE_MEAT = registry.register("cooked_sissie_meat", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.COOKED_SISSIE_MEAT);
      });
      SISSIE_FIN = registry.register("sissie_fin", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.SISSIE_FIN);
      });
      COOKED_SISSIE_FIN = registry.register("cooked_sissie_fin", () -> {
         return new SimpleFoodItem(TensuraFoodProperties.COOKED_SISSIE_FIN);
      });
      SILVER_APPLE = registry.register("silver_apple", () -> {
         return new SimpleFoodItem((new Properties()).m_41491_(TensuraCreativeTab.CONSUMABLES).m_41497_(Rarity.RARE).m_41489_(TensuraFoodProperties.SILVER_APPLE), false);
      });
      ENCHANTED_SILVER_APPLE = registry.register("enchanted_silver_apple", () -> {
         return new SimpleFoodItem((new Properties()).m_41491_(TensuraCreativeTab.CONSUMABLES).m_41497_(Rarity.EPIC).m_41489_(TensuraFoodProperties.ENCHANTED_SILVER_APPLE), true);
      });
      MAGIC_BOTTLE = registry.register("magic_bottle", () -> {
         return new MagicBottleItem((new Properties()).m_41491_(TensuraCreativeTab.CONSUMABLES));
      });
      WATER_MAGIC_BOTTLE = registry.register("magic_bottle_of_water", () -> {
         return new HealingPotionItem(0, 0.0F, 0.0F, 0.0F);
      });
      VACUUMED_WATER_MAGIC_BOTTLE = registry.register("vacuumed_magic_bottle_of_water", () -> {
         return new HealingPotionItem(0, 0.0F, 0.0F, 0.001F);
      });
      LOW_POTION = registry.register("low_potion", () -> {
         return new HealingPotionItem(1, 1.0F, 0.33F, 0.05F);
      });
      HIGH_POTION = registry.register("high_potion", () -> {
         return new HealingPotionItem(1, 1.0F, 0.66F, 0.1F);
      });
      FULL_POTION = registry.register("full_potion", () -> {
         return new HealingPotionItem(1, 1.0F, 0.99F, 0.2F);
      });
      REVIVAL_ELIXER = registry.register("revival_elixer", () -> {
         return new HealingPotionItem(1, 1.0F, 1.0F, 0.3F);
      });
      HOLY_WATER = registry.register("holy_water", () -> {
         return new HolyWaterItem((new Properties()).m_41491_(TensuraCreativeTab.CONSUMABLES).m_41497_(TensuraRarity.UNIQUE).m_41486_().m_41487_(16), 0);
      });
      GREATER_HOLY_WATER = registry.register("greater_holy_water", () -> {
         return new HolyWaterItem((new Properties()).m_41491_(TensuraCreativeTab.CONSUMABLES).m_41497_(TensuraRarity.UNIQUE).m_41486_().m_41487_(16), 1);
      });
      HOLY_MILK = registry.register("holy_milk", () -> {
         return new HolyMilkItem((new Properties()).m_41491_(TensuraCreativeTab.CONSUMABLES).m_41497_(TensuraRarity.UNIQUE).m_41486_().m_41487_(1));
      });
      HOLY_MILK_BUCKET = registry.register("holy_milk_bucket", () -> {
         return new HolyMilkBucketItem((new Properties()).m_41491_(TensuraCreativeTab.CONSUMABLES).m_41497_(TensuraRarity.UNIQUE).m_41486_().m_41487_(1));
      });
   }
}
