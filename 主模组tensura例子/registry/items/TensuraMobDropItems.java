package com.github.manasmods.tensura.registry.items;

import com.github.manasmods.manascore.api.data.gen.annotation.GenerateItemModels;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateItemModels.SingleHandheldTextureModel;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateItemModels.SingleTextureModel;
import com.github.manasmods.tensura.item.TensuraCreativeTab;
import com.github.manasmods.tensura.item.custom.UnicornHornItem;
import com.github.manasmods.tensura.item.food.SimpleFoodItem;
import com.github.manasmods.tensura.item.food.TensuraFoodProperties;
import java.util.List;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.level.Level;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@GenerateItemModels
public class TensuraMobDropItems {
   private static final DeferredRegister<Item> registry;
   @SingleTextureModel
   public static final RegistryObject<Item> LOW_QUALITY_MAGIC_CRYSTAL;
   @SingleTextureModel
   public static final RegistryObject<Item> MEDIUM_QUALITY_MAGIC_CRYSTAL;
   public static final RegistryObject<Item> HIGH_QUALITY_MAGIC_CRYSTAL;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_D;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_C;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_B;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_A;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_SPECIAL_A;
   @SingleTextureModel
   public static final RegistryObject<Item> GIANT_BAT_WING;
   @SingleTextureModel
   public static final RegistryObject<Item> INVISIBLE_FEATHER;
   @SingleTextureModel
   public static final RegistryObject<Item> DRAGON_PEACOCK_FEATHER;
   @SingleTextureModel
   public static final RegistryObject<Item> HELL_MOTH_SILK;
   @SingleTextureModel
   public static final RegistryObject<Item> GEHENNA_MOTH_SILK;
   @SingleTextureModel
   public static final RegistryObject<Item> GIANT_ANT_CARAPACE;
   @SingleTextureModel
   public static final RegistryObject<Item> KNIGHT_SPIDER_CARAPACE;
   @SingleTextureModel
   public static final RegistryObject<Item> INSECTAR_CARAPACE;
   @SingleTextureModel
   public static final RegistryObject<Item> ARMOURSAURUS_SCALE;
   @SingleTextureModel
   public static final RegistryObject<Item> ARMOURSAURUS_SHELL;
   @SingleTextureModel
   public static final RegistryObject<Item> SERPENT_SCALE;
   @SingleTextureModel
   public static final RegistryObject<Item> CHARYBDIS_SCALE;
   @SingleTextureModel
   public static final RegistryObject<Item> CENTIPEDE_STINGER;
   @SingleTextureModel
   public static final RegistryObject<Item> SPIDER_FANG;
   @SingleHandheldTextureModel
   public static final RegistryObject<Item> BLADE_TIGER_TAIL;
   @SingleTextureModel
   public static final RegistryObject<Item> SLIME_CHUNK;
   @SingleTextureModel
   public static final RegistryObject<Item> SLIME_CORE;
   @SingleTextureModel
   public static final RegistryObject<Item> STICKY_THREAD;
   @SingleTextureModel
   public static final RegistryObject<Item> STEEL_THREAD;
   @SingleTextureModel
   public static final RegistryObject<Item> SISSIE_TOOTH;
   @SingleTextureModel
   public static final RegistryObject<Item> BEAST_HORN;
   @SingleTextureModel
   public static final RegistryObject<Item> UNICORN_HORN;
   @SingleTextureModel
   public static final RegistryObject<Item> DEMON_ESSENCE;
   @SingleTextureModel
   public static final RegistryObject<Item> DRAGON_ESSENCE;
   @SingleTextureModel
   public static final RegistryObject<Item> ELEMENTAL_ESSENCE;
   @SingleTextureModel
   public static final RegistryObject<Item> ROYAL_BLOOD;
   @SingleTextureModel
   public static final RegistryObject<Item> ZANE_BLOOD;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.ITEMS, "tensura");
      LOW_QUALITY_MAGIC_CRYSTAL = registry.register("low_quality_magic_crystal", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      MEDIUM_QUALITY_MAGIC_CRYSTAL = registry.register("medium_quality_magic_crystal", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS).m_41486_());
      });
      HIGH_QUALITY_MAGIC_CRYSTAL = registry.register("high_quality_magic_crystal", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS).m_41486_());
      });
      MONSTER_LEATHER_D = registry.register("monster_leather_d", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      MONSTER_LEATHER_C = registry.register("monster_leather_c", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      MONSTER_LEATHER_B = registry.register("monster_leather_b", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      MONSTER_LEATHER_A = registry.register("monster_leather_a", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      MONSTER_LEATHER_SPECIAL_A = registry.register("monster_leather_special_a", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      GIANT_BAT_WING = registry.register("giant_bat_wing", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      INVISIBLE_FEATHER = registry.register("invisible_feather", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      DRAGON_PEACOCK_FEATHER = registry.register("dragon_peacock_feather", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      HELL_MOTH_SILK = registry.register("hell_moth_silk", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS)) {
            public void m_7373_(@NotNull ItemStack pStack, @Nullable Level pLevel, @NotNull List<Component> pTooltipComponents, @NotNull TooltipFlag pIsAdvanced) {
               pTooltipComponents.add(Component.m_237115_("tooltip.tensura.coming_soon").m_130940_(ChatFormatting.RED));
            }
         };
      });
      GEHENNA_MOTH_SILK = registry.register("gehenna_moth_silk", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS)) {
            public void m_7373_(@NotNull ItemStack pStack, @Nullable Level pLevel, @NotNull List<Component> pTooltipComponents, @NotNull TooltipFlag pIsAdvanced) {
               pTooltipComponents.add(Component.m_237115_("tooltip.tensura.coming_soon").m_130940_(ChatFormatting.RED));
            }
         };
      });
      GIANT_ANT_CARAPACE = registry.register("giant_ant_carapace", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      KNIGHT_SPIDER_CARAPACE = registry.register("knight_spider_carapace", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      INSECTAR_CARAPACE = registry.register("insectar_carapace", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS)) {
            public void m_7373_(@NotNull ItemStack pStack, @Nullable Level pLevel, @NotNull List<Component> pTooltipComponents, @NotNull TooltipFlag pIsAdvanced) {
               pTooltipComponents.add(Component.m_237115_("tooltip.tensura.coming_soon").m_130940_(ChatFormatting.RED));
            }
         };
      });
      ARMOURSAURUS_SCALE = registry.register("armoursaurus_scale", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      ARMOURSAURUS_SHELL = registry.register("armoursaurus_shell", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      SERPENT_SCALE = registry.register("serpent_scale", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      CHARYBDIS_SCALE = registry.register("charybdis_scale", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS).m_41486_());
      });
      CENTIPEDE_STINGER = registry.register("centipede_stinger", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      SPIDER_FANG = registry.register("spider_fang", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      BLADE_TIGER_TAIL = registry.register("blade_tiger_tail", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      SLIME_CHUNK = registry.register("slime_chunk", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      SLIME_CORE = registry.register("slime_core", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      STICKY_THREAD = registry.register("sticky_thread", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      STEEL_THREAD = registry.register("steel_thread", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      SISSIE_TOOTH = registry.register("sissie_tooth", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      BEAST_HORN = registry.register("beast_horn", () -> {
         return new Item((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      UNICORN_HORN = registry.register("unicorn_horn", () -> {
         return new UnicornHornItem((new Properties()).m_41491_(TensuraCreativeTab.MOB_DROPS));
      });
      DEMON_ESSENCE = registry.register("demon_essence", () -> {
         return new SimpleFoodItem(TensuraCreativeTab.MOB_DROPS, TensuraFoodProperties.DEMON_ESSENCE);
      });
      DRAGON_ESSENCE = registry.register("dragon_essence", () -> {
         return new SimpleFoodItem(TensuraCreativeTab.MOB_DROPS, TensuraFoodProperties.DRAGON_ESSENCE);
      });
      ELEMENTAL_ESSENCE = registry.register("elemental_essence", () -> {
         return new SimpleFoodItem(TensuraCreativeTab.MOB_DROPS, TensuraFoodProperties.ELEMENTAL_ESSENCE);
      });
      ROYAL_BLOOD = registry.register("royal_blood", () -> {
         return new SimpleFoodItem(TensuraCreativeTab.MOB_DROPS, TensuraFoodProperties.ROYAL_BLOOD, true);
      });
      ZANE_BLOOD = registry.register("zane_blood", () -> {
         return new SimpleFoodItem(TensuraCreativeTab.MOB_DROPS, TensuraFoodProperties.ZANE_BLOOD, true);
      });
   }
}
