package com.github.manasmods.tensura.registry.items;

import com.github.manasmods.manascore.api.data.gen.annotation.GenerateItemModels;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateItemModels.SingleTextureModel;
import com.github.manasmods.tensura.item.TensuraArmourMaterials;
import com.github.manasmods.tensura.item.TensuraCreativeTab;
import com.github.manasmods.tensura.item.armor.AdamantiteArmorItem;
import com.github.manasmods.tensura.item.armor.AngryPierrotMaskItem;
import com.github.manasmods.tensura.item.armor.AntiMagicMaskItem;
import com.github.manasmods.tensura.item.armor.ArmoursaurusArmorItem;
import com.github.manasmods.tensura.item.armor.BatGliderItem;
import com.github.manasmods.tensura.item.armor.CharybdisScalemailItem;
import com.github.manasmods.tensura.item.armor.CrazyPierrotMaskItem;
import com.github.manasmods.tensura.item.armor.DarkSetArmorItem;
import com.github.manasmods.tensura.item.armor.HighMagisteelArmorItem;
import com.github.manasmods.tensura.item.armor.HihiirokaneArmorItem;
import com.github.manasmods.tensura.item.armor.HolyArmamentsArmorItem;
import com.github.manasmods.tensura.item.armor.LowMagisteelArmorItem;
import com.github.manasmods.tensura.item.armor.MithrilArmorItem;
import com.github.manasmods.tensura.item.armor.MonsterLeatherHelmetItem;
import com.github.manasmods.tensura.item.armor.MonsterLeatherSpecialAHelmetItem;
import com.github.manasmods.tensura.item.armor.OrichalcumArmorItem;
import com.github.manasmods.tensura.item.armor.PureMagisteelArmorItem;
import com.github.manasmods.tensura.item.armor.SimpleBootsItem;
import com.github.manasmods.tensura.item.armor.SimpleChestplateItem;
import com.github.manasmods.tensura.item.armor.SimpleDyableArmorItem;
import com.github.manasmods.tensura.item.armor.SimpleHelmetItem;
import com.github.manasmods.tensura.item.armor.SimpleLeggingsItem;
import com.github.manasmods.tensura.item.armor.TeardropPierrotMaskItem;
import com.github.manasmods.tensura.item.armor.WalkOnSnowBootsItem;
import com.github.manasmods.tensura.item.armor.WingedShoesItem;
import com.github.manasmods.tensura.item.armor.WonderPierrotMaskItem;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.Item.Properties;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

@GenerateItemModels
public class TensuraArmorItems {
   private static final DeferredRegister<Item> registry;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_D_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_D_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_D_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_D_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_C_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_C_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_C_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_C_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_B_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_B_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_B_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_B_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_A_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_A_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_A_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_A_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_SPECIAL_A_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_SPECIAL_A_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_SPECIAL_A_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> MONSTER_LEATHER_SPECIAL_A_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> BAT_GLIDER;
   @SingleTextureModel
   public static final RegistryObject<Item> WINGED_SHOES;
   @SingleTextureModel
   public static final RegistryObject<Item> SILVER_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> SILVER_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> SILVER_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> SILVER_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> ANT_CARAPACE_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> ANT_CARAPACE_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> ANT_CARAPACE_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> ANT_CARAPACE_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> SERPENT_SCALEMAIL_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> SERPENT_SCALEMAIL_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> SERPENT_SCALEMAIL_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> SERPENT_SCALEMAIL_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> KNIGHT_SPIDER_CARAPACE_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> KNIGHT_SPIDER_CARAPACE_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> KNIGHT_SPIDER_CARAPACE_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> KNIGHT_SPIDER_CARAPACE_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> LOW_MAGISTEEL_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> LOW_MAGISTEEL_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> LOW_MAGISTEEL_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> LOW_MAGISTEEL_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> ARMOURSAURUS_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> ARMOURSAURUS_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> ARMOURSAURUS_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> ARMOURSAURUS_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> ARMOURSAURUS_SCALEMAIL_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> ARMOURSAURUS_SCALEMAIL_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> ARMOURSAURUS_SCALEMAIL_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> ARMOURSAURUS_SCALEMAIL_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> HIGH_MAGISTEEL_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> HIGH_MAGISTEEL_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> HIGH_MAGISTEEL_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> HIGH_MAGISTEEL_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> CHARYBDIS_SCALEMAIL_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> CHARYBDIS_SCALEMAIL_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> CHARYBDIS_SCALEMAIL_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> CHARYBDIS_SCALEMAIL_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> MITHRIL_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> MITHRIL_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> MITHRIL_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> MITHRIL_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> ORICHALCUM_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> ORICHALCUM_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> ORICHALCUM_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> ORICHALCUM_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> PURE_MAGISTEEL_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> PURE_MAGISTEEL_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> PURE_MAGISTEEL_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> PURE_MAGISTEEL_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> ADAMANTITE_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> ADAMANTITE_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> ADAMANTITE_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> ADAMANTITE_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> HIHIIROKANE_HELMET;
   @SingleTextureModel
   public static final RegistryObject<Item> HIHIIROKANE_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> HIHIIROKANE_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> HIHIIROKANE_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> HOLY_ARMAMENTS_CHESTPLATE;
   @SingleTextureModel
   public static final RegistryObject<Item> HOLY_ARMAMENTS_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> HOLY_ARMAMENTS_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> ANTI_MAGIC_MASK;
   @SingleTextureModel
   public static final RegistryObject<Item> DARK_JACKET;
   @SingleTextureModel
   public static final RegistryObject<Item> DARK_LEGGINGS;
   @SingleTextureModel
   public static final RegistryObject<Item> DARK_BOOTS;
   @SingleTextureModel
   public static final RegistryObject<Item> CRAZY_PIERROT_MASK;
   @SingleTextureModel
   public static final RegistryObject<Item> ANGRY_PIERROT_MASK;
   @SingleTextureModel
   public static final RegistryObject<Item> WONDER_PIERROT_MASK;
   @SingleTextureModel
   public static final RegistryObject<Item> TEARY_PIERROT_MASK;

   public static void init(IEventBus modEventBus) {
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.ITEMS, "tensura");
      MONSTER_LEATHER_D_HELMET = registry.register("monster_leather_helmet_d", () -> {
         return new MonsterLeatherHelmetItem(TensuraArmourMaterials.MONSTER_LEATHER_D, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      MONSTER_LEATHER_D_CHESTPLATE = registry.register("monster_leather_chestplate_d", () -> {
         return new SimpleDyableArmorItem(TensuraArmourMaterials.MONSTER_LEATHER_D, EquipmentSlot.CHEST, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      MONSTER_LEATHER_D_LEGGINGS = registry.register("monster_leather_leggings_d", () -> {
         return new SimpleDyableArmorItem(TensuraArmourMaterials.MONSTER_LEATHER_D, EquipmentSlot.LEGS, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      MONSTER_LEATHER_D_BOOTS = registry.register("monster_leather_boots_d", () -> {
         return new WalkOnSnowBootsItem(TensuraArmourMaterials.MONSTER_LEATHER_D, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      MONSTER_LEATHER_C_HELMET = registry.register("monster_leather_helmet_c", () -> {
         return new MonsterLeatherHelmetItem(TensuraArmourMaterials.MONSTER_LEATHER_C, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      MONSTER_LEATHER_C_CHESTPLATE = registry.register("monster_leather_chestplate_c", () -> {
         return new SimpleDyableArmorItem(TensuraArmourMaterials.MONSTER_LEATHER_C, EquipmentSlot.CHEST, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      MONSTER_LEATHER_C_LEGGINGS = registry.register("monster_leather_leggings_c", () -> {
         return new SimpleDyableArmorItem(TensuraArmourMaterials.MONSTER_LEATHER_C, EquipmentSlot.LEGS, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      MONSTER_LEATHER_C_BOOTS = registry.register("monster_leather_boots_c", () -> {
         return new WalkOnSnowBootsItem(TensuraArmourMaterials.MONSTER_LEATHER_C, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      MONSTER_LEATHER_B_HELMET = registry.register("monster_leather_helmet_b", () -> {
         return new MonsterLeatherHelmetItem(TensuraArmourMaterials.MONSTER_LEATHER_B, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      MONSTER_LEATHER_B_CHESTPLATE = registry.register("monster_leather_chestplate_b", () -> {
         return new SimpleDyableArmorItem(TensuraArmourMaterials.MONSTER_LEATHER_B, EquipmentSlot.CHEST, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      MONSTER_LEATHER_B_LEGGINGS = registry.register("monster_leather_leggings_b", () -> {
         return new SimpleDyableArmorItem(TensuraArmourMaterials.MONSTER_LEATHER_B, EquipmentSlot.LEGS, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      MONSTER_LEATHER_B_BOOTS = registry.register("monster_leather_boots_b", () -> {
         return new WalkOnSnowBootsItem(TensuraArmourMaterials.MONSTER_LEATHER_B, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      MONSTER_LEATHER_A_HELMET = registry.register("monster_leather_helmet_a", () -> {
         return new MonsterLeatherHelmetItem(TensuraArmourMaterials.MONSTER_LEATHER_A, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      MONSTER_LEATHER_A_CHESTPLATE = registry.register("monster_leather_chestplate_a", () -> {
         return new SimpleDyableArmorItem(TensuraArmourMaterials.MONSTER_LEATHER_A, EquipmentSlot.CHEST, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      MONSTER_LEATHER_A_LEGGINGS = registry.register("monster_leather_leggings_a", () -> {
         return new SimpleDyableArmorItem(TensuraArmourMaterials.MONSTER_LEATHER_A, EquipmentSlot.LEGS, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      MONSTER_LEATHER_A_BOOTS = registry.register("monster_leather_boots_a", () -> {
         return new WalkOnSnowBootsItem(TensuraArmourMaterials.MONSTER_LEATHER_A, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      MONSTER_LEATHER_SPECIAL_A_HELMET = registry.register("monster_leather_helmet_special_a", () -> {
         return new MonsterLeatherSpecialAHelmetItem(TensuraArmourMaterials.MONSTER_LEATHER_SPECIAL_A, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR).m_41486_());
      });
      MONSTER_LEATHER_SPECIAL_A_CHESTPLATE = registry.register("monster_leather_chestplate_special_a", () -> {
         return new SimpleDyableArmorItem(TensuraArmourMaterials.MONSTER_LEATHER_SPECIAL_A, EquipmentSlot.CHEST, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR).m_41486_());
      });
      MONSTER_LEATHER_SPECIAL_A_LEGGINGS = registry.register("monster_leather_leggings_special_a", () -> {
         return new SimpleDyableArmorItem(TensuraArmourMaterials.MONSTER_LEATHER_SPECIAL_A, EquipmentSlot.LEGS, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR).m_41486_());
      });
      MONSTER_LEATHER_SPECIAL_A_BOOTS = registry.register("monster_leather_boots_special_a", () -> {
         return new WalkOnSnowBootsItem(TensuraArmourMaterials.MONSTER_LEATHER_SPECIAL_A, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR).m_41486_());
      });
      BAT_GLIDER = registry.register("bat_glider", BatGliderItem::new);
      WINGED_SHOES = registry.register("winged_shoes", WingedShoesItem::new);
      SILVER_HELMET = registry.register("silver_helmet", () -> {
         return new SimpleHelmetItem(TensuraArmourMaterials.SILVER, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      SILVER_CHESTPLATE = registry.register("silver_chestplate", () -> {
         return new SimpleChestplateItem(TensuraArmourMaterials.SILVER, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      SILVER_LEGGINGS = registry.register("silver_leggings", () -> {
         return new SimpleLeggingsItem(TensuraArmourMaterials.SILVER, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      SILVER_BOOTS = registry.register("silver_boots", () -> {
         return new SimpleBootsItem(TensuraArmourMaterials.SILVER, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      ANT_CARAPACE_HELMET = registry.register("ant_carapace_helmet", () -> {
         return new SimpleHelmetItem(TensuraArmourMaterials.ANT_CARAPACE, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      ANT_CARAPACE_CHESTPLATE = registry.register("ant_carapace_chestplate", () -> {
         return new SimpleChestplateItem(TensuraArmourMaterials.ANT_CARAPACE, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      ANT_CARAPACE_LEGGINGS = registry.register("ant_carapace_leggings", () -> {
         return new SimpleLeggingsItem(TensuraArmourMaterials.ANT_CARAPACE, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      ANT_CARAPACE_BOOTS = registry.register("ant_carapace_boots", () -> {
         return new SimpleBootsItem(TensuraArmourMaterials.ANT_CARAPACE, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      SERPENT_SCALEMAIL_HELMET = registry.register("serpent_scalemail_helmet", () -> {
         return new SimpleHelmetItem(TensuraArmourMaterials.SERPENT_SCALEMAIL, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      SERPENT_SCALEMAIL_CHESTPLATE = registry.register("serpent_scalemail_chestplate", () -> {
         return new SimpleChestplateItem(TensuraArmourMaterials.SERPENT_SCALEMAIL, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      SERPENT_SCALEMAIL_LEGGINGS = registry.register("serpent_scalemail_leggings", () -> {
         return new SimpleLeggingsItem(TensuraArmourMaterials.SERPENT_SCALEMAIL, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      SERPENT_SCALEMAIL_BOOTS = registry.register("serpent_scalemail_boots", () -> {
         return new SimpleBootsItem(TensuraArmourMaterials.SERPENT_SCALEMAIL, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      KNIGHT_SPIDER_CARAPACE_HELMET = registry.register("knight_spider_carapace_helmet", () -> {
         return new SimpleHelmetItem(TensuraArmourMaterials.KNIGHT_SPIDER_CARAPACE, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      KNIGHT_SPIDER_CARAPACE_CHESTPLATE = registry.register("knight_spider_carapace_chestplate", () -> {
         return new SimpleChestplateItem(TensuraArmourMaterials.KNIGHT_SPIDER_CARAPACE, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      KNIGHT_SPIDER_CARAPACE_LEGGINGS = registry.register("knight_spider_carapace_leggings", () -> {
         return new SimpleLeggingsItem(TensuraArmourMaterials.KNIGHT_SPIDER_CARAPACE, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      KNIGHT_SPIDER_CARAPACE_BOOTS = registry.register("knight_spider_carapace_boots", () -> {
         return new SimpleBootsItem(TensuraArmourMaterials.KNIGHT_SPIDER_CARAPACE, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      LOW_MAGISTEEL_HELMET = registry.register("low_magisteel_helmet", () -> {
         return new LowMagisteelArmorItem(EquipmentSlot.HEAD);
      });
      LOW_MAGISTEEL_CHESTPLATE = registry.register("low_magisteel_chestplate", () -> {
         return new LowMagisteelArmorItem(EquipmentSlot.CHEST);
      });
      LOW_MAGISTEEL_LEGGINGS = registry.register("low_magisteel_leggings", () -> {
         return new LowMagisteelArmorItem(EquipmentSlot.LEGS);
      });
      LOW_MAGISTEEL_BOOTS = registry.register("low_magisteel_boots", () -> {
         return new LowMagisteelArmorItem(EquipmentSlot.FEET);
      });
      ARMOURSAURUS_HELMET = registry.register("armoursaurus_helmet", () -> {
         return new ArmoursaurusArmorItem(EquipmentSlot.HEAD);
      });
      ARMOURSAURUS_CHESTPLATE = registry.register("armoursaurus_chestplate", () -> {
         return new ArmoursaurusArmorItem(EquipmentSlot.CHEST);
      });
      ARMOURSAURUS_LEGGINGS = registry.register("armoursaurus_leggings", () -> {
         return new ArmoursaurusArmorItem(EquipmentSlot.LEGS);
      });
      ARMOURSAURUS_BOOTS = registry.register("armoursaurus_boots", () -> {
         return new ArmoursaurusArmorItem(EquipmentSlot.FEET);
      });
      ARMOURSAURUS_SCALEMAIL_HELMET = registry.register("armoursaurus_scalemail_helmet", () -> {
         return new SimpleHelmetItem(TensuraArmourMaterials.ARMOURSAURUS_SCALEMAIL, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      ARMOURSAURUS_SCALEMAIL_CHESTPLATE = registry.register("armoursaurus_scalemail_chestplate", () -> {
         return new SimpleChestplateItem(TensuraArmourMaterials.ARMOURSAURUS_SCALEMAIL, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      ARMOURSAURUS_SCALEMAIL_LEGGINGS = registry.register("armoursaurus_scalemail_leggings", () -> {
         return new SimpleLeggingsItem(TensuraArmourMaterials.ARMOURSAURUS_SCALEMAIL, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      ARMOURSAURUS_SCALEMAIL_BOOTS = registry.register("armoursaurus_scalemail_boots", () -> {
         return new SimpleBootsItem(TensuraArmourMaterials.ARMOURSAURUS_SCALEMAIL, (new Properties()).m_41491_(TensuraCreativeTab.ARMOUR));
      });
      HIGH_MAGISTEEL_HELMET = registry.register("high_magisteel_helmet", () -> {
         return new HighMagisteelArmorItem(EquipmentSlot.HEAD);
      });
      HIGH_MAGISTEEL_CHESTPLATE = registry.register("high_magisteel_chestplate", () -> {
         return new HighMagisteelArmorItem(EquipmentSlot.CHEST);
      });
      HIGH_MAGISTEEL_LEGGINGS = registry.register("high_magisteel_leggings", () -> {
         return new HighMagisteelArmorItem(EquipmentSlot.LEGS);
      });
      HIGH_MAGISTEEL_BOOTS = registry.register("high_magisteel_boots", () -> {
         return new HighMagisteelArmorItem(EquipmentSlot.FEET);
      });
      CHARYBDIS_SCALEMAIL_HELMET = registry.register("charybdis_scalemail_helmet", () -> {
         return new CharybdisScalemailItem(EquipmentSlot.HEAD);
      });
      CHARYBDIS_SCALEMAIL_CHESTPLATE = registry.register("charybdis_scalemail_chestplate", () -> {
         return new CharybdisScalemailItem(EquipmentSlot.CHEST);
      });
      CHARYBDIS_SCALEMAIL_LEGGINGS = registry.register("charybdis_scalemail_leggings", () -> {
         return new CharybdisScalemailItem(EquipmentSlot.LEGS);
      });
      CHARYBDIS_SCALEMAIL_BOOTS = registry.register("charybdis_scalemail_boots", () -> {
         return new CharybdisScalemailItem(EquipmentSlot.FEET);
      });
      MITHRIL_HELMET = registry.register("mithril_helmet", () -> {
         return new MithrilArmorItem(EquipmentSlot.HEAD);
      });
      MITHRIL_CHESTPLATE = registry.register("mithril_chestplate", () -> {
         return new MithrilArmorItem(EquipmentSlot.CHEST);
      });
      MITHRIL_LEGGINGS = registry.register("mithril_leggings", () -> {
         return new MithrilArmorItem(EquipmentSlot.LEGS);
      });
      MITHRIL_BOOTS = registry.register("mithril_boots", () -> {
         return new MithrilArmorItem(EquipmentSlot.FEET);
      });
      ORICHALCUM_HELMET = registry.register("orichalcum_helmet", () -> {
         return new OrichalcumArmorItem(EquipmentSlot.HEAD);
      });
      ORICHALCUM_CHESTPLATE = registry.register("orichalcum_chestplate", () -> {
         return new OrichalcumArmorItem(EquipmentSlot.CHEST);
      });
      ORICHALCUM_LEGGINGS = registry.register("orichalcum_leggings", () -> {
         return new OrichalcumArmorItem(EquipmentSlot.LEGS);
      });
      ORICHALCUM_BOOTS = registry.register("orichalcum_boots", () -> {
         return new OrichalcumArmorItem(EquipmentSlot.FEET);
      });
      PURE_MAGISTEEL_HELMET = registry.register("pure_magisteel_helmet", () -> {
         return new PureMagisteelArmorItem(EquipmentSlot.HEAD);
      });
      PURE_MAGISTEEL_CHESTPLATE = registry.register("pure_magisteel_chestplate", () -> {
         return new PureMagisteelArmorItem(EquipmentSlot.CHEST);
      });
      PURE_MAGISTEEL_LEGGINGS = registry.register("pure_magisteel_leggings", () -> {
         return new PureMagisteelArmorItem(EquipmentSlot.LEGS);
      });
      PURE_MAGISTEEL_BOOTS = registry.register("pure_magisteel_boots", () -> {
         return new PureMagisteelArmorItem(EquipmentSlot.FEET);
      });
      ADAMANTITE_HELMET = registry.register("adamantite_helmet", () -> {
         return new AdamantiteArmorItem(EquipmentSlot.HEAD);
      });
      ADAMANTITE_CHESTPLATE = registry.register("adamantite_chestplate", () -> {
         return new AdamantiteArmorItem(EquipmentSlot.CHEST);
      });
      ADAMANTITE_LEGGINGS = registry.register("adamantite_leggings", () -> {
         return new AdamantiteArmorItem(EquipmentSlot.LEGS);
      });
      ADAMANTITE_BOOTS = registry.register("adamantite_boots", () -> {
         return new AdamantiteArmorItem(EquipmentSlot.FEET);
      });
      HIHIIROKANE_HELMET = registry.register("hihiirokane_helmet", () -> {
         return new HihiirokaneArmorItem(EquipmentSlot.HEAD);
      });
      HIHIIROKANE_CHESTPLATE = registry.register("hihiirokane_chestplate", () -> {
         return new HihiirokaneArmorItem(EquipmentSlot.CHEST);
      });
      HIHIIROKANE_LEGGINGS = registry.register("hihiirokane_leggings", () -> {
         return new HihiirokaneArmorItem(EquipmentSlot.LEGS);
      });
      HIHIIROKANE_BOOTS = registry.register("hihiirokane_boots", () -> {
         return new HihiirokaneArmorItem(EquipmentSlot.FEET);
      });
      HOLY_ARMAMENTS_CHESTPLATE = registry.register("holy_armaments_chestplate", () -> {
         return new HolyArmamentsArmorItem(EquipmentSlot.CHEST);
      });
      HOLY_ARMAMENTS_LEGGINGS = registry.register("holy_armaments_leggings", () -> {
         return new HolyArmamentsArmorItem(EquipmentSlot.LEGS);
      });
      HOLY_ARMAMENTS_BOOTS = registry.register("holy_armaments_boots", () -> {
         return new HolyArmamentsArmorItem(EquipmentSlot.FEET);
      });
      ANTI_MAGIC_MASK = registry.register("anti_magic_mask", AntiMagicMaskItem::new);
      DARK_JACKET = registry.register("dark_jacket", () -> {
         return new DarkSetArmorItem(EquipmentSlot.CHEST);
      });
      DARK_LEGGINGS = registry.register("dark_leggings", () -> {
         return new DarkSetArmorItem(EquipmentSlot.LEGS);
      });
      DARK_BOOTS = registry.register("dark_boots", () -> {
         return new DarkSetArmorItem(EquipmentSlot.FEET);
      });
      CRAZY_PIERROT_MASK = registry.register("crazy_pierrot_mask", CrazyPierrotMaskItem::new);
      ANGRY_PIERROT_MASK = registry.register("angry_pierrot_mask", AngryPierrotMaskItem::new);
      WONDER_PIERROT_MASK = registry.register("wonder_pierrot_mask", WonderPierrotMaskItem::new);
      TEARY_PIERROT_MASK = registry.register("teary_pierrot_mask", TeardropPierrotMaskItem::new);
   }
}
