package com.github.manasmods.tensura.registry.blocks;

import com.github.manasmods.manascore.api.data.gen.RenderType;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateBlockLoot;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateBlockModels;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateBlockLoot.DoorDrop;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateBlockLoot.LeavesDrop;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateBlockLoot.OreDrop;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateBlockLoot.OtherDrop;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateBlockLoot.SelfDrop;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateBlockLoot.SlabDrop;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateBlockLoot.WithLootTables;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateBlockModels.CubeAllModel;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateBlockModels.PillarModel;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateBlockModels.SlabModel;
import com.github.manasmods.manascore.api.data.gen.annotation.GenerateBlockModels.StairModel;
import com.github.manasmods.tensura.block.AllSidesDirectionalBlock;
import com.github.manasmods.tensura.block.BlackFireBlock;
import com.github.manasmods.tensura.block.CharybdisCoreBlock;
import com.github.manasmods.tensura.block.ChilledSlimeBlock;
import com.github.manasmods.tensura.block.HellPortal;
import com.github.manasmods.tensura.block.HipokuteFlowerPotBlock;
import com.github.manasmods.tensura.block.HipokuteGrass;
import com.github.manasmods.tensura.block.HolyFireBlock;
import com.github.manasmods.tensura.block.HotSpringWater;
import com.github.manasmods.tensura.block.LabyrinthBarrierBlock;
import com.github.manasmods.tensura.block.LabyrinthLightPathBlock;
import com.github.manasmods.tensura.block.LabyrinthLightPathSlabBlock;
import com.github.manasmods.tensura.block.LabyrinthLightPathStairBlock;
import com.github.manasmods.tensura.block.LabyrinthPortal;
import com.github.manasmods.tensura.block.LabyrinthPrayingPathBlock;
import com.github.manasmods.tensura.block.LightAirBlock;
import com.github.manasmods.tensura.block.MagicEngineBlock;
import com.github.manasmods.tensura.block.MothEggBlock;
import com.github.manasmods.tensura.block.OrcDisasterHead;
import com.github.manasmods.tensura.block.SidewayDirectionalBlock;
import com.github.manasmods.tensura.block.SimpleBlock;
import com.github.manasmods.tensura.block.SimpleLeaves;
import com.github.manasmods.tensura.block.SimpleLog;
import com.github.manasmods.tensura.block.SlimeChunkBlock;
import com.github.manasmods.tensura.block.SmithingBenchBlock;
import com.github.manasmods.tensura.block.SolidSpaceBlock;
import com.github.manasmods.tensura.block.SpiderEggBlock;
import com.github.manasmods.tensura.block.StickyCobwebBlock;
import com.github.manasmods.tensura.block.StickySteelCobwebBlock;
import com.github.manasmods.tensura.block.TensuraSapling;
import com.github.manasmods.tensura.block.ThatchBed;
import com.github.manasmods.tensura.block.UnlitLanternBlock;
import com.github.manasmods.tensura.block.UnlitStandingTorchBlock;
import com.github.manasmods.tensura.block.UnlitWallTorchBlock;
import com.github.manasmods.tensura.block.WebBlock;
import com.github.manasmods.tensura.block.WebbedStoneBlock;
import com.github.manasmods.tensura.item.TensuraCreativeTab;
import com.github.manasmods.tensura.item.custom.OrcDisasterHeadItem;
import com.github.manasmods.tensura.item.templates.SimpleBlockItem;
import com.github.manasmods.tensura.registry.fluids.TensuraFluids;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.util.TensuraBlockMaterial;
import com.github.manasmods.tensura.world.tree.grower.PalmTreeGrower;
import com.github.manasmods.tensura.world.tree.grower.SakuraTreeGrower;
import java.util.Objects;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.world.item.CreativeModeTab;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.SignItem;
import net.minecraft.world.item.StandingAndWallBlockItem;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.DoorBlock;
import net.minecraft.world.level.block.FenceBlock;
import net.minecraft.world.level.block.FenceGateBlock;
import net.minecraft.world.level.block.FlowerPotBlock;
import net.minecraft.world.level.block.PressurePlateBlock;
import net.minecraft.world.level.block.RotatedPillarBlock;
import net.minecraft.world.level.block.SandBlock;
import net.minecraft.world.level.block.SlabBlock;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.StairBlock;
import net.minecraft.world.level.block.TrapDoorBlock;
import net.minecraft.world.level.block.WallBlock;
import net.minecraft.world.level.block.WoodButtonBlock;
import net.minecraft.world.level.block.PressurePlateBlock.Sensitivity;
import net.minecraft.world.level.block.state.BlockBehaviour;
import net.minecraft.world.level.block.state.BlockBehaviour.Properties;
import net.minecraft.world.level.material.Material;
import net.minecraft.world.level.material.MaterialColor;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

@GenerateBlockModels
@GenerateBlockLoot
public class TensuraBlocks {
   @WithLootTables
   private static final DeferredRegister<Block> registry;
   @SelfDrop
   @PillarModel
   public static final RegistryObject<RotatedPillarBlock> THATCH_BLOCK;
   @SelfDrop
   public static final RegistryObject<TensuraSapling> PALM_SAPLING;
   public static final RegistryObject<Block> POTTED_PALM_SAPLING;
   @SelfDrop
   public static final RegistryObject<TensuraSapling> SAKURA_SAPLING;
   public static final RegistryObject<Block> POTTED_SAKURA_SAPLING;
   @LeavesDrop("tensura:palm_sapling")
   @CubeAllModel(
      renderType = RenderType.CUTOUT_MIPPED
   )
   public static final RegistryObject<SimpleLeaves> PALM_LEAVES;
   @LeavesDrop("tensura:sakura_sapling")
   @CubeAllModel(
      renderType = RenderType.CUTOUT_MIPPED
   )
   public static final RegistryObject<SimpleLeaves> SAKURA_LEAVES;
   @SelfDrop
   @PillarModel
   public static final RegistryObject<SimpleLog> PALM_LOG;
   @SelfDrop
   @PillarModel
   public static final RegistryObject<SimpleLog> SAKURA_LOG;
   @SelfDrop
   @PillarModel
   public static final RegistryObject<SimpleLog> STRIPPED_PALM_LOG;
   @SelfDrop
   @PillarModel
   public static final RegistryObject<SimpleLog> STRIPPED_SAKURA_LOG;
   @SelfDrop
   @PillarModel
   public static final RegistryObject<SimpleLog> PALM_WOOD;
   @SelfDrop
   @PillarModel
   public static final RegistryObject<SimpleLog> SAKURA_WOOD;
   @SelfDrop
   @PillarModel
   public static final RegistryObject<Block> STRIPPED_PALM_WOOD;
   @SelfDrop
   @PillarModel
   public static final RegistryObject<Block> STRIPPED_SAKURA_WOOD;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> PALM_PLANKS;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> SAKURA_PLANKS;
   @SelfDrop
   @StairModel("tensura:palm_planks")
   public static final RegistryObject<StairBlock> PALM_STAIRS;
   @SelfDrop
   @StairModel("tensura:sakura_planks")
   public static final RegistryObject<StairBlock> SAKURA_STAIRS;
   @SelfDrop
   @StairModel("tensura:thatch_block")
   public static final RegistryObject<StairBlock> THATCH_STAIRS;
   @SelfDrop
   @SlabModel("tensura:palm_planks")
   public static final RegistryObject<SlabBlock> PALM_SLAB;
   @SelfDrop
   @SlabModel("tensura:sakura_planks")
   public static final RegistryObject<SlabBlock> SAKURA_SLAB;
   @SelfDrop
   @SlabModel("tensura:thatch_block")
   public static final RegistryObject<SlabBlock> THATCH_SLAB;
   @DoorDrop
   public static final RegistryObject<DoorBlock> PALM_DOOR;
   @DoorDrop
   public static final RegistryObject<DoorBlock> SAKURA_DOOR;
   @SelfDrop
   public static final RegistryObject<TrapDoorBlock> PALM_TRAPDOOR;
   @SelfDrop
   public static final RegistryObject<TrapDoorBlock> SAKURA_TRAPDOOR;
   @SelfDrop
   public static final RegistryObject<FenceBlock> PALM_FENCE;
   @SelfDrop
   public static final RegistryObject<FenceBlock> SAKURA_FENCE;
   @SelfDrop
   public static final RegistryObject<FenceGateBlock> PALM_FENCE_GATE;
   @SelfDrop
   public static final RegistryObject<FenceGateBlock> SAKURA_FENCE_GATE;
   @SelfDrop
   public static final RegistryObject<WoodButtonBlock> PALM_BUTTON;
   @SelfDrop
   public static final RegistryObject<WoodButtonBlock> SAKURA_BUTTON;
   @SelfDrop
   public static final RegistryObject<PressurePlateBlock> PALM_PRESSURE_PLATE;
   @SelfDrop
   public static final RegistryObject<PressurePlateBlock> SAKURA_PRESSURE_PLATE;
   @OreDrop("tensura:magic_ore_shard")
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> MAGIC_ORE;
   @OreDrop("tensura:raw_silver")
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> SILVER_ORE;
   @OreDrop("tensura:magic_ore_shard")
   @CubeAllModel
   public static final RegistryObject<RotatedPillarBlock> DEEPSLATE_MAGIC_ORE;
   @OreDrop("tensura:raw_silver")
   @CubeAllModel
   public static final RegistryObject<RotatedPillarBlock> DEEPSLATE_SILVER_ORE;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> RAW_SILVER_BLOCK;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> SILVER_BLOCK;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> MAGIC_ORE_BLOCK;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> LOW_MAGISTEEL_BLOCK;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> HIGH_MAGISTEEL_BLOCK;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> PURE_MAGISTEEL_BLOCK;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> MITHRIL_BLOCK;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> ORICHALCUM_BLOCK;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> ADAMANTITE_BLOCK;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> HIHIIROKANE_BLOCK;
   public static final RegistryObject<Block> LABYRINTH_LAMP;
   public static final RegistryObject<Block> LABYRINTH_LAMP_TL;
   public static final RegistryObject<Block> LABYRINTH_LAMP_TR;
   public static final RegistryObject<Block> LABYRINTH_LAMP_BL;
   public static final RegistryObject<Block> LABYRINTH_LAMP_BR;
   public static final RegistryObject<Block> LABYRINTH_LIT_LAMP;
   public static final RegistryObject<Block> LABYRINTH_LIT_LAMP_TL;
   public static final RegistryObject<Block> LABYRINTH_LIT_LAMP_TR;
   public static final RegistryObject<Block> LABYRINTH_LIT_LAMP_BL;
   public static final RegistryObject<Block> LABYRINTH_LIT_LAMP_BR;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> LABYRINTH_BRICK;
   @StairModel("tensura:labyrinth_bricks")
   public static final RegistryObject<StairBlock> LABYRINTH_BRICK_STAIR;
   @SlabModel("tensura:labyrinth_bricks")
   public static final RegistryObject<SlabBlock> LABYRINTH_BRICK_SLAB;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> LABYRINTH_BRICK_TL;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> LABYRINTH_BRICK_TR;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> LABYRINTH_BRICK_BL;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> LABYRINTH_BRICK_BR;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> LABYRINTH_STONE;
   @StairModel("tensura:labyrinth_stone")
   public static final RegistryObject<StairBlock> LABYRINTH_STONE_STAIR;
   @SlabModel("tensura:labyrinth_stone")
   public static final RegistryObject<SlabBlock> LABYRINTH_STONE_SLAB;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> LABYRINTH_STONE_TL;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> LABYRINTH_STONE_TR;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> LABYRINTH_STONE_BL;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> LABYRINTH_STONE_BR;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> CREAM_LABYRINTH_BRICK;
   @StairModel("tensura:cream_labyrinth_bricks")
   public static final RegistryObject<StairBlock> CREAM_LABYRINTH_BRICK_STAIR;
   @SlabModel("tensura:cream_labyrinth_bricks")
   public static final RegistryObject<SlabBlock> CREAM_LABYRINTH_BRICK_SLAB;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> CREAM_LABYRINTH_BRICK_TL;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> CREAM_LABYRINTH_BRICK_TR;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> CREAM_LABYRINTH_BRICK_BL;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> CREAM_LABYRINTH_BRICK_BR;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> CREAM_LABYRINTH_STONE;
   @StairModel("tensura:cream_labyrinth_stone")
   public static final RegistryObject<StairBlock> CREAM_LABYRINTH_STONE_STAIR;
   @SlabModel("tensura:cream_labyrinth_stone")
   public static final RegistryObject<SlabBlock> CREAM_LABYRINTH_STONE_SLAB;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> CREAM_LABYRINTH_STONE_TL;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> CREAM_LABYRINTH_STONE_TR;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> CREAM_LABYRINTH_STONE_BL;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> CREAM_LABYRINTH_STONE_BR;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> DARK_LABYRINTH_BRICK;
   @StairModel("tensura:dark_labyrinth_bricks")
   public static final RegistryObject<StairBlock> DARK_LABYRINTH_BRICK_STAIR;
   @SlabModel("tensura:dark_labyrinth_bricks")
   public static final RegistryObject<SlabBlock> DARK_LABYRINTH_BRICK_SLAB;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> DARK_LABYRINTH_BRICK_TL;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> DARK_LABYRINTH_BRICK_TR;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> DARK_LABYRINTH_BRICK_BL;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> DARK_LABYRINTH_BRICK_BR;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> DARK_LABYRINTH_STONE;
   @StairModel("tensura:dark_labyrinth_stone")
   public static final RegistryObject<StairBlock> DARK_LABYRINTH_STONE_STAIR;
   @SlabModel("tensura:dark_labyrinth_stone")
   public static final RegistryObject<SlabBlock> DARK_LABYRINTH_STONE_SLAB;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> DARK_LABYRINTH_STONE_TL;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> DARK_LABYRINTH_STONE_TR;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> DARK_LABYRINTH_STONE_BL;
   @CubeAllModel
   public static final RegistryObject<SimpleBlock> DARK_LABYRINTH_STONE_BR;
   public static final RegistryObject<Block> LABYRINTH_PORTAL;
   public static final RegistryObject<Block> LABYRINTH_BARRIER_BLOCK;
   public static final RegistryObject<Block> LABYRINTH_CRYSTAL;
   public static final RegistryObject<Block> LABYRINTH_PRAYING_PATH;
   public static final RegistryObject<Block> LABYRINTH_LIGHT_PATH;
   public static final RegistryObject<StairBlock> LABYRINTH_LIGHT_PATH_STAIRS;
   public static final RegistryObject<SlabBlock> LABYRINTH_LIGHT_PATH_SLAB;
   public static final RegistryObject<Block> HELL_PORTAL;
   @SelfDrop
   public static final RegistryObject<Block> LOW_QUALITY_MAGIC_CRYSTAL_BLOCK;
   @SelfDrop
   @StairModel("tensura:low_quality_magic_crystal_block")
   public static final RegistryObject<StairBlock> LOW_QUALITY_MAGIC_CRYSTAL_STAIRS;
   @SlabDrop
   @SlabModel("tensura:low_quality_magic_crystal_block")
   public static final RegistryObject<Block> LOW_QUALITY_MAGIC_CRYSTAL_SLAB;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<Block> LOW_QUALITY_MAGIC_CRYSTAL_BRICKS;
   @SelfDrop
   @StairModel("tensura:low_quality_magic_crystal_bricks")
   public static final RegistryObject<StairBlock> LOW_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS;
   @SlabDrop
   @SlabModel("tensura:low_quality_magic_crystal_bricks")
   public static final RegistryObject<Block> LOW_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB;
   @SelfDrop
   public static final RegistryObject<WallBlock> LOW_QUALITY_MAGIC_CRYSTAL_BRICK_WALL;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<Block> CHISELED_LOW_QUALITY_MAGIC_CRYSTAL_BRICKS;
   @SelfDrop
   public static final RegistryObject<Block> MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK;
   @SelfDrop
   @StairModel("tensura:medium_quality_magic_crystal_block")
   public static final RegistryObject<StairBlock> MEDIUM_QUALITY_MAGIC_CRYSTAL_STAIRS;
   @SlabDrop
   @SlabModel("tensura:medium_quality_magic_crystal_block")
   public static final RegistryObject<Block> MEDIUM_QUALITY_MAGIC_CRYSTAL_SLAB;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<Block> MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS;
   @SelfDrop
   @StairModel("tensura:medium_quality_magic_crystal_bricks")
   public static final RegistryObject<StairBlock> MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS;
   @SlabDrop
   @SlabModel("tensura:medium_quality_magic_crystal_bricks")
   public static final RegistryObject<Block> MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB;
   @SelfDrop
   public static final RegistryObject<WallBlock> MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_WALL;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<Block> CHISELED_MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS;
   @SelfDrop
   public static final RegistryObject<Block> HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK;
   @SelfDrop
   @StairModel("tensura:high_quality_magic_crystal_block")
   public static final RegistryObject<StairBlock> HIGH_QUALITY_MAGIC_CRYSTAL_STAIRS;
   @SlabDrop
   @SlabModel("tensura:high_quality_magic_crystal_block")
   public static final RegistryObject<Block> HIGH_QUALITY_MAGIC_CRYSTAL_SLAB;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<Block> HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS;
   @SelfDrop
   @StairModel("tensura:high_quality_magic_crystal_bricks")
   public static final RegistryObject<StairBlock> HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS;
   @SlabDrop
   @SlabModel("tensura:high_quality_magic_crystal_bricks")
   public static final RegistryObject<Block> HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB;
   @SelfDrop
   public static final RegistryObject<WallBlock> HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_WALL;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<Block> CHISELED_HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<Block> SARASA_SAND;
   @SelfDrop
   public static final RegistryObject<Block> SARASA_SANDSTONE;
   @SelfDrop
   public static final RegistryObject<StairBlock> SARASA_SANDSTONE_STAIRS;
   @SlabDrop
   public static final RegistryObject<SlabBlock> SARASA_SANDSTONE_SLAB;
   @SelfDrop
   public static final RegistryObject<WallBlock> SARASA_SANDSTONE_WALL;
   @SelfDrop
   public static final RegistryObject<Block> CHISELED_SARASA_SANDSTONE;
   @SelfDrop
   public static final RegistryObject<Block> CUT_SARASA_SANDSTONE;
   @SlabDrop
   public static final RegistryObject<SlabBlock> CUT_SARASA_SANDSTONE_SLAB;
   @SelfDrop
   @CubeAllModel
   public static final RegistryObject<Block> SMOOTH_SARASA_SANDSTONE;
   @SelfDrop
   @StairModel("tensura:smooth_sarasa_sandstone")
   public static final RegistryObject<StairBlock> SMOOTH_SARASA_SANDSTONE_STAIRS;
   @SlabDrop
   @SlabModel("tensura:smooth_sarasa_sandstone")
   public static final RegistryObject<Block> SMOOTH_SARASA_SANDSTONE_SLAB;
   public static final RegistryObject<Block> STICKY_COBWEB;
   public static final RegistryObject<Block> STICKY_STEEL_COBWEB;
   @SelfDrop
   public static final RegistryObject<Block> SLIME_CHUNK_BLOCK;
   @SelfDrop
   public static final RegistryObject<Block> CHILLED_SLIME_BLOCK;
   public static final RegistryObject<MothEggBlock> MOTH_EGG;
   public static final RegistryObject<Block> CHARYBDIS_CORE;
   public static final RegistryObject<Block> SPIDER_EGG;
   @SelfDrop
   public static final RegistryObject<Block> WEB_BLOCK;
   @SelfDrop
   public static final RegistryObject<StairBlock> WEB_STAIRS;
   @SelfDrop
   public static final RegistryObject<SlabBlock> WEB_SLAB;
   @SelfDrop
   public static final RegistryObject<Block> WEBBED_COBBLESTONE;
   @SelfDrop
   public static final RegistryObject<StairBlock> WEBBED_COBBLESTONE_STAIRS;
   @SelfDrop
   public static final RegistryObject<SlabBlock> WEBBED_COBBLESTONE_SLAB;
   @SelfDrop
   public static final RegistryObject<WallBlock> WEBBED_COBBLESTONE_WALL;
   @SelfDrop
   public static final RegistryObject<Block> WEBBED_STONE_BRICKS;
   @SelfDrop
   public static final RegistryObject<StairBlock> WEBBED_STONE_BRICK_STAIRS;
   @SelfDrop
   public static final RegistryObject<SlabBlock> WEBBED_STONE_BRICK_SLAB;
   @SelfDrop
   public static final RegistryObject<WallBlock> WEBBED_STONE_BRICK_WALL;
   public static final RegistryObject<Block> BLACK_FIRE;
   public static final RegistryObject<Block> HOLY_FIRE;
   @SelfDrop
   public static final RegistryObject<MagicEngineBlock> BRICK_MAGIC_ENGINE;
   @SelfDrop
   public static final RegistryObject<MagicEngineBlock> STONE_BRICK_MAGIC_ENGINE;
   @SelfDrop
   public static final RegistryObject<SmithingBenchBlock> SMITHING_BENCH;
   @SelfDrop
   public static final RegistryObject<SimpleBlock> WARP_PAD;
   @SelfDrop
   public static final RegistryObject<ThatchBed> THATCH_BED;
   @SelfDrop
   public static final RegistryObject<Block> UNLIT_TORCH;
   @OtherDrop("tensura:unlit_torch")
   public static final RegistryObject<UnlitWallTorchBlock> UNLIT_WALL_TORCH;
   @SelfDrop
   public static final RegistryObject<UnlitLanternBlock> UNLIT_LANTERN;
   @OtherDrop("minecraft:air")
   public static final RegistryObject<HotSpringWater> HOT_SPRING_WATER;
   public static final RegistryObject<HipokuteGrass> HIPOKUTE_GRASS;
   public static final RegistryObject<Block> POTTED_HIPOKUTE_FLOWER;
   public static final RegistryObject<Block> LIGHT_AIR;
   public static final RegistryObject<Block> SOLID_SPACE;
   public static final RegistryObject<OrcDisasterHead> ORC_DISASTER_HEAD;

   public static void init(IEventBus modEventBus) {
      TensuraBlocks.Items.registry.register(modEventBus);
      registry.register(modEventBus);
   }

   static {
      registry = DeferredRegister.create(ForgeRegistries.BLOCKS, "tensura");
      THATCH_BLOCK = registry.register("thatch_block", () -> {
         return new RotatedPillarBlock(Properties.m_60944_(Material.f_76274_, MaterialColor.f_76405_).m_60978_(1.0F).m_60918_(SoundType.f_56740_));
      });
      PALM_SAPLING = registry.register("palm_sapling", () -> {
         return new TensuraSapling(new PalmTreeGrower());
      });
      POTTED_PALM_SAPLING = registry.register("potted_palm_sapling", () -> {
         return new FlowerPotBlock(() -> {
            return (FlowerPotBlock)Blocks.f_50276_;
         }, PALM_SAPLING, Properties.m_60926_(Blocks.f_50279_).m_60955_());
      });
      SAKURA_SAPLING = registry.register("sakura_sapling", () -> {
         return new TensuraSapling(new SakuraTreeGrower());
      });
      POTTED_SAKURA_SAPLING = registry.register("potted_sakura_sapling", () -> {
         return new FlowerPotBlock(() -> {
            return (FlowerPotBlock)Blocks.f_50276_;
         }, SAKURA_SAPLING, Properties.m_60926_(Blocks.f_50279_).m_60955_());
      });
      PALM_LEAVES = registry.register("palm_leaves", SimpleLeaves::new);
      SAKURA_LEAVES = registry.register("sakura_leaves", SimpleLeaves::new);
      PALM_LOG = registry.register("palm_log", () -> {
         return new SimpleLog(MaterialColor.f_76372_, MaterialColor.f_76362_, STRIPPED_PALM_LOG);
      });
      SAKURA_LOG = registry.register("sakura_log", () -> {
         return new SimpleLog(MaterialColor.f_76378_, MaterialColor.f_76418_, STRIPPED_SAKURA_LOG);
      });
      STRIPPED_PALM_LOG = registry.register("stripped_palm_log", () -> {
         return new SimpleLog(MaterialColor.f_76372_, MaterialColor.f_76362_);
      });
      STRIPPED_SAKURA_LOG = registry.register("stripped_sakura_log", () -> {
         return new SimpleLog(MaterialColor.f_76378_, MaterialColor.f_76418_);
      });
      PALM_WOOD = registry.register("palm_wood", () -> {
         return new SimpleLog(MaterialColor.f_76372_, MaterialColor.f_76362_, STRIPPED_PALM_WOOD);
      });
      SAKURA_WOOD = registry.register("sakura_wood", () -> {
         return new SimpleLog(MaterialColor.f_76373_, MaterialColor.f_76419_, STRIPPED_SAKURA_WOOD);
      });
      STRIPPED_PALM_WOOD = registry.register("stripped_palm_wood", () -> {
         return new SimpleLog(MaterialColor.f_76372_, MaterialColor.f_76362_);
      });
      STRIPPED_SAKURA_WOOD = registry.register("stripped_sakura_wood", () -> {
         return new SimpleLog(MaterialColor.f_76373_, MaterialColor.f_76419_);
      });
      PALM_PLANKS = registry.register("palm_planks", () -> {
         return new SimpleBlock(Material.f_76320_, (properties) -> {
            return properties.m_60913_(2.0F, 3.0F).m_60918_(SoundType.f_56736_);
         });
      });
      SAKURA_PLANKS = registry.register("sakura_planks", () -> {
         return new SimpleBlock(Material.f_76320_, (properties) -> {
            return properties.m_60913_(2.0F, 3.0F).m_60918_(SoundType.f_56736_);
         });
      });
      PALM_STAIRS = registry.register("palm_stairs", () -> {
         SimpleBlock var10002 = (SimpleBlock)PALM_PLANKS.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60939_(Material.f_76320_).m_60913_(2.0F, 3.0F).m_60918_(SoundType.f_56736_));
      });
      SAKURA_STAIRS = registry.register("sakura_stairs", () -> {
         SimpleBlock var10002 = (SimpleBlock)PALM_PLANKS.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60939_(Material.f_76320_).m_60913_(2.0F, 3.0F).m_60918_(SoundType.f_56736_));
      });
      THATCH_STAIRS = registry.register("thatch_stairs", () -> {
         RotatedPillarBlock var10002 = (RotatedPillarBlock)THATCH_BLOCK.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60939_(Material.f_76274_).m_60978_(1.0F).m_60918_(SoundType.f_56740_));
      });
      PALM_SLAB = registry.register("palm_slab", () -> {
         return new SlabBlock(Properties.m_60939_(Material.f_76320_).m_60913_(2.0F, 3.0F).m_60918_(SoundType.f_56736_));
      });
      SAKURA_SLAB = registry.register("sakura_slab", () -> {
         return new SlabBlock(Properties.m_60939_(Material.f_76320_).m_60913_(2.0F, 3.0F).m_60918_(SoundType.f_56736_));
      });
      THATCH_SLAB = registry.register("thatch_slab", () -> {
         return new SlabBlock(Properties.m_60939_(Material.f_76274_).m_60978_(1.0F).m_60918_(SoundType.f_56740_));
      });
      PALM_DOOR = registry.register("palm_door", () -> {
         return new DoorBlock(Properties.m_60939_(Material.f_76320_).m_60978_(3.0F).m_60955_().m_60918_(SoundType.f_56736_));
      });
      SAKURA_DOOR = registry.register("sakura_door", () -> {
         return new DoorBlock(Properties.m_60939_(Material.f_76320_).m_60978_(3.0F).m_60955_().m_60918_(SoundType.f_56736_));
      });
      PALM_TRAPDOOR = registry.register("palm_trapdoor", () -> {
         return new TrapDoorBlock(Properties.m_60939_(Material.f_76320_).m_60978_(3.0F).m_60955_().m_60918_(SoundType.f_56736_));
      });
      SAKURA_TRAPDOOR = registry.register("sakura_trapdoor", () -> {
         return new TrapDoorBlock(Properties.m_60939_(Material.f_76320_).m_60978_(3.0F).m_60955_().m_60918_(SoundType.f_56736_));
      });
      PALM_FENCE = registry.register("palm_fence", () -> {
         return new FenceBlock(Properties.m_60939_(Material.f_76320_).m_60913_(2.0F, 3.0F).m_60918_(SoundType.f_56736_));
      });
      SAKURA_FENCE = registry.register("sakura_fence", () -> {
         return new FenceBlock(Properties.m_60939_(Material.f_76320_).m_60913_(2.0F, 3.0F).m_60918_(SoundType.f_56736_));
      });
      PALM_FENCE_GATE = registry.register("palm_fence_gate", () -> {
         return new FenceGateBlock(Properties.m_60939_(Material.f_76320_).m_60913_(2.0F, 3.0F).m_60918_(SoundType.f_56736_));
      });
      SAKURA_FENCE_GATE = registry.register("sakura_fence_gate", () -> {
         return new FenceGateBlock(Properties.m_60939_(Material.f_76320_).m_60913_(2.0F, 3.0F).m_60918_(SoundType.f_56736_));
      });
      PALM_BUTTON = registry.register("palm_button", () -> {
         return new WoodButtonBlock(Properties.m_60939_(Material.f_76320_).m_60978_(0.5F).m_60910_().m_60918_(SoundType.f_56736_));
      });
      SAKURA_BUTTON = registry.register("sakura_button", () -> {
         return new WoodButtonBlock(Properties.m_60939_(Material.f_76320_).m_60978_(0.5F).m_60910_().m_60918_(SoundType.f_56736_));
      });
      PALM_PRESSURE_PLATE = registry.register("palm_pressure_plate", () -> {
         return new PressurePlateBlock(Sensitivity.EVERYTHING, Properties.m_60939_(Material.f_76320_).m_60978_(0.5F).m_60918_(SoundType.f_56736_));
      });
      SAKURA_PRESSURE_PLATE = registry.register("sakura_pressure_plate", () -> {
         return new PressurePlateBlock(Sensitivity.EVERYTHING, Properties.m_60939_(Material.f_76320_).m_60978_(0.5F).m_60918_(SoundType.f_56736_));
      });
      MAGIC_ORE = registry.register("magic_ore", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(3.0F, 3.0F).m_60918_(SoundType.f_56742_).m_60953_((blockState) -> {
               return 4;
            }).m_60999_();
         });
      });
      SILVER_ORE = registry.register("silver_ore", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(3.0F, 3.0F).m_60918_(SoundType.f_56742_).m_60999_();
         });
      });
      DEEPSLATE_MAGIC_ORE = registry.register("deepslate_magic_ore", () -> {
         return new RotatedPillarBlock(Properties.m_60944_(Material.f_76278_, MaterialColor.f_164534_).m_60913_(4.5F, 3.0F).m_60918_(SoundType.f_154677_).m_60953_((blockState) -> {
            return 4;
         }).m_60999_());
      });
      DEEPSLATE_SILVER_ORE = registry.register("deepslate_silver_ore", () -> {
         return new RotatedPillarBlock(Properties.m_60944_(Material.f_76278_, MaterialColor.f_164534_).m_60913_(4.5F, 3.0F).m_60918_(SoundType.f_154677_).m_60999_());
      });
      RAW_SILVER_BLOCK = registry.register("block_of_raw_silver", () -> {
         return new SimpleBlock(Material.f_76279_, (properties) -> {
            return properties.m_60913_(3.0F, 6.0F).m_60918_(SoundType.f_56743_).m_60999_();
         });
      });
      SILVER_BLOCK = registry.register("block_of_silver", () -> {
         return new SimpleBlock(Material.f_76279_, (properties) -> {
            return properties.m_60913_(3.0F, 6.0F).m_60918_(SoundType.f_56743_).m_60999_();
         });
      });
      MAGIC_ORE_BLOCK = registry.register("block_of_magic_ore", () -> {
         return new SimpleBlock(Material.f_164531_, (properties) -> {
            return properties.m_60913_(5.0F, 100.0F).m_60918_(SoundType.f_154654_).m_60953_((blockState) -> {
               return 15;
            }).m_60999_();
         });
      });
      LOW_MAGISTEEL_BLOCK = registry.register("block_of_low_magisteel", () -> {
         return new SimpleBlock(Material.f_76279_, (properties) -> {
            return properties.m_60913_(5.0F, 100.0F).m_60918_(SoundType.f_56743_).m_60953_((blockState) -> {
               return 11;
            }).m_60999_();
         });
      });
      HIGH_MAGISTEEL_BLOCK = registry.register("block_of_high_magisteel", () -> {
         return new SimpleBlock(Material.f_76279_, (properties) -> {
            return properties.m_60913_(10.0F, 600.0F).m_60918_(SoundType.f_56743_).m_60953_((blockState) -> {
               return 13;
            }).m_60999_();
         });
      });
      PURE_MAGISTEEL_BLOCK = registry.register("block_of_pure_magisteel", () -> {
         return new SimpleBlock(Material.f_76279_, (properties) -> {
            return properties.m_60913_(50.0F, 1200.0F).m_60918_(SoundType.f_56743_).m_60953_((blockState) -> {
               return 15;
            }).m_60999_();
         });
      });
      MITHRIL_BLOCK = registry.register("block_of_mithril", () -> {
         return new SimpleBlock(Material.f_76279_, (properties) -> {
            return properties.m_60913_(20.0F, 1200.0F).m_60918_(SoundType.f_56743_).m_60953_((blockState) -> {
               return 13;
            }).m_60999_();
         });
      });
      ORICHALCUM_BLOCK = registry.register("block_of_orichalcum", () -> {
         return new SimpleBlock(Material.f_76279_, (properties) -> {
            return properties.m_60913_(40.0F, 1200.0F).m_60918_(SoundType.f_56743_).m_60953_((blockState) -> {
               return 13;
            }).m_60999_();
         });
      });
      ADAMANTITE_BLOCK = registry.register("block_of_adamantite", () -> {
         return new SimpleBlock(Material.f_76279_, (properties) -> {
            return properties.m_60913_(60.0F, 1200.0F).m_60918_(SoundType.f_56743_).m_60953_((blockState) -> {
               return 14;
            }).m_60999_();
         });
      });
      HIHIIROKANE_BLOCK = registry.register("block_of_hihiirokane", () -> {
         return new SimpleBlock(Material.f_76279_, (properties) -> {
            return properties.m_60913_(70.0F, 1200.0F).m_60918_(SoundType.f_56743_).m_60953_((blockState) -> {
               return 15;
            }).m_60999_();
         });
      });
      LABYRINTH_LAMP = registry.register("labyrinth_lamp", () -> {
         return new SidewayDirectionalBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_LAMP_TL = registry.register("labyrinth_lamp_tl", () -> {
         return new SidewayDirectionalBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_LAMP_TR = registry.register("labyrinth_lamp_tr", () -> {
         return new SidewayDirectionalBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_LAMP_BL = registry.register("labyrinth_lamp_bl", () -> {
         return new SidewayDirectionalBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_LAMP_BR = registry.register("labyrinth_lamp_br", () -> {
         return new SidewayDirectionalBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_LIT_LAMP = registry.register("labyrinth_lit_lamp", () -> {
         return new SidewayDirectionalBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_LIT_LAMP_TL = registry.register("labyrinth_lit_lamp_tl", () -> {
         return new SidewayDirectionalBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_LIT_LAMP_TR = registry.register("labyrinth_lit_lamp_tr", () -> {
         return new SidewayDirectionalBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_LIT_LAMP_BL = registry.register("labyrinth_lit_lamp_bl", () -> {
         return new SidewayDirectionalBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_LIT_LAMP_BR = registry.register("labyrinth_lit_lamp_br", () -> {
         return new SidewayDirectionalBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_BRICK = registry.register("labyrinth_bricks", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_BRICK_STAIR = registry.register("labyrinth_brick_stairs", () -> {
         SimpleBlock var10002 = (SimpleBlock)LABYRINTH_BRICK.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60926_((BlockBehaviour)LABYRINTH_BRICK.get()).m_222994_());
      });
      LABYRINTH_BRICK_SLAB = registry.register("labyrinth_brick_slab", () -> {
         return new SlabBlock(Properties.m_60926_((BlockBehaviour)LABYRINTH_BRICK.get()).m_222994_());
      });
      LABYRINTH_BRICK_TL = registry.register("labyrinth_bricks_tl", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_BRICK_TR = registry.register("labyrinth_bricks_tr", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_BRICK_BL = registry.register("labyrinth_bricks_bl", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_BRICK_BR = registry.register("labyrinth_bricks_br", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_STONE = registry.register("labyrinth_stone", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_STONE_STAIR = registry.register("labyrinth_stone_stairs", () -> {
         SimpleBlock var10002 = (SimpleBlock)LABYRINTH_STONE.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60926_((BlockBehaviour)LABYRINTH_STONE.get()).m_222994_());
      });
      LABYRINTH_STONE_SLAB = registry.register("labyrinth_stone_slab", () -> {
         return new SlabBlock(Properties.m_60926_((BlockBehaviour)LABYRINTH_STONE.get()).m_222994_());
      });
      LABYRINTH_STONE_TL = registry.register("labyrinth_stone_tl", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_STONE_TR = registry.register("labyrinth_stone_tr", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_STONE_BL = registry.register("labyrinth_stone_bl", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_STONE_BR = registry.register("labyrinth_stone_br", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      CREAM_LABYRINTH_BRICK = registry.register("cream_labyrinth_bricks", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      CREAM_LABYRINTH_BRICK_STAIR = registry.register("cream_labyrinth_brick_stairs", () -> {
         SimpleBlock var10002 = (SimpleBlock)CREAM_LABYRINTH_BRICK.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60926_((BlockBehaviour)CREAM_LABYRINTH_BRICK.get()).m_222994_());
      });
      CREAM_LABYRINTH_BRICK_SLAB = registry.register("cream_labyrinth_brick_slab", () -> {
         return new SlabBlock(Properties.m_60926_((BlockBehaviour)CREAM_LABYRINTH_BRICK.get()).m_222994_());
      });
      CREAM_LABYRINTH_BRICK_TL = registry.register("cream_labyrinth_bricks_tl", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      CREAM_LABYRINTH_BRICK_TR = registry.register("cream_labyrinth_bricks_tr", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      CREAM_LABYRINTH_BRICK_BL = registry.register("cream_labyrinth_bricks_bl", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      CREAM_LABYRINTH_BRICK_BR = registry.register("cream_labyrinth_bricks_br", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      CREAM_LABYRINTH_STONE = registry.register("cream_labyrinth_stone", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      CREAM_LABYRINTH_STONE_STAIR = registry.register("cream_labyrinth_stone_stairs", () -> {
         SimpleBlock var10002 = (SimpleBlock)CREAM_LABYRINTH_STONE.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60926_((BlockBehaviour)CREAM_LABYRINTH_STONE.get()).m_222994_());
      });
      CREAM_LABYRINTH_STONE_SLAB = registry.register("cream_labyrinth_stone_slab", () -> {
         return new SlabBlock(Properties.m_60926_((BlockBehaviour)CREAM_LABYRINTH_STONE.get()).m_222994_());
      });
      CREAM_LABYRINTH_STONE_TL = registry.register("cream_labyrinth_stone_tl", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      CREAM_LABYRINTH_STONE_TR = registry.register("cream_labyrinth_stone_tr", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      CREAM_LABYRINTH_STONE_BL = registry.register("cream_labyrinth_stone_bl", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      CREAM_LABYRINTH_STONE_BR = registry.register("cream_labyrinth_stone_br", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      DARK_LABYRINTH_BRICK = registry.register("dark_labyrinth_bricks", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      DARK_LABYRINTH_BRICK_STAIR = registry.register("dark_labyrinth_brick_stairs", () -> {
         SimpleBlock var10002 = (SimpleBlock)DARK_LABYRINTH_BRICK.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60926_((BlockBehaviour)DARK_LABYRINTH_BRICK.get()).m_222994_());
      });
      DARK_LABYRINTH_BRICK_SLAB = registry.register("dark_labyrinth_brick_slab", () -> {
         return new SlabBlock(Properties.m_60926_((BlockBehaviour)DARK_LABYRINTH_BRICK.get()).m_222994_());
      });
      DARK_LABYRINTH_BRICK_TL = registry.register("dark_labyrinth_bricks_tl", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      DARK_LABYRINTH_BRICK_TR = registry.register("dark_labyrinth_bricks_tr", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      DARK_LABYRINTH_BRICK_BL = registry.register("dark_labyrinth_bricks_bl", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      DARK_LABYRINTH_BRICK_BR = registry.register("dark_labyrinth_bricks_br", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      DARK_LABYRINTH_STONE = registry.register("dark_labyrinth_stone", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      DARK_LABYRINTH_STONE_STAIR = registry.register("dark_labyrinth_stone_stairs", () -> {
         SimpleBlock var10002 = (SimpleBlock)DARK_LABYRINTH_STONE.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60926_((BlockBehaviour)DARK_LABYRINTH_STONE.get()).m_222994_());
      });
      DARK_LABYRINTH_STONE_SLAB = registry.register("dark_labyrinth_stone_slab", () -> {
         return new SlabBlock(Properties.m_60926_((BlockBehaviour)DARK_LABYRINTH_STONE.get()).m_222994_());
      });
      DARK_LABYRINTH_STONE_TL = registry.register("dark_labyrinth_stone_tl", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      DARK_LABYRINTH_STONE_TR = registry.register("dark_labyrinth_stone_tr", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      DARK_LABYRINTH_STONE_BL = registry.register("dark_labyrinth_stone_bl", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      DARK_LABYRINTH_STONE_BR = registry.register("dark_labyrinth_stone_br", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(-1.0F, 3600000.0F).m_222994_();
         });
      });
      LABYRINTH_PORTAL = registry.register("labyrinth_portal", LabyrinthPortal::new);
      LABYRINTH_BARRIER_BLOCK = registry.register("labyrinth_barrier_block", LabyrinthBarrierBlock::new);
      LABYRINTH_CRYSTAL = registry.register("labyrinth_crystal", () -> {
         return new SimpleBlock(Material.f_164531_, (properties) -> {
            return properties.m_222994_().m_60955_().m_60971_((blockState, blockGetter, blockPos) -> {
               return false;
            }).m_60918_(SoundType.f_154654_).m_60953_((state) -> {
               return 15;
            }).m_60913_(-1.0F, 3600000.0F);
         });
      });
      LABYRINTH_PRAYING_PATH = registry.register("labyrinth_praying_path", LabyrinthPrayingPathBlock::new);
      LABYRINTH_LIGHT_PATH = registry.register("labyrinth_light_path", LabyrinthLightPathBlock::new);
      LABYRINTH_LIGHT_PATH_STAIRS = registry.register("labyrinth_light_path_stairs", () -> {
         Block var10002 = (Block)LABYRINTH_LIGHT_PATH.get();
         Objects.requireNonNull(var10002);
         return new LabyrinthLightPathStairBlock(var10002::m_49966_, Properties.m_60926_((BlockBehaviour)LABYRINTH_LIGHT_PATH.get()).m_222994_());
      });
      LABYRINTH_LIGHT_PATH_SLAB = registry.register("labyrinth_light_path_slab", () -> {
         return new LabyrinthLightPathSlabBlock(Properties.m_60926_((BlockBehaviour)LABYRINTH_LIGHT_PATH.get()).m_222994_());
      });
      HELL_PORTAL = registry.register("hell_portal", HellPortal::new);
      LOW_QUALITY_MAGIC_CRYSTAL_BLOCK = registry.register("low_quality_magic_crystal_block", () -> {
         return new AllSidesDirectionalBlock(Properties.m_60944_(Material.f_164531_, MaterialColor.f_76415_).m_60978_(1.5F).m_60999_().m_60918_(SoundType.f_154654_));
      });
      LOW_QUALITY_MAGIC_CRYSTAL_STAIRS = registry.register("low_quality_magic_crystal_stairs", () -> {
         Block var10002 = (Block)LOW_QUALITY_MAGIC_CRYSTAL_BLOCK.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60926_((BlockBehaviour)LOW_QUALITY_MAGIC_CRYSTAL_BLOCK.get()));
      });
      LOW_QUALITY_MAGIC_CRYSTAL_SLAB = registry.register("low_quality_magic_crystal_slab", () -> {
         return new SlabBlock(Properties.m_60926_((BlockBehaviour)LOW_QUALITY_MAGIC_CRYSTAL_BLOCK.get()));
      });
      LOW_QUALITY_MAGIC_CRYSTAL_BRICKS = registry.register("low_quality_magic_crystal_bricks", () -> {
         return new SimpleBlock(Properties.m_60926_((BlockBehaviour)LOW_QUALITY_MAGIC_CRYSTAL_BLOCK.get()));
      });
      LOW_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS = registry.register("low_quality_magic_crystal_brick_stairs", () -> {
         Block var10002 = (Block)LOW_QUALITY_MAGIC_CRYSTAL_BRICKS.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60926_((BlockBehaviour)LOW_QUALITY_MAGIC_CRYSTAL_BRICKS.get()));
      });
      LOW_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB = registry.register("low_quality_magic_crystal_brick_slab", () -> {
         return new SlabBlock(Properties.m_60926_((BlockBehaviour)LOW_QUALITY_MAGIC_CRYSTAL_BRICKS.get()));
      });
      LOW_QUALITY_MAGIC_CRYSTAL_BRICK_WALL = registry.register("low_quality_magic_crystal_brick_wall", () -> {
         return new WallBlock(Properties.m_60926_((BlockBehaviour)LOW_QUALITY_MAGIC_CRYSTAL_BRICKS.get()));
      });
      CHISELED_LOW_QUALITY_MAGIC_CRYSTAL_BRICKS = registry.register("chiseled_low_quality_magic_crystal_bricks", () -> {
         return new SimpleBlock(Properties.m_60926_((BlockBehaviour)LOW_QUALITY_MAGIC_CRYSTAL_BRICKS.get()));
      });
      MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK = registry.register("medium_quality_magic_crystal_block", () -> {
         return new AllSidesDirectionalBlock(Properties.m_60944_(Material.f_164531_, MaterialColor.f_76415_).m_60978_(2.0F).m_60999_().m_60918_(SoundType.f_154654_));
      });
      MEDIUM_QUALITY_MAGIC_CRYSTAL_STAIRS = registry.register("medium_quality_magic_crystal_stairs", () -> {
         Block var10002 = (Block)MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60926_((BlockBehaviour)MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK.get()));
      });
      MEDIUM_QUALITY_MAGIC_CRYSTAL_SLAB = registry.register("meidum_quality_magic_crystal_slab", () -> {
         return new SlabBlock(Properties.m_60926_((BlockBehaviour)MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK.get()));
      });
      MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS = registry.register("medium_quality_magic_crystal_bricks", () -> {
         return new SimpleBlock(Properties.m_60926_((BlockBehaviour)MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK.get()));
      });
      MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS = registry.register("medium_quality_magic_crystal_brick_stairs", () -> {
         Block var10002 = (Block)MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60926_((BlockBehaviour)MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS.get()));
      });
      MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB = registry.register("medium_quality_magic_crystal_brick_slab", () -> {
         return new SlabBlock(Properties.m_60926_((BlockBehaviour)MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS.get()));
      });
      MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_WALL = registry.register("medium_quality_magic_crystal_brick_wall", () -> {
         return new WallBlock(Properties.m_60926_((BlockBehaviour)MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS.get()));
      });
      CHISELED_MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS = registry.register("chiseled_medium_quality_magic_crystal_bricks", () -> {
         return new SimpleBlock(Properties.m_60926_((BlockBehaviour)MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS.get()));
      });
      HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK = registry.register("high_quality_magic_crystal_block", () -> {
         return new AllSidesDirectionalBlock(Properties.m_60944_(Material.f_164531_, MaterialColor.f_76415_).m_60978_(3.0F).m_60999_().m_60918_(SoundType.f_154654_));
      });
      HIGH_QUALITY_MAGIC_CRYSTAL_STAIRS = registry.register("high_quality_magic_crystal_stairs", () -> {
         Block var10002 = (Block)HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60926_((BlockBehaviour)HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK.get()));
      });
      HIGH_QUALITY_MAGIC_CRYSTAL_SLAB = registry.register("high_quality_magic_crystal_slab", () -> {
         return new SlabBlock(Properties.m_60926_((BlockBehaviour)HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK.get()));
      });
      HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS = registry.register("high_quality_magic_crystal_bricks", () -> {
         return new SimpleBlock(Properties.m_60926_((BlockBehaviour)HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK.get()));
      });
      HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS = registry.register("high_quality_magic_crystal_brick_stairs", () -> {
         Block var10002 = (Block)HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60926_((BlockBehaviour)HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS.get()));
      });
      HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB = registry.register("high_quality_magic_crystal_brick_slab", () -> {
         return new SlabBlock(Properties.m_60926_((BlockBehaviour)HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS.get()));
      });
      HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_WALL = registry.register("high_quality_magic_crystal_brick_wall", () -> {
         return new WallBlock(Properties.m_60926_((BlockBehaviour)HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS.get()));
      });
      CHISELED_HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS = registry.register("chiseled_high_quality_magic_crystal_bricks", () -> {
         return new SimpleBlock(Properties.m_60926_((BlockBehaviour)HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS.get()));
      });
      SARASA_SAND = registry.register("sarasa_sand", () -> {
         return new SandBlock(14406560, Properties.m_60944_(Material.f_76317_, MaterialColor.f_76400_).m_60978_(0.5F).m_60918_(SoundType.f_56746_));
      });
      SARASA_SANDSTONE = registry.register("sarasa_sandstone", () -> {
         return new SimpleBlock(Properties.m_60944_(Material.f_76278_, MaterialColor.f_76400_).m_60999_().m_60978_(0.8F).m_60918_(SoundType.f_56742_));
      });
      SARASA_SANDSTONE_STAIRS = registry.register("sarasa_sandstone_stairs", () -> {
         Block var10002 = (Block)SARASA_SANDSTONE.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60926_((BlockBehaviour)SARASA_SANDSTONE.get()));
      });
      SARASA_SANDSTONE_SLAB = registry.register("sarasa_sandstone_slab", () -> {
         return new SlabBlock(Properties.m_60926_((BlockBehaviour)SARASA_SANDSTONE.get()));
      });
      SARASA_SANDSTONE_WALL = registry.register("sarasa_sandstone_wall", () -> {
         return new WallBlock(Properties.m_60926_((BlockBehaviour)SARASA_SANDSTONE.get()));
      });
      CHISELED_SARASA_SANDSTONE = registry.register("chiseled_sarasa_sandstone", () -> {
         return new SimpleBlock(Properties.m_60926_((BlockBehaviour)SARASA_SANDSTONE.get()));
      });
      CUT_SARASA_SANDSTONE = registry.register("cut_sarasa_sandstone", () -> {
         return new SimpleBlock(Properties.m_60926_((BlockBehaviour)SARASA_SANDSTONE.get()));
      });
      CUT_SARASA_SANDSTONE_SLAB = registry.register("cut_sarasa_sandstone_slab", () -> {
         return new SlabBlock(Properties.m_60926_((BlockBehaviour)SARASA_SANDSTONE.get()));
      });
      SMOOTH_SARASA_SANDSTONE = registry.register("smooth_sarasa_sandstone", () -> {
         return new SimpleBlock(Properties.m_60944_(Material.f_76278_, MaterialColor.f_76400_).m_60999_().m_60913_(2.0F, 6.0F).m_60918_(SoundType.f_56742_));
      });
      SMOOTH_SARASA_SANDSTONE_STAIRS = registry.register("smooth_sarasa_sandstone_stairs", () -> {
         Block var10002 = (Block)SMOOTH_SARASA_SANDSTONE.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60926_((BlockBehaviour)SMOOTH_SARASA_SANDSTONE.get()));
      });
      SMOOTH_SARASA_SANDSTONE_SLAB = registry.register("smooth_sarasa_sandstone_slab", () -> {
         return new SlabBlock(Properties.m_60926_((BlockBehaviour)SMOOTH_SARASA_SANDSTONE.get()));
      });
      STICKY_COBWEB = registry.register("sticky_cobweb", () -> {
         return new StickyCobwebBlock(Properties.m_60939_(TensuraBlockMaterial.TENSURA_WEB).m_60918_(SoundType.f_56745_).m_60910_().m_60955_().m_60999_().m_60978_(6.0F).m_222994_());
      });
      STICKY_STEEL_COBWEB = registry.register("sticky_steel_cobweb", () -> {
         return new StickySteelCobwebBlock(Properties.m_60939_(TensuraBlockMaterial.TENSURA_WEB).m_60918_(SoundType.f_56745_).m_60910_().m_60955_().m_60999_().m_60978_(8.0F).m_222994_());
      });
      SLIME_CHUNK_BLOCK = registry.register("slime_chunk_block", () -> {
         return new SlimeChunkBlock(Properties.m_60944_(Material.f_76313_, MaterialColor.f_76399_).m_60911_(0.8F).m_60918_(SoundType.f_56750_).m_60955_());
      });
      CHILLED_SLIME_BLOCK = registry.register("chilled_slime_block", () -> {
         return new ChilledSlimeBlock(Properties.m_60944_(Material.f_76313_, MaterialColor.f_76399_).m_60911_(0.8F).m_60918_(SoundType.f_56750_).m_60955_());
      });
      MOTH_EGG = registry.register("moth_egg", () -> {
         return new MothEggBlock(Properties.m_60944_(Material.f_76286_, MaterialColor.f_76400_).m_60978_(0.5F).m_60918_(SoundType.f_56745_).m_60977_().m_60955_());
      });
      CHARYBDIS_CORE = registry.register("charybdis_core", CharybdisCoreBlock::new);
      SPIDER_EGG = registry.register("spider_egg", SpiderEggBlock::new);
      WEB_BLOCK = registry.register("web_block", WebBlock::new);
      WEB_STAIRS = registry.register("web_stairs", () -> {
         Block var10002 = (Block)WEB_BLOCK.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60926_((BlockBehaviour)WEB_BLOCK.get()));
      });
      WEB_SLAB = registry.register("web_slab", () -> {
         return new SlabBlock(Properties.m_60926_((BlockBehaviour)WEB_BLOCK.get()));
      });
      WEBBED_COBBLESTONE = registry.register("webbed_cobblestone", WebbedStoneBlock::new);
      WEBBED_COBBLESTONE_STAIRS = registry.register("webbed_cobblestone_stairs", () -> {
         Block var10002 = (Block)WEBBED_COBBLESTONE.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60926_((BlockBehaviour)WEBBED_COBBLESTONE.get()));
      });
      WEBBED_COBBLESTONE_SLAB = registry.register("webbed_cobblestone_slab", () -> {
         return new SlabBlock(Properties.m_60926_((BlockBehaviour)WEBBED_COBBLESTONE.get()));
      });
      WEBBED_COBBLESTONE_WALL = registry.register("webbed_cobblestone_wall", () -> {
         return new WallBlock(Properties.m_60926_((BlockBehaviour)WEBBED_COBBLESTONE.get()));
      });
      WEBBED_STONE_BRICKS = registry.register("webbed_stone_bricks", WebbedStoneBlock::new);
      WEBBED_STONE_BRICK_STAIRS = registry.register("webbed_stone_brick_stairs", () -> {
         Block var10002 = (Block)WEBBED_STONE_BRICKS.get();
         Objects.requireNonNull(var10002);
         return new StairBlock(var10002::m_49966_, Properties.m_60926_((BlockBehaviour)WEBBED_STONE_BRICKS.get()));
      });
      WEBBED_STONE_BRICK_SLAB = registry.register("webbed_stone_brick_slab", () -> {
         return new SlabBlock(Properties.m_60926_((BlockBehaviour)WEBBED_STONE_BRICKS.get()));
      });
      WEBBED_STONE_BRICK_WALL = registry.register("webbed_stone_brick_wall", () -> {
         return new WallBlock(Properties.m_60926_((BlockBehaviour)WEBBED_STONE_BRICKS.get()));
      });
      BLACK_FIRE = registry.register("black_fire", BlackFireBlock::new);
      HOLY_FIRE = registry.register("holy_fire", HolyFireBlock::new);
      BRICK_MAGIC_ENGINE = registry.register("brick_magic_engine", () -> {
         return new MagicEngineBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(7.0F, 10.0F).m_60955_().m_60918_(SoundType.f_154678_).m_60999_();
         });
      });
      STONE_BRICK_MAGIC_ENGINE = registry.register("stone_brick_magic_engine", () -> {
         return new MagicEngineBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(7.0F, 10.0F).m_60955_().m_60918_(SoundType.f_154678_).m_60999_();
         });
      });
      SMITHING_BENCH = registry.register("smithing_bench", () -> {
         return new SmithingBenchBlock(Material.f_76321_, (properties) -> {
            return properties.m_60913_(3.0F, 1200.0F).m_60918_(SoundType.f_56749_).m_60955_().m_60999_();
         });
      });
      WARP_PAD = registry.register("warp_pad", () -> {
         return new SimpleBlock(Material.f_76278_, (properties) -> {
            return properties.m_60913_(3.0F, 6.0F).m_60918_(SoundType.f_56742_).m_60955_().m_60999_();
         });
      });
      THATCH_BED = registry.register("thatch_bed", () -> {
         return new ThatchBed(Properties.m_60939_(Material.f_76315_).m_60978_(0.5F).m_60918_(SoundType.f_56740_).m_60955_());
      });
      UNLIT_TORCH = registry.register("unlit_torch", () -> {
         return new UnlitStandingTorchBlock(Properties.m_60939_(Material.f_76278_).m_60910_().m_60966_().m_60918_(SoundType.f_56736_), ParticleTypes.f_123744_);
      });
      UNLIT_WALL_TORCH = registry.register("unlit_wall_torch", () -> {
         return new UnlitWallTorchBlock(Properties.m_60939_(Material.f_76278_).m_60910_().m_60966_().m_60918_(SoundType.f_56736_), ParticleTypes.f_123744_);
      });
      UNLIT_LANTERN = registry.register("unlit_lantern", () -> {
         return new UnlitLanternBlock(Properties.m_60939_(Material.f_76279_).m_60999_().m_60978_(3.5F).m_60918_(SoundType.f_56762_));
      });
      HOT_SPRING_WATER = registry.register("hot_spring_water", () -> {
         return new HotSpringWater(TensuraFluids.SOURCE_HOT_SPRING_WATER, Properties.m_60926_(Blocks.f_49990_));
      });
      HIPOKUTE_GRASS = registry.register("hipokute_grass", () -> {
         return new HipokuteGrass(Properties.m_60939_(Material.f_76300_).m_60910_().m_60977_().m_60966_().m_60918_(SoundType.f_56758_));
      });
      POTTED_HIPOKUTE_FLOWER = registry.register("potted_hipokute_flower", () -> {
         return new HipokuteFlowerPotBlock(() -> {
            return (FlowerPotBlock)Blocks.f_50276_;
         }, TensuraMaterialItems.HIPOKUTE_FLOWER, Properties.m_60926_(Blocks.f_50279_).m_60955_());
      });
      LIGHT_AIR = registry.register("light_air", LightAirBlock::new);
      SOLID_SPACE = registry.register("solid_space", SolidSpaceBlock::new);
      ORC_DISASTER_HEAD = registry.register("orc_disaster_head", () -> {
         return new OrcDisasterHead(Properties.m_60939_(Material.f_76310_).m_60955_().m_60978_(1.0F));
      });
   }

   public static class Items {
      private static final DeferredRegister<Item> registry;
      public static final RegistryObject<Item> PALM_LOG;
      public static final RegistryObject<Item> PALM_WOOD;
      public static final RegistryObject<Item> STRIPPED_PALM_LOG;
      public static final RegistryObject<Item> STRIPPED_PALM_WOOD;
      public static final RegistryObject<Item> PALM_PLANKS;
      public static final RegistryObject<Item> PALM_STAIRS;
      public static final RegistryObject<Item> PALM_SLAB;
      public static final RegistryObject<Item> PALM_FENCE;
      public static final RegistryObject<Item> PALM_FENCE_GATE;
      public static final RegistryObject<Item> PALM_DOOR;
      public static final RegistryObject<Item> PALM_TRAPDOOR;
      public static final RegistryObject<Item> PALM_PRESSURE_PLATE;
      public static final RegistryObject<Item> PALM_BUTTON;
      public static final RegistryObject<SignItem> PALM_SIGN;
      public static final RegistryObject<Item> PALM_SAPLING;
      public static final RegistryObject<Item> PALM_LEAVES;
      public static final RegistryObject<Item> SAKURA_LOG;
      public static final RegistryObject<Item> STRIPPED_SAKURA_LOG;
      public static final RegistryObject<Item> SAKURA_WOOD;
      public static final RegistryObject<Item> STRIPPED_SAKURA_WOOD;
      public static final RegistryObject<Item> SAKURA_PLANKS;
      public static final RegistryObject<Item> SAKURA_STAIRS;
      public static final RegistryObject<Item> SAKURA_SLAB;
      public static final RegistryObject<Item> SAKURA_FENCE;
      public static final RegistryObject<Item> SAKURA_FENCE_GATE;
      public static final RegistryObject<Item> SAKURA_DOOR;
      public static final RegistryObject<Item> SAKURA_TRAPDOOR;
      public static final RegistryObject<Item> SAKURA_PRESSURE_PLATE;
      public static final RegistryObject<Item> SAKURA_BUTTON;
      public static final RegistryObject<SignItem> SAKURA_SIGN;
      public static final RegistryObject<Item> SAKURA_SAPLING;
      public static final RegistryObject<Item> SAKURA_LEAVES;
      public static final RegistryObject<Item> THATCH_BLOCK;
      public static final RegistryObject<Item> THATCH_STAIRS;
      public static final RegistryObject<Item> THACH_SLAB;
      public static final RegistryObject<Item> THATCH_BED;
      public static final RegistryObject<Item> SARASA_SAND;
      public static final RegistryObject<Item> SARASA_SANDSTONE;
      public static final RegistryObject<Item> CHISELED_SARASA_SANDSTONE;
      public static final RegistryObject<Item> CUT_SARASA_SANDSTONE;
      public static final RegistryObject<Item> SMOOTH_SARASA_SANDSTONE;
      public static final RegistryObject<Item> SARASA_SANDSTONE_STAIRS;
      public static final RegistryObject<Item> SMOOTH_SARASA_SANDSTONE_STAIRS;
      public static final RegistryObject<Item> SARASA_SANDSTONE_SLAB;
      public static final RegistryObject<Item> CUT_SARASA_SANDSTONE_SLAB;
      public static final RegistryObject<Item> SMOOTH_SARASA_SANDSTONE_SLAB;
      public static final RegistryObject<Item> SARASA_SANDSTONE_WALL;
      public static final RegistryObject<Item> LOW_QUALITY_MAGIC_CRYSTAL_BLOCK;
      public static final RegistryObject<Item> LOW_QUALITY_MAGIC_CRYSTAL_BRICKS;
      public static final RegistryObject<Item> CHISELED_LOW_QUALITY_MAGIC_CRYSTAL_BRICKS;
      public static final RegistryObject<Item> LOW_QUALITY_MAGIC_CRYSTAL_STAIRS;
      public static final RegistryObject<Item> LOW_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS;
      public static final RegistryObject<Item> LOW_QUALITY_MAGIC_CRYSTAL_SLAB;
      public static final RegistryObject<Item> LOW_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB;
      public static final RegistryObject<Item> LOW_QUALITY_MAGIC_CRYSTAL_BRICK_WALL;
      public static final RegistryObject<Item> MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK;
      public static final RegistryObject<Item> MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS;
      public static final RegistryObject<Item> CHISELED_MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS;
      public static final RegistryObject<Item> MEDIUM_QUALITY_MAGIC_CRYSTAL_STAIRS;
      public static final RegistryObject<Item> MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS;
      public static final RegistryObject<Item> MEDIUM_QUALITY_MAGIC_CRYSTAL_SLAB;
      public static final RegistryObject<Item> MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB;
      public static final RegistryObject<Item> MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_WALL;
      public static final RegistryObject<Item> HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK;
      public static final RegistryObject<Item> HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS;
      public static final RegistryObject<Item> CHISELED_HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS;
      public static final RegistryObject<Item> HIGH_QUALITY_MAGIC_CRYSTAL_STAIRS;
      public static final RegistryObject<Item> HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS;
      public static final RegistryObject<Item> HIGH_QUALITY_MAGIC_CRYSTAL_SLAB;
      public static final RegistryObject<Item> HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB;
      public static final RegistryObject<Item> HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_WALL;
      public static final RegistryObject<Item> MAGIC_ORE;
      public static final RegistryObject<Item> SILVER_ORE;
      public static final RegistryObject<Item> DEEPSLATE_MAGIC_ORE;
      public static final RegistryObject<Item> DEEPSLATE_SILVER_ORE;
      public static final RegistryObject<Item> RAW_SILVER_BLOCK;
      public static final RegistryObject<Item> SILVER_BLOCK;
      public static final RegistryObject<Item> MAGIC_ORE_BLOCK;
      public static final RegistryObject<Item> LOW_MAGISTEEL_BLOCK;
      public static final RegistryObject<Item> HIGH_MAGISTEEL_BLOCK;
      public static final RegistryObject<Item> PURE_MAGISTEEL_BLOCK;
      public static final RegistryObject<Item> MITHRIL_BLOCK;
      public static final RegistryObject<Item> ORICHALCUM_BLOCK;
      public static final RegistryObject<Item> ADAMANITE_BLOCK;
      public static final RegistryObject<Item> HIHIIROKANE_BLOCK;
      public static final RegistryObject<StandingAndWallBlockItem> UNLIT_TORCH;
      public static final RegistryObject<Item> UNLIT_LANTERN;
      public static final RegistryObject<Item> STICKY_COBWEB;
      public static final RegistryObject<Item> STICKY_STEEL_COBWEB;
      public static final RegistryObject<Item> SPIDER_EGG;
      public static final RegistryObject<Item> WEB_BLOCK;
      public static final RegistryObject<Item> WEB_STAIRS;
      public static final RegistryObject<Item> WEB_SLAB;
      public static final RegistryObject<Item> WEBBED_COBBLESTONE;
      public static final RegistryObject<Item> WEBBED_COBBLESTONE_STAIRS;
      public static final RegistryObject<Item> WEBBED_COBBLESTONE_SLAB;
      public static final RegistryObject<Item> WEBBED_COBBLESTONE_WALL;
      public static final RegistryObject<Item> WEBBED_STONE_BRICKS;
      public static final RegistryObject<Item> WEBBED_STONE_BRICK_STAIRS;
      public static final RegistryObject<Item> WEBBED_STONE_BRICK_SLAB;
      public static final RegistryObject<Item> WEBBED_STONE_BRICK_WALL;
      public static final RegistryObject<Item> LABYRINTH_BRICK;
      public static final RegistryObject<Item> LABYRINTH_BRICK_STAIR;
      public static final RegistryObject<Item> LABYRINTH_BRICK_SLAB;
      public static final RegistryObject<Item> LABYRINTH_BRICK_TL;
      public static final RegistryObject<Item> LABYRINTH_BRICK_TR;
      public static final RegistryObject<Item> LABYRINTH_BRICK_BL;
      public static final RegistryObject<Item> LABYRINTH_BRICK_BR;
      public static final RegistryObject<Item> LABYRINTH_STONE;
      public static final RegistryObject<Item> LABYRINTH_STONE_STAIR;
      public static final RegistryObject<Item> LABYRINTH_STONE_SLAB;
      public static final RegistryObject<Item> LABYRINTH_STONE_TL;
      public static final RegistryObject<Item> LABYRINTH_STONE_TR;
      public static final RegistryObject<Item> LABYRINTH_STONE_BL;
      public static final RegistryObject<Item> LABYRINTH_STONE_BR;
      public static final RegistryObject<Item> CREAM_LABYRINTH_BRICK;
      public static final RegistryObject<Item> CREAM_LABYRINTH_BRICK_STAIR;
      public static final RegistryObject<Item> CREAM_LABYRINTH_BRICK_SLAB;
      public static final RegistryObject<Item> CREAM_LABYRINTH_BRICK_TL;
      public static final RegistryObject<Item> CREAM_LABYRINTH_BRICK_TR;
      public static final RegistryObject<Item> CREAM_LABYRINTH_BRICK_BL;
      public static final RegistryObject<Item> CREAM_LABYRINTH_BRICK_BR;
      public static final RegistryObject<Item> CREAM_LABYRINTH_STONE;
      public static final RegistryObject<Item> CREAM_LABYRINTH_STONE_STAIR;
      public static final RegistryObject<Item> CREAM_LABYRINTH_STONE_SLAB;
      public static final RegistryObject<Item> CREAM_LABYRINTH_STONE_TL;
      public static final RegistryObject<Item> CREAM_LABYRINTH_STONE_TR;
      public static final RegistryObject<Item> CREAM_LABYRINTH_STONE_BL;
      public static final RegistryObject<Item> CREAM_LABYRINTH_STONE_BR;
      public static final RegistryObject<Item> DARK_LABYRINTH_BRICK;
      public static final RegistryObject<Item> DARK_LABYRINTH_BRICK_STAIR;
      public static final RegistryObject<Item> DARK_LABYRINTH_BRICK_SLAB;
      public static final RegistryObject<Item> DARK_LABYRINTH_BRICK_TL;
      public static final RegistryObject<Item> DARK_LABYRINTH_BRICK_TR;
      public static final RegistryObject<Item> DARK_LABYRINTH_BRICK_BL;
      public static final RegistryObject<Item> DARK_LABYRINTH_BRICK_BR;
      public static final RegistryObject<Item> DARK_LABYRINTH_STONE;
      public static final RegistryObject<Item> DARK_LABYRINTH_STONE_STAIR;
      public static final RegistryObject<Item> DARK_LABYRINTH_STONE_SLAB;
      public static final RegistryObject<Item> DARK_LABYRINTH_STONE_TL;
      public static final RegistryObject<Item> DARK_LABYRINTH_STONE_TR;
      public static final RegistryObject<Item> DARK_LABYRINTH_STONE_BL;
      public static final RegistryObject<Item> DARK_LABYRINTH_STONE_BR;
      public static final RegistryObject<Item> LABYRINTH_LAMP;
      public static final RegistryObject<Item> LABYRINTH_LAMP_TL;
      public static final RegistryObject<Item> LABYRINTH_LAMP_TR;
      public static final RegistryObject<Item> LABYRINTH_LAMP_BL;
      public static final RegistryObject<Item> LABYRINTH_LAMP_BR;
      public static final RegistryObject<Item> LABYRINTH_LIT_LAMP;
      public static final RegistryObject<Item> LABYRINTH_LIT_LAMP_TL;
      public static final RegistryObject<Item> LABYRINTH_LIT_LAMP_TR;
      public static final RegistryObject<Item> LABYRINTH_LIT_LAMP_BL;
      public static final RegistryObject<Item> LABYRINTH_LIT_LAMP_BR;
      public static final RegistryObject<Item> LABYRINTH_CRYSTAL;
      public static final RegistryObject<Item> LABYRINTH_PRAYING_PATH;
      public static final RegistryObject<Item> LABYRINTH_LIGHT_PATH;
      public static final RegistryObject<Item> LABYRINTH_LIGHT_PATH_STAIRS;
      public static final RegistryObject<Item> LABYRINTH_LIGHT_PATH_SLAB;
      public static final RegistryObject<Item> LABYRINTH_PORTAL;
      public static final RegistryObject<Item> LABYRINTH_BARRIER_BLOCK;
      public static final RegistryObject<Item> HELL_PORTAL;
      public static final RegistryObject<Item> KILN;
      public static final RegistryObject<Item> SMITHING_BENCH;
      public static final RegistryObject<Item> BRICK_MAGIC_ENGINE;
      public static final RegistryObject<Item> STONE_BRICK_MAGIC_ENGINE;
      public static final RegistryObject<Item> SLIME_CHUNK_BLOCK;
      public static final RegistryObject<Item> CHILLED_SLIME_BLOCK;
      public static final RegistryObject<Item> MOTH_EGG;
      public static final RegistryObject<Item> CHARYBDIS_CORE;
      public static final RegistryObject<Item> ORC_DISASTER_HEAD;

      public static <T extends Block> RegistryObject<Item> simpleBlockItem(RegistryObject<T> block) {
         return registry.register(block.getId().m_135815_(), () -> {
            return new SimpleBlockItem((Block)block.get());
         });
      }

      public static <T extends Block> RegistryObject<Item> simpleBlockItem(RegistryObject<T> block, CreativeModeTab tab) {
         return registry.register(block.getId().m_135815_(), () -> {
            return new SimpleBlockItem((Block)block.get(), tab);
         });
      }

      public static <T extends Block> RegistryObject<Item> dungeonBlockItem(RegistryObject<T> block) {
         return registry.register(block.getId().m_135815_(), () -> {
            return new SimpleBlockItem((Block)block.get(), (new net.minecraft.world.item.Item.Properties()).m_41491_(TensuraCreativeTab.DUNGEON_BLOCKS).m_41486_());
         });
      }

      public static <T extends Block> RegistryObject<Item> labyrinthBlockItem(RegistryObject<T> block) {
         return fireResistedBlockItem(block, TensuraCreativeTab.DUNGEON_BLOCKS);
      }

      public static <T extends Block> RegistryObject<Item> fireResistedBlockItem(RegistryObject<T> block) {
         return fireResistedBlockItem(block, TensuraCreativeTab.BLOCKS);
      }

      public static <T extends Block> RegistryObject<Item> fireResistedBlockItem(RegistryObject<T> block, CreativeModeTab tab) {
         return registry.register(block.getId().m_135815_(), () -> {
            return new SimpleBlockItem((Block)block.get(), (new net.minecraft.world.item.Item.Properties()).m_41491_(tab).m_41486_());
         });
      }

      static {
         registry = DeferredRegister.create(ForgeRegistries.ITEMS, "tensura");
         PALM_LOG = simpleBlockItem(TensuraBlocks.PALM_LOG);
         PALM_WOOD = simpleBlockItem(TensuraBlocks.PALM_WOOD);
         STRIPPED_PALM_LOG = simpleBlockItem(TensuraBlocks.STRIPPED_PALM_LOG);
         STRIPPED_PALM_WOOD = simpleBlockItem(TensuraBlocks.STRIPPED_PALM_WOOD);
         PALM_PLANKS = simpleBlockItem(TensuraBlocks.PALM_PLANKS);
         PALM_STAIRS = simpleBlockItem(TensuraBlocks.PALM_STAIRS);
         PALM_SLAB = simpleBlockItem(TensuraBlocks.PALM_SLAB);
         PALM_FENCE = simpleBlockItem(TensuraBlocks.PALM_FENCE);
         PALM_FENCE_GATE = simpleBlockItem(TensuraBlocks.PALM_FENCE_GATE);
         PALM_DOOR = simpleBlockItem(TensuraBlocks.PALM_DOOR);
         PALM_TRAPDOOR = simpleBlockItem(TensuraBlocks.PALM_TRAPDOOR);
         PALM_PRESSURE_PLATE = simpleBlockItem(TensuraBlocks.PALM_PRESSURE_PLATE);
         PALM_BUTTON = simpleBlockItem(TensuraBlocks.PALM_BUTTON);
         PALM_SIGN = registry.register("palm_sign", () -> {
            return new SignItem((new net.minecraft.world.item.Item.Properties()).m_41491_(TensuraCreativeTab.BLOCKS).m_41487_(16), (Block)TensuraBlockEntities.Blocks.PALM_STANDING_SIGN.get(), (Block)TensuraBlockEntities.Blocks.PALM_WALL_SIGN.get());
         });
         PALM_SAPLING = simpleBlockItem(TensuraBlocks.PALM_SAPLING);
         PALM_LEAVES = simpleBlockItem(TensuraBlocks.PALM_LEAVES);
         SAKURA_LOG = simpleBlockItem(TensuraBlocks.SAKURA_LOG);
         STRIPPED_SAKURA_LOG = simpleBlockItem(TensuraBlocks.STRIPPED_SAKURA_LOG);
         SAKURA_WOOD = simpleBlockItem(TensuraBlocks.SAKURA_WOOD);
         STRIPPED_SAKURA_WOOD = simpleBlockItem(TensuraBlocks.STRIPPED_SAKURA_WOOD);
         SAKURA_PLANKS = simpleBlockItem(TensuraBlocks.SAKURA_PLANKS);
         SAKURA_STAIRS = simpleBlockItem(TensuraBlocks.SAKURA_STAIRS);
         SAKURA_SLAB = simpleBlockItem(TensuraBlocks.SAKURA_SLAB);
         SAKURA_FENCE = simpleBlockItem(TensuraBlocks.SAKURA_FENCE);
         SAKURA_FENCE_GATE = simpleBlockItem(TensuraBlocks.SAKURA_FENCE_GATE);
         SAKURA_DOOR = simpleBlockItem(TensuraBlocks.SAKURA_DOOR);
         SAKURA_TRAPDOOR = simpleBlockItem(TensuraBlocks.SAKURA_TRAPDOOR);
         SAKURA_PRESSURE_PLATE = simpleBlockItem(TensuraBlocks.SAKURA_PRESSURE_PLATE);
         SAKURA_BUTTON = simpleBlockItem(TensuraBlocks.SAKURA_BUTTON);
         SAKURA_SIGN = registry.register("sakura_sign", () -> {
            return new SignItem((new net.minecraft.world.item.Item.Properties()).m_41491_(TensuraCreativeTab.BLOCKS).m_41487_(16), (Block)TensuraBlockEntities.Blocks.SAKURA_STANDING_SIGN.get(), (Block)TensuraBlockEntities.Blocks.SAKURA_WALL_SIGN.get());
         });
         SAKURA_SAPLING = simpleBlockItem(TensuraBlocks.SAKURA_SAPLING);
         SAKURA_LEAVES = simpleBlockItem(TensuraBlocks.SAKURA_LEAVES);
         THATCH_BLOCK = simpleBlockItem(TensuraBlocks.THATCH_BLOCK);
         THATCH_STAIRS = simpleBlockItem(TensuraBlocks.THATCH_STAIRS);
         THACH_SLAB = simpleBlockItem(TensuraBlocks.THATCH_SLAB);
         THATCH_BED = simpleBlockItem(TensuraBlocks.THATCH_BED);
         SARASA_SAND = simpleBlockItem(TensuraBlocks.SARASA_SAND);
         SARASA_SANDSTONE = simpleBlockItem(TensuraBlocks.SARASA_SANDSTONE);
         CHISELED_SARASA_SANDSTONE = simpleBlockItem(TensuraBlocks.CHISELED_SARASA_SANDSTONE);
         CUT_SARASA_SANDSTONE = simpleBlockItem(TensuraBlocks.CUT_SARASA_SANDSTONE);
         SMOOTH_SARASA_SANDSTONE = simpleBlockItem(TensuraBlocks.SMOOTH_SARASA_SANDSTONE);
         SARASA_SANDSTONE_STAIRS = simpleBlockItem(TensuraBlocks.SARASA_SANDSTONE_STAIRS);
         SMOOTH_SARASA_SANDSTONE_STAIRS = simpleBlockItem(TensuraBlocks.SMOOTH_SARASA_SANDSTONE_STAIRS);
         SARASA_SANDSTONE_SLAB = simpleBlockItem(TensuraBlocks.SARASA_SANDSTONE_SLAB);
         CUT_SARASA_SANDSTONE_SLAB = simpleBlockItem(TensuraBlocks.CUT_SARASA_SANDSTONE_SLAB);
         SMOOTH_SARASA_SANDSTONE_SLAB = simpleBlockItem(TensuraBlocks.SMOOTH_SARASA_SANDSTONE_SLAB);
         SARASA_SANDSTONE_WALL = simpleBlockItem(TensuraBlocks.SARASA_SANDSTONE_WALL);
         LOW_QUALITY_MAGIC_CRYSTAL_BLOCK = simpleBlockItem(TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BLOCK);
         LOW_QUALITY_MAGIC_CRYSTAL_BRICKS = simpleBlockItem(TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICKS);
         CHISELED_LOW_QUALITY_MAGIC_CRYSTAL_BRICKS = simpleBlockItem(TensuraBlocks.CHISELED_LOW_QUALITY_MAGIC_CRYSTAL_BRICKS);
         LOW_QUALITY_MAGIC_CRYSTAL_STAIRS = simpleBlockItem(TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_STAIRS);
         LOW_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS = simpleBlockItem(TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS);
         LOW_QUALITY_MAGIC_CRYSTAL_SLAB = simpleBlockItem(TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_SLAB);
         LOW_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB = simpleBlockItem(TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB);
         LOW_QUALITY_MAGIC_CRYSTAL_BRICK_WALL = simpleBlockItem(TensuraBlocks.LOW_QUALITY_MAGIC_CRYSTAL_BRICK_WALL);
         MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK = simpleBlockItem(TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BLOCK);
         MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS = simpleBlockItem(TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS);
         CHISELED_MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS = simpleBlockItem(TensuraBlocks.CHISELED_MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICKS);
         MEDIUM_QUALITY_MAGIC_CRYSTAL_STAIRS = simpleBlockItem(TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_STAIRS);
         MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS = simpleBlockItem(TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS);
         MEDIUM_QUALITY_MAGIC_CRYSTAL_SLAB = simpleBlockItem(TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_SLAB);
         MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB = simpleBlockItem(TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB);
         MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_WALL = simpleBlockItem(TensuraBlocks.MEDIUM_QUALITY_MAGIC_CRYSTAL_BRICK_WALL);
         HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK = simpleBlockItem(TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BLOCK);
         HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS = simpleBlockItem(TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS);
         CHISELED_HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS = simpleBlockItem(TensuraBlocks.CHISELED_HIGH_QUALITY_MAGIC_CRYSTAL_BRICKS);
         HIGH_QUALITY_MAGIC_CRYSTAL_STAIRS = simpleBlockItem(TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_STAIRS);
         HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS = simpleBlockItem(TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_STAIRS);
         HIGH_QUALITY_MAGIC_CRYSTAL_SLAB = simpleBlockItem(TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_SLAB);
         HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB = simpleBlockItem(TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_SLAB);
         HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_WALL = simpleBlockItem(TensuraBlocks.HIGH_QUALITY_MAGIC_CRYSTAL_BRICK_WALL);
         MAGIC_ORE = simpleBlockItem(TensuraBlocks.MAGIC_ORE);
         SILVER_ORE = simpleBlockItem(TensuraBlocks.SILVER_ORE);
         DEEPSLATE_MAGIC_ORE = simpleBlockItem(TensuraBlocks.DEEPSLATE_MAGIC_ORE);
         DEEPSLATE_SILVER_ORE = simpleBlockItem(TensuraBlocks.DEEPSLATE_SILVER_ORE);
         RAW_SILVER_BLOCK = simpleBlockItem(TensuraBlocks.RAW_SILVER_BLOCK);
         SILVER_BLOCK = simpleBlockItem(TensuraBlocks.SILVER_BLOCK);
         MAGIC_ORE_BLOCK = simpleBlockItem(TensuraBlocks.MAGIC_ORE_BLOCK);
         LOW_MAGISTEEL_BLOCK = simpleBlockItem(TensuraBlocks.LOW_MAGISTEEL_BLOCK);
         HIGH_MAGISTEEL_BLOCK = fireResistedBlockItem(TensuraBlocks.HIGH_MAGISTEEL_BLOCK);
         PURE_MAGISTEEL_BLOCK = fireResistedBlockItem(TensuraBlocks.PURE_MAGISTEEL_BLOCK);
         MITHRIL_BLOCK = fireResistedBlockItem(TensuraBlocks.MITHRIL_BLOCK);
         ORICHALCUM_BLOCK = fireResistedBlockItem(TensuraBlocks.ORICHALCUM_BLOCK);
         ADAMANITE_BLOCK = fireResistedBlockItem(TensuraBlocks.ADAMANTITE_BLOCK);
         HIHIIROKANE_BLOCK = fireResistedBlockItem(TensuraBlocks.HIHIIROKANE_BLOCK);
         UNLIT_TORCH = registry.register("unlit_torch", () -> {
            return new StandingAndWallBlockItem((Block)TensuraBlocks.UNLIT_TORCH.get(), (Block)TensuraBlocks.UNLIT_WALL_TORCH.get(), (new net.minecraft.world.item.Item.Properties()).m_41491_(TensuraCreativeTab.DUNGEON_BLOCKS));
         });
         UNLIT_LANTERN = dungeonBlockItem(TensuraBlocks.UNLIT_LANTERN);
         STICKY_COBWEB = dungeonBlockItem(TensuraBlocks.STICKY_COBWEB);
         STICKY_STEEL_COBWEB = dungeonBlockItem(TensuraBlocks.STICKY_STEEL_COBWEB);
         SPIDER_EGG = dungeonBlockItem(TensuraBlocks.SPIDER_EGG);
         WEB_BLOCK = dungeonBlockItem(TensuraBlocks.WEB_BLOCK);
         WEB_STAIRS = dungeonBlockItem(TensuraBlocks.WEB_STAIRS);
         WEB_SLAB = dungeonBlockItem(TensuraBlocks.WEB_SLAB);
         WEBBED_COBBLESTONE = dungeonBlockItem(TensuraBlocks.WEBBED_COBBLESTONE);
         WEBBED_COBBLESTONE_STAIRS = dungeonBlockItem(TensuraBlocks.WEBBED_COBBLESTONE_STAIRS);
         WEBBED_COBBLESTONE_SLAB = dungeonBlockItem(TensuraBlocks.WEBBED_COBBLESTONE_SLAB);
         WEBBED_COBBLESTONE_WALL = dungeonBlockItem(TensuraBlocks.WEBBED_COBBLESTONE_WALL);
         WEBBED_STONE_BRICKS = dungeonBlockItem(TensuraBlocks.WEBBED_STONE_BRICKS);
         WEBBED_STONE_BRICK_STAIRS = dungeonBlockItem(TensuraBlocks.WEBBED_STONE_BRICK_STAIRS);
         WEBBED_STONE_BRICK_SLAB = dungeonBlockItem(TensuraBlocks.WEBBED_STONE_BRICK_SLAB);
         WEBBED_STONE_BRICK_WALL = dungeonBlockItem(TensuraBlocks.WEBBED_STONE_BRICK_WALL);
         LABYRINTH_BRICK = labyrinthBlockItem(TensuraBlocks.LABYRINTH_BRICK);
         LABYRINTH_BRICK_STAIR = labyrinthBlockItem(TensuraBlocks.LABYRINTH_BRICK_STAIR);
         LABYRINTH_BRICK_SLAB = labyrinthBlockItem(TensuraBlocks.LABYRINTH_BRICK_SLAB);
         LABYRINTH_BRICK_TL = labyrinthBlockItem(TensuraBlocks.LABYRINTH_BRICK_TL);
         LABYRINTH_BRICK_TR = labyrinthBlockItem(TensuraBlocks.LABYRINTH_BRICK_TR);
         LABYRINTH_BRICK_BL = labyrinthBlockItem(TensuraBlocks.LABYRINTH_BRICK_BL);
         LABYRINTH_BRICK_BR = labyrinthBlockItem(TensuraBlocks.LABYRINTH_BRICK_BR);
         LABYRINTH_STONE = labyrinthBlockItem(TensuraBlocks.LABYRINTH_STONE);
         LABYRINTH_STONE_STAIR = labyrinthBlockItem(TensuraBlocks.LABYRINTH_STONE_STAIR);
         LABYRINTH_STONE_SLAB = labyrinthBlockItem(TensuraBlocks.LABYRINTH_STONE_SLAB);
         LABYRINTH_STONE_TL = labyrinthBlockItem(TensuraBlocks.LABYRINTH_STONE_TL);
         LABYRINTH_STONE_TR = labyrinthBlockItem(TensuraBlocks.LABYRINTH_STONE_TR);
         LABYRINTH_STONE_BL = labyrinthBlockItem(TensuraBlocks.LABYRINTH_STONE_BL);
         LABYRINTH_STONE_BR = labyrinthBlockItem(TensuraBlocks.LABYRINTH_STONE_BR);
         CREAM_LABYRINTH_BRICK = labyrinthBlockItem(TensuraBlocks.CREAM_LABYRINTH_BRICK);
         CREAM_LABYRINTH_BRICK_STAIR = labyrinthBlockItem(TensuraBlocks.CREAM_LABYRINTH_BRICK_STAIR);
         CREAM_LABYRINTH_BRICK_SLAB = labyrinthBlockItem(TensuraBlocks.CREAM_LABYRINTH_BRICK_SLAB);
         CREAM_LABYRINTH_BRICK_TL = labyrinthBlockItem(TensuraBlocks.CREAM_LABYRINTH_BRICK_TL);
         CREAM_LABYRINTH_BRICK_TR = labyrinthBlockItem(TensuraBlocks.CREAM_LABYRINTH_BRICK_TR);
         CREAM_LABYRINTH_BRICK_BL = labyrinthBlockItem(TensuraBlocks.CREAM_LABYRINTH_BRICK_BL);
         CREAM_LABYRINTH_BRICK_BR = labyrinthBlockItem(TensuraBlocks.CREAM_LABYRINTH_BRICK_BR);
         CREAM_LABYRINTH_STONE = labyrinthBlockItem(TensuraBlocks.CREAM_LABYRINTH_STONE);
         CREAM_LABYRINTH_STONE_STAIR = labyrinthBlockItem(TensuraBlocks.CREAM_LABYRINTH_STONE_STAIR);
         CREAM_LABYRINTH_STONE_SLAB = labyrinthBlockItem(TensuraBlocks.CREAM_LABYRINTH_STONE_SLAB);
         CREAM_LABYRINTH_STONE_TL = labyrinthBlockItem(TensuraBlocks.CREAM_LABYRINTH_STONE_TL);
         CREAM_LABYRINTH_STONE_TR = labyrinthBlockItem(TensuraBlocks.CREAM_LABYRINTH_STONE_TR);
         CREAM_LABYRINTH_STONE_BL = labyrinthBlockItem(TensuraBlocks.CREAM_LABYRINTH_STONE_BL);
         CREAM_LABYRINTH_STONE_BR = labyrinthBlockItem(TensuraBlocks.CREAM_LABYRINTH_STONE_BR);
         DARK_LABYRINTH_BRICK = labyrinthBlockItem(TensuraBlocks.DARK_LABYRINTH_BRICK);
         DARK_LABYRINTH_BRICK_STAIR = labyrinthBlockItem(TensuraBlocks.DARK_LABYRINTH_BRICK_STAIR);
         DARK_LABYRINTH_BRICK_SLAB = labyrinthBlockItem(TensuraBlocks.DARK_LABYRINTH_BRICK_SLAB);
         DARK_LABYRINTH_BRICK_TL = labyrinthBlockItem(TensuraBlocks.DARK_LABYRINTH_BRICK_TL);
         DARK_LABYRINTH_BRICK_TR = labyrinthBlockItem(TensuraBlocks.DARK_LABYRINTH_BRICK_TR);
         DARK_LABYRINTH_BRICK_BL = labyrinthBlockItem(TensuraBlocks.DARK_LABYRINTH_BRICK_BL);
         DARK_LABYRINTH_BRICK_BR = labyrinthBlockItem(TensuraBlocks.DARK_LABYRINTH_BRICK_BR);
         DARK_LABYRINTH_STONE = labyrinthBlockItem(TensuraBlocks.DARK_LABYRINTH_STONE);
         DARK_LABYRINTH_STONE_STAIR = labyrinthBlockItem(TensuraBlocks.DARK_LABYRINTH_STONE_STAIR);
         DARK_LABYRINTH_STONE_SLAB = labyrinthBlockItem(TensuraBlocks.DARK_LABYRINTH_STONE_SLAB);
         DARK_LABYRINTH_STONE_TL = labyrinthBlockItem(TensuraBlocks.DARK_LABYRINTH_STONE_TL);
         DARK_LABYRINTH_STONE_TR = labyrinthBlockItem(TensuraBlocks.DARK_LABYRINTH_STONE_TR);
         DARK_LABYRINTH_STONE_BL = labyrinthBlockItem(TensuraBlocks.DARK_LABYRINTH_STONE_BL);
         DARK_LABYRINTH_STONE_BR = labyrinthBlockItem(TensuraBlocks.DARK_LABYRINTH_STONE_BR);
         LABYRINTH_LAMP = labyrinthBlockItem(TensuraBlocks.LABYRINTH_LAMP);
         LABYRINTH_LAMP_TL = labyrinthBlockItem(TensuraBlocks.LABYRINTH_LAMP_TL);
         LABYRINTH_LAMP_TR = labyrinthBlockItem(TensuraBlocks.LABYRINTH_LAMP_TR);
         LABYRINTH_LAMP_BL = labyrinthBlockItem(TensuraBlocks.LABYRINTH_LAMP_BL);
         LABYRINTH_LAMP_BR = labyrinthBlockItem(TensuraBlocks.LABYRINTH_LAMP_BR);
         LABYRINTH_LIT_LAMP = labyrinthBlockItem(TensuraBlocks.LABYRINTH_LIT_LAMP);
         LABYRINTH_LIT_LAMP_TL = labyrinthBlockItem(TensuraBlocks.LABYRINTH_LIT_LAMP_TL);
         LABYRINTH_LIT_LAMP_TR = labyrinthBlockItem(TensuraBlocks.LABYRINTH_LIT_LAMP_TR);
         LABYRINTH_LIT_LAMP_BL = labyrinthBlockItem(TensuraBlocks.LABYRINTH_LIT_LAMP_BL);
         LABYRINTH_LIT_LAMP_BR = labyrinthBlockItem(TensuraBlocks.LABYRINTH_LIT_LAMP_BR);
         LABYRINTH_CRYSTAL = labyrinthBlockItem(TensuraBlocks.LABYRINTH_CRYSTAL);
         LABYRINTH_PRAYING_PATH = labyrinthBlockItem(TensuraBlocks.LABYRINTH_PRAYING_PATH);
         LABYRINTH_LIGHT_PATH = labyrinthBlockItem(TensuraBlocks.LABYRINTH_LIGHT_PATH);
         LABYRINTH_LIGHT_PATH_STAIRS = labyrinthBlockItem(TensuraBlocks.LABYRINTH_LIGHT_PATH_STAIRS);
         LABYRINTH_LIGHT_PATH_SLAB = labyrinthBlockItem(TensuraBlocks.LABYRINTH_LIGHT_PATH_SLAB);
         LABYRINTH_PORTAL = labyrinthBlockItem(TensuraBlocks.LABYRINTH_PORTAL);
         LABYRINTH_BARRIER_BLOCK = labyrinthBlockItem(TensuraBlocks.LABYRINTH_BARRIER_BLOCK);
         HELL_PORTAL = labyrinthBlockItem(TensuraBlocks.HELL_PORTAL);
         KILN = fireResistedBlockItem(TensuraBlockEntities.Blocks.KILN);
         SMITHING_BENCH = simpleBlockItem(TensuraBlocks.SMITHING_BENCH);
         BRICK_MAGIC_ENGINE = fireResistedBlockItem(TensuraBlocks.BRICK_MAGIC_ENGINE);
         STONE_BRICK_MAGIC_ENGINE = fireResistedBlockItem(TensuraBlocks.STONE_BRICK_MAGIC_ENGINE);
         SLIME_CHUNK_BLOCK = simpleBlockItem(TensuraBlocks.SLIME_CHUNK_BLOCK);
         CHILLED_SLIME_BLOCK = simpleBlockItem(TensuraBlocks.CHILLED_SLIME_BLOCK);
         MOTH_EGG = simpleBlockItem(TensuraBlocks.MOTH_EGG);
         CHARYBDIS_CORE = simpleBlockItem(TensuraBlocks.CHARYBDIS_CORE);
         ORC_DISASTER_HEAD = registry.register("orc_disaster_head", OrcDisasterHeadItem::new);
      }
   }
}
