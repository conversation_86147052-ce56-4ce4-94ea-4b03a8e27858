package com.github.manasmods.tensura.registry.fluids;

import com.mojang.math.Vector3f;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.level.pathfinder.BlockPathTypes;
import net.minecraftforge.common.SoundActions;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.fluids.FluidType;
import net.minecraftforge.fluids.FluidType.Properties;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;
import net.minecraftforge.registries.ForgeRegistries.Keys;

public class TensuraFluidTypes {
   private static final ResourceLocation WATER_SOURCE = new ResourceLocation("block/water_still");
   private static final ResourceLocation WATER_FLOWING = new ResourceLocation("block/water_flow");
   private static final ResourceLocation WATER_OVERLAY = new ResourceLocation("block/water_overlay");
   public static final DeferredRegister<FluidType> FLUID_TYPES;
   public static final RegistryObject<FluidType> HOT_SPRING_WATER;

   private static RegistryObject<FluidType> register(String name, Vector3f fogColor, int color, Properties properties) {
      return FLUID_TYPES.register(name, () -> {
         return new TensuraBaseFluidType(WATER_SOURCE, WATER_FLOWING, WATER_OVERLAY, color, fogColor, properties);
      });
   }

   public static void init(IEventBus modEventBus) {
      FLUID_TYPES.register(modEventBus);
   }

   static {
      FLUID_TYPES = DeferredRegister.create(Keys.FLUID_TYPES, "tensura");
      HOT_SPRING_WATER = register("hot_spring_water", new Vector3f(0.7529412F, 0.84705883F, 1.0F), -1589389842, Properties.create().fallDistanceModifier(0.0F).sound(SoundActions.BUCKET_FILL, SoundEvents.f_11781_).sound(SoundActions.BUCKET_EMPTY, SoundEvents.f_11778_).sound(SoundActions.FLUID_VAPORIZE, SoundEvents.f_11937_).supportsBoating(false).canDrown(true).canExtinguish(true).canHydrate(true).canPushEntity(true).density(3000).viscosity(6000).pathType(BlockPathTypes.WATER).adjacentPathType((BlockPathTypes)null).canSwim(true));
   }
}
