package com.github.manasmods.tensura.registry.fluids;

import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.tags.FluidTags;
import net.minecraft.util.RandomSource;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.material.FlowingFluid;
import net.minecraft.world.level.material.Fluid;
import net.minecraft.world.level.material.FluidState;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.fluids.ForgeFlowingFluid.Flowing;
import net.minecraftforge.fluids.ForgeFlowingFluid.Properties;
import net.minecraftforge.fluids.ForgeFlowingFluid.Source;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;
import org.jetbrains.annotations.Nullable;

public class TensuraFluids {
   public static final DeferredRegister<Fluid> FLUIDS;
   public static final RegistryObject<FlowingFluid> SOURCE_HOT_SPRING_WATER;
   public static final RegistryObject<FlowingFluid> FLOWING_HOT_SPRING_WATER;
   public static final Properties HOT_SPRING_PROPERTIES;

   private static void customSpread(LevelAccessor pLevel, BlockPos pPos, Direction pDirection) {
      BlockPos pos = pPos.m_121945_(pDirection);
      FluidState fluidState = pLevel.m_6425_(pos);
      if (pLevel.m_8055_(pos).m_60767_().m_76332_()) {
         if (fluidState.getFluidType() != TensuraFluidTypes.HOT_SPRING_WATER.get()) {
            if (fluidState.m_205070_(FluidTags.f_13131_)) {
               int i = pLevel.m_213780_().m_216332_(1, 3);
               Block block = i == 1 ? Blocks.f_50228_ : (i == 2 ? Blocks.f_50334_ : Blocks.f_50122_);
               pLevel.m_7731_(pos, block.m_49966_(), 3);
               pLevel.m_5594_((Player)null, pos, SoundEvents.f_11996_, SoundSource.BLOCKS, 0.5F, 0.5F);
            } else if (fluidState.m_205070_(FluidTags.f_13132_)) {
               Block block = pLevel.m_213780_().m_188499_() ? Blocks.f_152497_ : Blocks.f_152496_;
               pLevel.m_7731_(pos, block.m_49966_(), 3);
               pLevel.m_5594_((Player)null, pos, SoundEvents.f_11937_, SoundSource.BLOCKS, 0.5F, 0.5F);
               pLevel.m_46796_(1501, pos, 0);
            }
         }

      }
   }

   private static void customAnimateTick(Level pLevel, BlockPos pPos, FluidState pState, RandomSource pRandom) {
      if (!pState.m_76170_() && !(Boolean)pState.m_61143_(BlockStateProperties.f_61434_)) {
         if (pRandom.m_188503_(64) == 0) {
            pLevel.m_7785_((double)pPos.m_123341_() + 0.5D, (double)pPos.m_123342_() + 0.5D, (double)pPos.m_123343_() + 0.5D, SoundEvents.f_12540_, SoundSource.BLOCKS, pRandom.m_188501_() * 0.25F + 0.75F, pRandom.m_188501_() + 0.5F, false);
         }
      } else if (pRandom.m_188503_(10) == 0) {
         pLevel.m_7106_(ParticleTypes.f_123768_, (double)pPos.m_123341_() + pRandom.m_188500_(), (double)pPos.m_123342_() + pRandom.m_188500_(), (double)pPos.m_123343_() + pRandom.m_188500_(), 0.0D, 0.0D, 0.0D);
      }

   }

   public static void init(IEventBus modEventBus) {
      FLUIDS.register(modEventBus);
   }

   static {
      FLUIDS = DeferredRegister.create(ForgeRegistries.FLUIDS, "tensura");
      SOURCE_HOT_SPRING_WATER = FLUIDS.register("source_hot_spring_water", () -> {
         return new Source(HOT_SPRING_PROPERTIES) {
            protected void m_6364_(LevelAccessor pLevel, BlockPos pPos, BlockState pBlockState, Direction pDirection, FluidState pFluidState) {
               TensuraFluids.customSpread(pLevel, pPos, pDirection);
               super.m_6364_(pLevel, pPos, pBlockState, pDirection, pFluidState);
            }

            @Nullable
            protected ParticleOptions m_7792_() {
               return ParticleTypes.f_123803_;
            }

            protected void m_213811_(Level pLevel, BlockPos pPos, FluidState pState, RandomSource pRandom) {
               TensuraFluids.customAnimateTick(pLevel, pPos, pState, pRandom);
               super.m_213811_(pLevel, pPos, pState, pRandom);
            }
         };
      });
      FLOWING_HOT_SPRING_WATER = FLUIDS.register("flowing_hot_spring_water", () -> {
         return new Flowing(HOT_SPRING_PROPERTIES) {
            protected void m_6364_(LevelAccessor pLevel, BlockPos pPos, BlockState pBlockState, Direction pDirection, FluidState pFluidState) {
               TensuraFluids.customSpread(pLevel, pPos, pDirection);
               super.m_6364_(pLevel, pPos, pBlockState, pDirection, pFluidState);
            }

            protected void m_213811_(Level pLevel, BlockPos pPos, FluidState pState, RandomSource pRandom) {
               TensuraFluids.customAnimateTick(pLevel, pPos, pState, pRandom);
               super.m_213811_(pLevel, pPos, pState, pRandom);
            }
         };
      });
      HOT_SPRING_PROPERTIES = (new Properties(TensuraFluidTypes.HOT_SPRING_WATER, SOURCE_HOT_SPRING_WATER, FLOWING_HOT_SPRING_WATER)).levelDecreasePerBlock(1).slopeFindDistance(4).block(TensuraBlocks.HOT_SPRING_WATER).bucket(Items.f_42447_.m_204114_());
   }
}
