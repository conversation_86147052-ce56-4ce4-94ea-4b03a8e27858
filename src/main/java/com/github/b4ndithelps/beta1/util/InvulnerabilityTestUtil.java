package com.github.b4ndithelps.beta1.util;

import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.damagesource.DamageSources;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.entity.player.Player;
import net.minecraft.network.chat.Component;

/**
 * Utility class for testing invulnerability effects
 * 用于测试无敌效果的工具类
 */
public class InvulnerabilityTestUtil {
    
    /**
     * List of invulnerable entity types (same as in the Mixin)
     * 无敌生物类型列表（与Mixin中的相同）
     */
    private static final String[] INVULNERABLE_ENTITIES = {
        "trbeyond:dryad",
        "trbeyond:kaijin", 
        "trbeyond:gozul",
        "trbeyond:guild_lady",
        "trbeyond:koby",
        "trbeyond:kurobe"
    };
    
    /**
     * Check if an entity should be invulnerable
     * 检查生物是否应该无敌
     * 
     * @param entity The entity to check
     * @return true if the entity should be invulnerable
     */
    public static boolean isInvulnerableEntity(LivingEntity entity) {
        String entityType = entity.getType().toString();
        
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Test invulnerability for a specific entity
     * 测试特定生物的无敌效果
     * 
     * @param entity The entity to test
     * @param player The player performing the test (for feedback)
     * @return true if the entity is properly invulnerable
     */
    public static boolean testEntityInvulnerability(LivingEntity entity, Player player) {
        if (!isInvulnerableEntity(entity)) {
            if (player != null) {
                player.sendSystemMessage(Component.literal("§c这个生物不在无敌列表中: " + entity.getType().toString()));
            }
            return false;
        }
        
        // Test various invulnerability methods
        boolean isInvulnerable = entity.isInvulnerable();
        boolean isDeadOrDying = entity.isDeadOrDying();
        float currentHealth = entity.getHealth();
        float maxHealth = entity.getMaxHealth();
        int hurtTime = entity.hurtTime;
        int hurtDuration = entity.hurtDuration;
        
        // Check if all invulnerability conditions are met
        boolean allTestsPassed = true;
        StringBuilder testResults = new StringBuilder();
        testResults.append("§6无敌测试结果 for ").append(entity.getType().toString()).append(":\n");
        
        // Test 1: isInvulnerable should return true
        if (isInvulnerable) {
            testResults.append("§a✓ isInvulnerable: true\n");
        } else {
            testResults.append("§c✗ isInvulnerable: false (应该为true)\n");
            allTestsPassed = false;
        }
        
        // Test 2: isDeadOrDying should return false
        if (!isDeadOrDying) {
            testResults.append("§a✓ isDeadOrDying: false\n");
        } else {
            testResults.append("§c✗ isDeadOrDying: true (应该为false)\n");
            allTestsPassed = false;
        }
        
        // Test 3: Health should be at maximum
        if (currentHealth >= maxHealth) {
            testResults.append("§a✓ 生命值: ").append(currentHealth).append("/").append(maxHealth).append("\n");
        } else {
            testResults.append("§c✗ 生命值: ").append(currentHealth).append("/").append(maxHealth).append(" (应该满血)\n");
            allTestsPassed = false;
        }
        
        // Test 4: Hurt animation values should be 0
        if (hurtTime == 0 && hurtDuration == 0) {
            testResults.append("§a✓ 受伤动画值: hurtTime=").append(hurtTime).append(", hurtDuration=").append(hurtDuration).append("\n");
        } else {
            testResults.append("§c✗ 受伤动画值: hurtTime=").append(hurtTime).append(", hurtDuration=").append(hurtDuration).append(" (应该都为0)\n");
            allTestsPassed = false;
        }
        
        if (allTestsPassed) {
            testResults.append("§a✓ 所有测试通过！生物已正确设置为无敌状态。");
        } else {
            testResults.append("§c✗ 部分测试失败！请检查Mixin配置。");
        }
        
        if (player != null) {
            player.sendSystemMessage(Component.literal(testResults.toString()));
        }
        
        return allTestsPassed;
    }
    
    /**
     * Attempt to damage an entity and verify it takes no damage
     * 尝试伤害生物并验证其不受伤害
     * 
     * @param entity The entity to test
     * @param damageAmount The amount of damage to attempt
     * @param player The player performing the test (for feedback)
     * @return true if the entity properly resisted damage
     */
    public static boolean testDamageResistance(LivingEntity entity, float damageAmount, Player player) {
        if (!isInvulnerableEntity(entity)) {
            if (player != null) {
                player.sendSystemMessage(Component.literal("§c这个生物不在无敌列表中: " + entity.getType().toString()));
            }
            return false;
        }
        
        float healthBefore = entity.getHealth();
        
        // Try to damage the entity with generic damage
        if (entity.level() instanceof ServerLevel serverLevel) {
            DamageSources damageSources = serverLevel.damageSources();
            DamageSource genericDamage = damageSources.generic();
            
            // Attempt to hurt the entity
            boolean damageResult = entity.hurt(genericDamage, damageAmount);
            
            float healthAfter = entity.getHealth();
            int hurtTimeAfter = entity.hurtTime;
            int hurtDurationAfter = entity.hurtDuration;
            
            boolean testPassed = !damageResult && 
                                healthBefore == healthAfter && 
                                hurtTimeAfter == 0 && 
                                hurtDurationAfter == 0;
            
            if (player != null) {
                if (testPassed) {
                    player.sendSystemMessage(Component.literal(
                        "§a✓ 伤害抗性测试通过！\n" +
                        "§7尝试造成 " + damageAmount + " 点伤害\n" +
                        "§7生命值: " + healthBefore + " → " + healthAfter + "\n" +
                        "§7hurt方法返回: " + damageResult + " (应该为false)\n" +
                        "§7受伤动画: hurtTime=" + hurtTimeAfter + ", hurtDuration=" + hurtDurationAfter
                    ));
                } else {
                    player.sendSystemMessage(Component.literal(
                        "§c✗ 伤害抗性测试失败！\n" +
                        "§7尝试造成 " + damageAmount + " 点伤害\n" +
                        "§7生命值: " + healthBefore + " → " + healthAfter + "\n" +
                        "§7hurt方法返回: " + damageResult + " (应该为false)\n" +
                        "§7受伤动画: hurtTime=" + hurtTimeAfter + ", hurtDuration=" + hurtDurationAfter
                    ));
                }
            }
            
            return testPassed;
        }
        
        return false;
    }
    
    /**
     * Get a formatted list of all invulnerable entities
     * 获取所有无敌生物的格式化列表
     * 
     * @return A formatted string listing all invulnerable entities
     */
    public static String getInvulnerableEntitiesList() {
        StringBuilder list = new StringBuilder();
        list.append("§6无敌生物列表:\n");
        for (int i = 0; i < INVULNERABLE_ENTITIES.length; i++) {
            list.append("§7").append(i + 1).append(". §a").append(INVULNERABLE_ENTITIES[i]).append("\n");
        }
        return list.toString();
    }
}
