package com.github.b4ndithelps.beta1.command;

import com.github.b4ndithelps.beta1.util.InvulnerabilityTestUtil;
import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.FloatArgumentType;
import com.mojang.brigadier.context.CommandContext;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.commands.arguments.EntityArgument;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

/**
 * Command for testing invulnerability effects
 * 用于测试无敌效果的命令
 */
public class TestInvulnerabilityCommand {
    
    /**
     * Register the command
     * 注册命令
     */
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        dispatcher.register(Commands.literal("testinvulnerability")
            .requires(source -> source.hasPermission(2)) // Requires OP level 2
            .then(Commands.literal("list")
                .executes(TestInvulnerabilityCommand::listInvulnerableEntities))
            .then(Commands.literal("check")
                .then(Commands.argument("entity", EntityArgument.entity())
                    .executes(TestInvulnerabilityCommand::checkEntityInvulnerability)))
            .then(Commands.literal("damage")
                .then(Commands.argument("entity", EntityArgument.entity())
                    .then(Commands.argument("amount", FloatArgumentType.floatArg(0.1f, 1000.0f))
                        .executes(TestInvulnerabilityCommand::testDamageResistance))))
        );
    }
    
    /**
     * List all invulnerable entities
     * 列出所有无敌生物
     */
    private static int listInvulnerableEntities(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();
        
        String list = InvulnerabilityTestUtil.getInvulnerableEntitiesList();
        source.sendSuccess(() -> Component.literal(list), false);
        
        return 1;
    }
    
    /**
     * Check if a specific entity is properly invulnerable
     * 检查特定生物是否正确无敌
     */
    private static int checkEntityInvulnerability(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();
        
        try {
            Entity entity = EntityArgument.getEntity(context, "entity");
            
            if (!(entity instanceof LivingEntity livingEntity)) {
                source.sendFailure(Component.literal("§c目标必须是生物实体！"));
                return 0;
            }
            
            Player player = null;
            if (source.getEntity() instanceof Player) {
                player = (Player) source.getEntity();
            }
            
            boolean testResult = InvulnerabilityTestUtil.testEntityInvulnerability(livingEntity, player);
            
            if (testResult) {
                source.sendSuccess(() -> Component.literal("§a无敌测试完成 - 所有测试通过！"), false);
            } else {
                source.sendFailure(Component.literal("§c无敌测试完成 - 部分测试失败！"));
            }
            
            return testResult ? 1 : 0;
            
        } catch (Exception e) {
            source.sendFailure(Component.literal("§c命令执行失败: " + e.getMessage()));
            return 0;
        }
    }
    
    /**
     * Test damage resistance of a specific entity
     * 测试特定生物的伤害抗性
     */
    private static int testDamageResistance(CommandContext<CommandSourceStack> context) {
        CommandSourceStack source = context.getSource();
        
        try {
            Entity entity = EntityArgument.getEntity(context, "entity");
            float damageAmount = FloatArgumentType.getFloat(context, "amount");
            
            if (!(entity instanceof LivingEntity livingEntity)) {
                source.sendFailure(Component.literal("§c目标必须是生物实体！"));
                return 0;
            }
            
            Player player = null;
            if (source.getEntity() instanceof Player) {
                player = (Player) source.getEntity();
            }
            
            boolean testResult = InvulnerabilityTestUtil.testDamageResistance(livingEntity, damageAmount, player);
            
            if (testResult) {
                source.sendSuccess(() -> Component.literal("§a伤害抗性测试通过！"), false);
            } else {
                source.sendFailure(Component.literal("§c伤害抗性测试失败！"));
            }
            
            return testResult ? 1 : 0;
            
        } catch (Exception e) {
            source.sendFailure(Component.literal("§c命令执行失败: " + e.getMessage()));
            return 0;
        }
    }
}
