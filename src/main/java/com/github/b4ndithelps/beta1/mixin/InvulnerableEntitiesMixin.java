package com.github.b4ndithelps.beta1.mixin;

import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.LivingEntity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * Mixin to make specific entities invulnerable to all damage
 * 为指定的生物添加无敌效果的Mixin类
 * 
 * 模仿HurtDummyEntity的无敌机制，为以下生物提供完全无敌效果：
 * - trbeyond:dryad (树精)
 * - trbeyond:kaijin (魁人)
 * - trbeyond:gozul (哥泽尔)
 * - trbeyond:guild_lady (公会女士)
 * - trbeyond:koby (科比)
 * - trbeyond:kurobe (黑部)
 */
@Mixin(LivingEntity.class)
public class InvulnerableEntitiesMixin {
    
    /**
     * List of entity types that should be invulnerable
     * 应该无敌的生物类型列表
     */
    private static final String[] INVULNERABLE_ENTITIES = {
        "trbeyond:dryad",
        "trbeyond:kaijin", 
        "trbeyond:gozul",
        "trbeyond:guild_lady",
        "trbeyond:koby",
        "trbeyond:kurobe"
    };
    
    /**
     * Intercept the hurt method to prevent damage for specified entities
     * 拦截hurt方法，为指定生物阻止伤害
     * 
     * 这个方法模仿了HurtDummyEntity.hurt()的行为：
     * 1. 检查是否为指定的无敌生物
     * 2. 重置受伤动画值
     * 3. 取消伤害并返回false
     */
    @Inject(method = "hurt", at = @At("HEAD"), cancellable = true)
    private void preventDamageForInvulnerableEntities(DamageSource damageSource, float amount, CallbackInfoReturnable<Boolean> cir) {
        LivingEntity entity = (LivingEntity) (Object) this;
        String entityType = entity.getType().toString();
        
        // Check if this entity should be invulnerable
        // 检查这个生物是否应该无敌
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                // Reset hurt animation values to prevent visual damage effects
                // 重置受伤动画值以防止视觉伤害效果 (模仿HurtDummyEntity的行为)
                entity.hurtTime = 0;
                entity.hurtDuration = 0;
                
                // Cancel the damage and return false (no damage taken)
                // 取消伤害并返回false（未受到伤害）
                cir.setReturnValue(false);
                return;
            }
        }
    }
    

    
    /**
     * Override isInvulnerable method for specified entities
     * 为指定生物重写isInvulnerable方法
     *
     * 完全模仿HurtDummyEntity.isInvulnerable()方法
     */
    @Inject(method = "isInvulnerable", at = @At("HEAD"), cancellable = true)
    private void makeEntitiesInvulnerable(CallbackInfoReturnable<Boolean> cir) {
        LivingEntity entity = (LivingEntity) (Object) this;
        String entityType = entity.getType().toString();

        // Check if this entity should be invulnerable
        // 检查这个生物是否应该无敌
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                cir.setReturnValue(true);
                return;
            }
        }
    }

    /**
     * Override isInvulnerableTo method for specified entities
     * 为指定生物重写isInvulnerableTo方法
     *
     * 完全模仿HurtDummyEntity.isInvulnerableTo()方法
     */
    @Inject(method = "isInvulnerableTo", at = @At("HEAD"), cancellable = true)
    private void makeEntitiesInvulnerableTo(DamageSource damageSource, CallbackInfoReturnable<Boolean> cir) {
        LivingEntity entity = (LivingEntity) (Object) this;
        String entityType = entity.getType().toString();

        // Check if this entity should be invulnerable
        // 检查这个生物是否应该无敌
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                cir.setReturnValue(true);
                return;
            }
        }
    }

    /**
     * Prevent death for specified entities
     * 为指定生物防止死亡
     *
     * 完全模仿HurtDummyEntity.isDeadOrDying()方法
     */
    @Inject(method = "isDeadOrDying", at = @At("HEAD"), cancellable = true)
    private void preventDeathForInvulnerableEntities(CallbackInfoReturnable<Boolean> cir) {
        LivingEntity entity = (LivingEntity) (Object) this;
        String entityType = entity.getType().toString();

        // Check if this entity should be invulnerable
        // 检查这个生物是否应该无敌
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                // Always return false - entity is never dead or dying
                // 总是返回false - 生物永远不会死亡或濒死
                cir.setReturnValue(false);
                return;
            }
        }
    }


    
    /**
     * Override tick method exactly like HurtDummyEntity
     * 完全按照HurtDummyEntity的tick方法重写
     *
     * HurtDummyEntity.tick()的完整逻辑：
     * 1. 在开始时重置受伤值
     * 2. 调用super.tick()
     * 3. 处理动画逻辑（我们跳过）
     */
    @Inject(method = "tick", at = @At("HEAD"))
    private void resetHurtValuesAtTickStart(CallbackInfo ci) {
        LivingEntity entity = (LivingEntity) (Object) this;
        String entityType = entity.getType().toString();

        // Check if this entity should be invulnerable
        // 检查这个生物是否应该无敌
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                // 完全模仿HurtDummyEntity.tick()的开始部分
                entity.hurtTime = 0;
                entity.hurtDuration = 0;
                return;
            }
        }
    }

    /**
     * Additional protection after tick completes
     * tick完成后的额外保护
     */
    @Inject(method = "tick", at = @At("TAIL"))
    private void additionalProtectionAfterTick(CallbackInfo ci) {
        LivingEntity entity = (LivingEntity) (Object) this;
        String entityType = entity.getType().toString();

        // Check if this entity should be invulnerable
        // 检查这个生物是否应该无敌
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                // 确保无敌状态和满血（像HurtDummyEntity构造函数一样）
                entity.setInvulnerable(true);
                entity.setHealth(entity.getMaxHealth());

                // 再次重置受伤值，确保没有被其他代码修改
                entity.hurtTime = 0;
                entity.hurtDuration = 0;
                return;
            }
        }
    }
    
    /**
     * Override baseTick method exactly like HurtDummyEntity
     * 完全按照HurtDummyEntity的baseTick方法重写
     *
     * HurtDummyEntity.baseTick()的完整逻辑：
     * 1. 保存当前hurtTime
     * 2. 调用super.baseTick()
     * 3. 重置hurtTime和hurtDuration为0
     */
    @Inject(method = "baseTick", at = @At("TAIL"))
    private void resetHurtValuesInBaseTick(CallbackInfo ci) {
        LivingEntity entity = (LivingEntity) (Object) this;
        String entityType = entity.getType().toString();

        // Check if this entity should be invulnerable
        // 检查这个生物是否应该无敌
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                // 完全模仿HurtDummyEntity.baseTick()的逻辑
                // super.baseTick()已经被调用了，现在重置受伤值
                entity.hurtTime = 0;
                entity.hurtDuration = 0;
                return;
            }
        }
    }



}
