package com.github.b4ndithelps.beta1.mixin;

import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.LivingEntity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

/**
 * Mixin to make specific entities invulnerable to all damage
 * 为指定的生物添加无敌效果的Mixin类
 * 
 * 模仿HurtDummyEntity的无敌机制，为以下生物提供完全无敌效果：
 * - trbeyond:dryad (树精)
 * - trbeyond:kaijin (魁人)
 * - trbeyond:gozul (哥泽尔)
 * - trbeyond:guild_lady (公会女士)
 * - trbeyond:koby (科比)
 * - trbeyond:kurobe (黑部)
 */
@Mixin(LivingEntity.class)
public class InvulnerableEntitiesMixin {
    
    /**
     * List of entity types that should be invulnerable
     * 应该无敌的生物类型列表
     */
    private static final String[] INVULNERABLE_ENTITIES = {
        "trbeyond:dryad",
        "trbeyond:kaijin", 
        "trbeyond:gozul",
        "trbeyond:guild_lady",
        "trbeyond:koby",
        "trbeyond:kurobe"
    };
    
    /**
     * Intercept the hurt method to prevent damage for specified entities
     * 拦截hurt方法，为指定生物阻止伤害
     * 
     * 这个方法模仿了HurtDummyEntity.hurt()的行为：
     * 1. 检查是否为指定的无敌生物
     * 2. 重置受伤动画值
     * 3. 取消伤害并返回false
     */
    @Inject(method = "hurt", at = @At("HEAD"), cancellable = true)
    private void preventDamageForInvulnerableEntities(DamageSource damageSource, float amount, CallbackInfoReturnable<Boolean> cir) {
        LivingEntity entity = (LivingEntity) (Object) this;
        String entityType = entity.getType().toString();
        
        // Check if this entity should be invulnerable
        // 检查这个生物是否应该无敌
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                // Reset hurt animation values to prevent visual damage effects
                // 重置受伤动画值以防止视觉伤害效果 (模仿HurtDummyEntity的行为)
                entity.hurtTime = 0;
                entity.hurtDuration = 0;
                
                // Cancel the damage and return false (no damage taken)
                // 取消伤害并返回false（未受到伤害）
                cir.setReturnValue(false);
                return;
            }
        }
    }
    
    /**
     * Make specified entities invulnerable to damage sources
     * 使指定生物对伤害源无敌
     *
     * 模仿HurtDummyEntity.isInvulnerableTo()方法
     */
    @Inject(method = "isInvulnerableTo", at = @At("HEAD"), cancellable = true)
    private void makeEntitiesInvulnerableTo(DamageSource damageSource, CallbackInfoReturnable<Boolean> cir) {
        LivingEntity entity = (LivingEntity) (Object) this;
        String entityType = entity.getType().toString();

        // Check if this entity should be invulnerable
        // 检查这个生物是否应该无敌
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                cir.setReturnValue(true);
                return;
            }
        }
    }
    
    /**
     * Override isInvulnerable method for specified entities
     * 为指定生物重写isInvulnerable方法
     *
     * 模仿HurtDummyEntity.isInvulnerable()方法
     */
    @Inject(method = "isInvulnerable", at = @At("HEAD"), cancellable = true)
    private void makeEntitiesInvulnerable(CallbackInfoReturnable<Boolean> cir) {
        LivingEntity entity = (LivingEntity) (Object) this;
        String entityType = entity.getType().toString();

        // Check if this entity should be invulnerable
        // 检查这个生物是否应该无敌
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                cir.setReturnValue(true);
                return;
            }
        }
    }
    
    /**
     * Prevent death for specified entities
     * 为指定生物防止死亡
     * 
     * 模仿HurtDummyEntity.isDeadOrDying()方法
     */
    @Inject(method = "isDeadOrDying", at = @At("HEAD"), cancellable = true)
    private void preventDeathForInvulnerableEntities(CallbackInfoReturnable<Boolean> cir) {
        LivingEntity entity = (LivingEntity) (Object) this;
        String entityType = entity.getType().toString();
        
        // Check if this entity should be invulnerable
        // 检查这个生物是否应该无敌
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                // Always return false - entity is never dead or dying
                // 总是返回false - 生物永远不会死亡或濒死
                cir.setReturnValue(false);
                return;
            }
        }
    }
    
    /**
     * Override tick method to continuously reset hurt values and maintain health
     * 重写tick方法以持续重置受伤值并维持生命值
     * 
     * 模仿HurtDummyEntity.tick()和baseTick()方法的行为
     */
    @Inject(method = "tick", at = @At("HEAD"))
    private void resetHurtValuesForInvulnerableEntities(CallbackInfoReturnable<Void> cir) {
        LivingEntity entity = (LivingEntity) (Object) this;
        String entityType = entity.getType().toString();
        
        // Check if this entity should be invulnerable
        // 检查这个生物是否应该无敌
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                // Continuously reset hurt animation values (模仿HurtDummyEntity的行为)
                // 持续重置受伤动画值
                entity.hurtTime = 0;
                entity.hurtDuration = 0;
                
                // Ensure health stays at maximum (确保生命值保持最大值)
                // 这确保即使有任何伤害绕过了hurt方法，生物也会立即恢复满血
                if (entity.getHealth() < entity.getMaxHealth()) {
                    entity.setHealth(entity.getMaxHealth());
                }
                return;
            }
        }
    }
    
    /**
     * Override baseTick method to reset hurt values like HurtDummyEntity
     * 重写baseTick方法以像HurtDummyEntity一样重置受伤值
     * 
     * 这个方法模仿HurtDummyEntity.baseTick()的行为
     */
    @Inject(method = "baseTick", at = @At("TAIL"))
    private void resetHurtValuesInBaseTick(CallbackInfoReturnable<Void> cir) {
        LivingEntity entity = (LivingEntity) (Object) this;
        String entityType = entity.getType().toString();
        
        // Check if this entity should be invulnerable
        // 检查这个生物是否应该无敌
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                // Reset hurt animation values after baseTick (在baseTick之后重置受伤动画值)
                // 这确保即使baseTick设置了这些值，我们也会立即重置它们
                entity.hurtTime = 0;
                entity.hurtDuration = 0;
                return;
            }
        }
    }
}
