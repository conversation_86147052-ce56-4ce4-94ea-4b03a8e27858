package com.github.b4ndithelps.beta1.mixin;

import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.LivingEntity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * Mixin to make specific entities invulnerable to all damage
 * 为指定的生物添加无敌效果的Mixin类
 * 
 * 模仿HurtDummyEntity的无敌机制，为以下生物提供完全无敌效果：
 * - trbeyond:dryad (树精)
 * - trbeyond:kaijin (魁人)
 * - trbeyond:gozul (哥泽尔)
 * - trbeyond:guild_lady (公会女士)
 * - trbeyond:koby (科比)
 * - trbeyond:kurobe (黑部)
 */
@Mixin(LivingEntity.class)
public class InvulnerableEntitiesMixin {
    
    /**
     * List of entity types that should be invulnerable
     * 应该无敌的生物类型列表
     */
    private static final String[] INVULNERABLE_ENTITIES = {
        "trbeyond:dryad",
        "trbeyond:kaijin", 
        "trbeyond:gozul",
        "trbeyond:guild_lady",
        "trbeyond:koby",
        "trbeyond:kurobe"
    };
    
    /**
     * Intercept the hurt method to prevent damage for specified entities
     * 拦截hurt方法，为指定生物阻止伤害
     * 
     * 这个方法模仿了HurtDummyEntity.hurt()的行为：
     * 1. 检查是否为指定的无敌生物
     * 2. 重置受伤动画值
     * 3. 取消伤害并返回false
     */
    @Inject(method = "hurt", at = @At("HEAD"), cancellable = true)
    private void preventDamageForInvulnerableEntities(DamageSource damageSource, float amount, CallbackInfoReturnable<Boolean> cir) {
        LivingEntity entity = (LivingEntity) (Object) this;
        String entityType = entity.getType().toString();
        
        // Check if this entity should be invulnerable
        // 检查这个生物是否应该无敌
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                // Reset hurt animation values to prevent visual damage effects
                // 重置受伤动画值以防止视觉伤害效果 (模仿HurtDummyEntity的行为)
                entity.hurtTime = 0;
                entity.hurtDuration = 0;
                
                // Cancel the damage and return false (no damage taken)
                // 取消伤害并返回false（未受到伤害）
                cir.setReturnValue(false);
                return;
            }
        }
    }
    

    
    /**
     * Prevent death for specified entities
     * 为指定生物防止死亡
     *
     * 模仿HurtDummyEntity.isDeadOrDying()方法
     */
    @Inject(method = "isDeadOrDying", at = @At("HEAD"), cancellable = true)
    private void preventDeathForInvulnerableEntities(CallbackInfoReturnable<Boolean> cir) {
        LivingEntity entity = (LivingEntity) (Object) this;
        String entityType = entity.getType().toString();

        // Check if this entity should be invulnerable
        // 检查这个生物是否应该无敌
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                // Always return false - entity is never dead or dying
                // 总是返回false - 生物永远不会死亡或濒死
                cir.setReturnValue(false);
                return;
            }
        }
    }


    
    /**
     * Override tick method to continuously reset hurt values and maintain health
     * 重写tick方法以持续重置受伤值并维持生命值
     *
     * 模仿HurtDummyEntity.tick()和baseTick()方法的行为
     */
    @Inject(method = "tick", at = @At("HEAD"))
    private void resetHurtValuesForInvulnerableEntities(CallbackInfo ci) {
        LivingEntity entity = (LivingEntity) (Object) this;
        String entityType = entity.getType().toString();

        // Check if this entity should be invulnerable
        // 检查这个生物是否应该无敌
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                // Continuously reset hurt animation values (模仿HurtDummyEntity的行为)
                // 持续重置受伤动画值
                entity.hurtTime = 0;
                entity.hurtDuration = 0;

                // Set invulnerable flag like HurtDummyEntity constructor
                // 像HurtDummyEntity构造函数一样设置无敌标志
                entity.setInvulnerable(true);

                // Ensure health stays at maximum (确保生命值保持最大值)
                // 这确保即使有任何伤害绕过了hurt方法，生物也会立即恢复满血
                // 这是关键的"骗过死亡"机制 - 即使生命值被设为0，也会立即恢复
                if (entity.getHealth() <= 0.0F || entity.getHealth() < entity.getMaxHealth()) {
                    entity.setHealth(entity.getMaxHealth());
                }

                // Force the entity to be alive even if it should be dead
                // 强制生物保持存活状态，即使它应该死亡
                if (entity.isDeadOrDying()) {
                    entity.setHealth(entity.getMaxHealth());
                }

                return;
            }
        }
    }
    
    /**
     * Override baseTick method to reset hurt values like HurtDummyEntity
     * 重写baseTick方法以像HurtDummyEntity一样重置受伤值
     *
     * 这个方法模仿HurtDummyEntity.baseTick()的行为
     */
    @Inject(method = "baseTick", at = @At("TAIL"))
    private void resetHurtValuesInBaseTick(CallbackInfo ci) {
        LivingEntity entity = (LivingEntity) (Object) this;
        String entityType = entity.getType().toString();

        // Check if this entity should be invulnerable
        // 检查这个生物是否应该无敌
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                // Reset hurt animation values after baseTick (在baseTick之后重置受伤动画值)
                // 这确保即使baseTick设置了这些值，我们也会立即重置它们
                entity.hurtTime = 0;
                entity.hurtDuration = 0;

                // Additional safety: ensure health is always at maximum
                // 额外安全措施：确保生命值始终为最大值
                if (entity.getHealth() <= 0.0F) {
                    entity.setHealth(entity.getMaxHealth());
                }

                return;
            }
        }
    }

    /**
     * Prevent actual death by intercepting die method
     * 通过拦截die方法防止真正的死亡
     *
     * 这是最关键的"骗过死亡"机制
     */
    @Inject(method = "die", at = @At("HEAD"), cancellable = true)
    private void preventActualDeath(DamageSource damageSource, CallbackInfo ci) {
        LivingEntity entity = (LivingEntity) (Object) this;
        String entityType = entity.getType().toString();

        // Check if this entity should be invulnerable
        // 检查这个生物是否应该无敌
        for (String invulnerableType : INVULNERABLE_ENTITIES) {
            if (entityType.equals(invulnerableType)) {
                // Cancel the death completely and restore full health
                // 完全取消死亡并恢复满血
                entity.setHealth(entity.getMaxHealth());
                entity.hurtTime = 0;
                entity.hurtDuration = 0;
                entity.setInvulnerable(true);

                // Cancel the die method execution
                // 取消die方法的执行
                ci.cancel();
                return;
            }
        }
    }


}
