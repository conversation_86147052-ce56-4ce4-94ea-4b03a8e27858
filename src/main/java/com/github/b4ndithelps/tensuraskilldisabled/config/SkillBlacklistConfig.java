package com.github.b4ndithelps.tensuraskilldisabled.config;

import com.github.b4ndithelps.tensuraskilldisabled.tensuraskilldisabled;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.fml.loading.FMLPaths;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.*;

/**
 * 技能黑名单配置管理器
 * 管理被禁用的技能列表
 */
public class SkillBlacklistConfig {
    private static final Logger LOGGER = LoggerFactory.getLogger(SkillBlacklistConfig.class);
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();
    private static final String CONFIG_FILE_NAME = "skill_blacklist.json";
    
    private static SkillBlacklistConfig INSTANCE;
    private final File configFile;
    private Set<String> disabledSkills;
    private ConfigData configData;
    
    private SkillBlacklistConfig() {
        // 使用全局配置目录，而不是特定世界的配置
        File globalConfigDir = FMLPaths.CONFIGDIR.get().toFile();
        this.configFile = new File(globalConfigDir, CONFIG_FILE_NAME);
        this.disabledSkills = new HashSet<>();
        this.configData = new ConfigData();
        loadConfig();
    }
    
    public static SkillBlacklistConfig getInstance() {
        if (INSTANCE == null) {
            INSTANCE = new SkillBlacklistConfig();
        }
        return INSTANCE;
    }
    
    /**
     * 加载配置文件
     */
    public void loadConfig() {
        if (!configFile.exists()) {
            createDefaultConfig();
            return;
        }
        
        try (FileReader reader = new FileReader(configFile)) {
            Type type = new TypeToken<ConfigData>(){}.getType();
            configData = GSON.fromJson(reader, type);
            
            if (configData == null) {
                configData = new ConfigData();
            }
            
            // 更新内存中的禁用技能集合
            disabledSkills.clear();
            disabledSkills.addAll(configData.disabledSkills);
            
            LOGGER.info("Loaded skill blacklist config with {} disabled skills", disabledSkills.size());
            
        } catch (IOException e) {
            LOGGER.error("Failed to load skill blacklist config", e);
            createDefaultConfig();
        }
    }
    
    /**
     * 保存配置文件
     */
    public void saveConfig() {
        try {
            // 确保配置目录存在
            configFile.getParentFile().mkdirs();
            
            // 更新配置数据
            configData.disabledSkills.clear();
            configData.disabledSkills.addAll(disabledSkills);
            
            try (FileWriter writer = new FileWriter(configFile)) {
                GSON.toJson(configData, writer);
                LOGGER.info("Saved skill blacklist config");
            }
            
        } catch (IOException e) {
            LOGGER.error("Failed to save skill blacklist config", e);
        }
    }
    
    /**
     * 创建默认配置文件
     */
    private void createDefaultConfig() {
        configData = new ConfigData();

        // 默认不添加任何禁用技能，让用户自己配置
        // configData.disabledSkills 保持为空列表

        // 更新内存中的集合
        disabledSkills.clear();
        disabledSkills.addAll(configData.disabledSkills);

        saveConfig();
        LOGGER.info("Created default skill blacklist config (empty)");
    }
    
    /**
     * 检查技能是否被禁用
     */
    public boolean isSkillDisabled(ResourceLocation skillId) {
        return disabledSkills.contains(skillId.toString());
    }
    
    /**
     * 检查技能是否被禁用（字符串形式）
     */
    public boolean isSkillDisabled(String skillId) {
        return disabledSkills.contains(skillId);
    }
    
    /**
     * 添加技能到黑名单
     */
    public void addDisabledSkill(String skillId) {
        if (disabledSkills.add(skillId)) {
            saveConfig();
            LOGGER.info("Added skill to blacklist: {}", skillId);
        }
    }
    
    /**
     * 从黑名单移除技能
     */
    public void removeDisabledSkill(String skillId) {
        if (disabledSkills.remove(skillId)) {
            saveConfig();
            LOGGER.info("Removed skill from blacklist: {}", skillId);
        }
    }
    
    /**
     * 获取所有禁用的技能
     */
    public Set<String> getDisabledSkills() {
        return new HashSet<>(disabledSkills);
    }
    
    /**
     * 重新加载配置
     */
    public void reload() {
        loadConfig();
    }
    
    /**
     * 配置数据类
     */
    public static class ConfigData {
        public String version = "1.0";
        public String description = "Tensura Skill Blacklist Configuration - Global settings for all worlds";
        public String note = "This configuration applies to all worlds and servers";
        public List<String> disabledSkills = new ArrayList<>();

        public ConfigData() {
            // 默认构造函数
        }
    }
}
