package com.github.b4ndithelps.tensuraskilldisabled.handler;

import com.github.b4ndithelps.tensuraskilldisabled.tensuraskilldisabled;
import com.github.manasmods.tensura.event.UpdateEPEvent;
import net.minecraftforge.eventbus.api.EventPriority;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

/**
 * 修复Tensura模组的栈溢出问题
 * 通过检测和阻止无限递归来防止StackOverflowError
 */
@Mod.EventBusSubscriber(modid = tensuraskilldisabled.MODID, bus = Mod.EventBusSubscriber.Bus.FORGE)
public class StackOverflowFixHandler {

    // 使用ThreadLocal来跟踪当前线程的递归深度
    private static final ThreadLocal<Integer> RECURSION_DEPTH = ThreadLocal.withInitial(() -> 0);

    // 最大允许的递归深度
    private static final int MAX_RECURSION_DEPTH = 3;
    
    /**
     * 监听UpdateEPEvent，防止无限递归
     */
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public static void onUpdateEP(UpdateEPEvent event) {
        // 获取当前递归深度
        int currentDepth = RECURSION_DEPTH.get();

        // 如果递归深度超过限制，取消事件
        if (currentDepth >= MAX_RECURSION_DEPTH) {
            event.setCanceled(true);
            return;
        }

        // 增加递归深度
        RECURSION_DEPTH.set(currentDepth + 1);

        try {
            // 事件正常处理
            // 其他处理器会继续处理这个事件
        } finally {
            // 恢复递归深度
            RECURSION_DEPTH.set(currentDepth);
        }
    }
}
