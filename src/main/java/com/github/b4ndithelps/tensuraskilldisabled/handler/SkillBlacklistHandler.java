package com.github.b4ndithelps.tensuraskilldisabled.handler;

import com.github.b4ndithelps.tensuraskilldisabled.config.SkillBlacklistConfig;
import com.github.b4ndithelps.tensuraskilldisabled.tensuraskilldisabled;
import com.github.manasmods.manascore.api.skills.event.UnlockSkillEvent;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.eventbus.api.EventPriority;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

/**
 * 技能黑名单事件处理器
 * 监听技能解锁事件并阻止黑名单中的技能被获得
 */
@Mod.EventBusSubscriber(modid = tensuraskilldisabled.MODID, bus = Mod.EventBusSubscriber.Bus.FORGE)
public class SkillBlacklistHandler {
    
    /**
     * 监听技能解锁事件
     * 使用 HIGHEST 优先级确保我们能在其他模组之前处理
     */
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public static void onSkillUnlock(UnlockSkillEvent event) {
        // 获取技能ID
        ResourceLocation skillId = event.getSkillInstance().getSkill().getRegistryName();
        if (skillId == null) {
            return;
        }
        
        // 检查技能是否在黑名单中
        SkillBlacklistConfig config = SkillBlacklistConfig.getInstance();
        if (config.isSkillDisabled(skillId)) {
            // 静默取消事件，阻止技能获得
            event.setCanceled(true);
            // 不发送任何消息给玩家
            // 不记录任何日志
        }
    }
}
