package com.github.b4ndithelps.tensuraskilldisabled.handler;

import com.github.b4ndithelps.tensuraskilldisabled.config.SkillBlacklistConfig;
import com.github.b4ndithelps.tensuraskilldisabled.tensuraskilldisabled;
import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

import java.util.ArrayList;
import java.util.List;

/**
 * 玩家登录时技能检查处理器
 * 检查并移除玩家身上的黑名单技能
 */
@Mod.EventBusSubscriber(modid = tensuraskilldisabled.MODID, bus = Mod.EventBusSubscriber.Bus.FORGE)
public class PlayerLoginSkillCheckHandler {
    
    /**
     * 监听玩家登录事件
     */
    @SubscribeEvent
    public static void onPlayerLogin(PlayerEvent.PlayerLoggedInEvent event) {
        if (!(event.getEntity() instanceof ServerPlayer)) {
            return;
        }
        
        ServerPlayer player = (ServerPlayer) event.getEntity();
        
        // 延迟执行技能检查，确保玩家完全加载
        player.getServer().execute(() -> {
            checkAndRemoveBlacklistedSkills(player);
        });
    }
    
    /**
     * 检查并移除玩家的黑名单技能
     */
    private static void checkAndRemoveBlacklistedSkills(ServerPlayer player) {
        try {
            SkillBlacklistConfig config = SkillBlacklistConfig.getInstance();
            
            // 如果没有禁用技能，直接返回
            if (config.getDisabledSkills().isEmpty()) {
                return;
            }
            
            // 获取玩家的技能存储
            SkillStorage skillStorage = SkillAPI.getSkillsFrom(player);
            if (skillStorage == null) {
                return;
            }
            
            // 获取玩家已学习的技能列表
            List<ManasSkillInstance> learnedSkills = new ArrayList<>(skillStorage.getLearnedSkills());
            List<ManasSkill> skillsToRemove = new ArrayList<>();
            
            // 检查每个已学习的技能
            for (ManasSkillInstance skillInstance : learnedSkills) {
                ManasSkill skill = skillInstance.getSkill();
                ResourceLocation skillId = skill.getRegistryName();
                
                if (skillId != null && config.isSkillDisabled(skillId)) {
                    skillsToRemove.add(skill);
                }
            }
            
            // 移除黑名单中的技能
            if (!skillsToRemove.isEmpty()) {
                for (ManasSkill skill : skillsToRemove) {
                    // 使用Tensura的API移除技能
                    skillStorage.forgetSkill(skill);
                }
                
                // 同步技能数据到客户端
                skillStorage.syncAll();
                
                // 可选：记录移除的技能数量（用于调试）
                // 由于我们要求静默模式，这里不发送消息给玩家
                // LOGGER.debug("Removed {} blacklisted skills from player {}", 
                //     skillsToRemove.size(), player.getName().getString());
            }
            
        } catch (Exception e) {
            // 静默处理异常，避免影响玩家登录
            // 可以选择记录到日志，但不影响游戏体验
        }
    }
    
    /**
     * 监听玩家重生事件（可选）
     * 在玩家重生时也检查技能
     */
    @SubscribeEvent
    public static void onPlayerRespawn(PlayerEvent.PlayerRespawnEvent event) {
        if (!(event.getEntity() instanceof ServerPlayer)) {
            return;
        }
        
        ServerPlayer player = (ServerPlayer) event.getEntity();
        
        // 延迟执行，确保重生过程完成
        player.getServer().execute(() -> {
            checkAndRemoveBlacklistedSkills(player);
        });
    }
    
    /**
     * 监听玩家切换维度事件（可选）
     * 在玩家切换维度时也检查技能
     */
    @SubscribeEvent
    public static void onPlayerChangeDimension(PlayerEvent.PlayerChangedDimensionEvent event) {
        if (!(event.getEntity() instanceof ServerPlayer)) {
            return;
        }
        
        ServerPlayer player = (ServerPlayer) event.getEntity();
        
        // 延迟执行，确保维度切换完成
        player.getServer().execute(() -> {
            checkAndRemoveBlacklistedSkills(player);
        });
    }
}
