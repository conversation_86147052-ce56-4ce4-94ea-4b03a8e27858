package com.github.b4ndithelps.tensuraskilldisabled.command;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.suggestion.SuggestionProvider;
import com.mojang.brigadier.suggestion.Suggestions;
import com.mojang.brigadier.suggestion.SuggestionsBuilder;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.registries.ForgeRegistry;

import java.util.concurrent.CompletableFuture;

/**
 * 技能ID建议提供器
 * 为命令提供所有可用技能的Tab补全
 */
public class SkillIdSuggestionProvider implements SuggestionProvider<CommandSourceStack> {
    
    public static final SkillIdSuggestionProvider INSTANCE = new SkillIdSuggestionProvider();
    
    @Override
    public CompletableFuture<Suggestions> getSuggestions(CommandContext<CommandSourceStack> context, SuggestionsBuilder builder) {
        try {
            // 获取技能注册表
            ForgeRegistry<ManasSkill> skillRegistry = (ForgeRegistry<ManasSkill>) SkillAPI.getSkillRegistry();
            
            if (skillRegistry != null) {
                // 遍历所有注册的技能
                for (ManasSkill skill : skillRegistry.getValues()) {
                    ResourceLocation skillId = skill.getRegistryName();
                    if (skillId != null) {
                        String skillIdString = skillId.toString();
                        // 添加到建议列表
                        if (skillIdString.toLowerCase().contains(builder.getRemaining().toLowerCase())) {
                            builder.suggest(skillIdString);
                        }
                    }
                }
            }
            
            // 添加一些常见的技能ID作为备选
            addCommonSkillSuggestions(builder);
            
        } catch (Exception e) {
            // 如果获取注册表失败，提供一些常见的技能ID
            addCommonSkillSuggestions(builder);
        }
        
        return builder.buildFuture();
    }
    
    /**
     * 添加常见技能建议
     */
    private void addCommonSkillSuggestions(SuggestionsBuilder builder) {
        String input = builder.getRemaining().toLowerCase();
        
        // Tensura模组的常见技能 - 仅用于Tab补全建议，不代表默认禁用
        String[] commonSkills = {
            "tensura:gluttony",
            "tensura:predator",
            "tensura:degenerate",
            "tensura:sage",
            "tensura:great_sage",
            "tensura:raphael",
            "tensura:uriel",
            "tensura:gabriel",
            "tensura:michael",
            "tensura:rimuru_tempest",
            "tensura:veldora_tempest",
            "tensura:storm_dragon",
            "tensura:magic_sense",
            "tensura:danger_sense",
            "tensura:thermal_fluctuation_resistance",
            "tensura:physical_attack_nullification",
            "tensura:natural_effects_nullification",
            "tensura:spiritual_attack_resistance",
            "tensura:holy_attack_resistance",
            "tensura:pain_nullification",
            "tensura:paralysis_resistance",
            "tensura:poison_resistance"
        };
        
        for (String skill : commonSkills) {
            if (skill.toLowerCase().contains(input)) {
                builder.suggest(skill);
            }
        }
    }
}
