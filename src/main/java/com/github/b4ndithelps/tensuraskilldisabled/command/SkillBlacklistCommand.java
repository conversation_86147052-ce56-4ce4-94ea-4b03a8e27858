package com.github.b4ndithelps.tensuraskilldisabled.command;

import com.github.b4ndithelps.tensuraskilldisabled.config.SkillBlacklistConfig;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import com.mojang.brigadier.suggestion.Suggestions;
import com.mojang.brigadier.suggestion.SuggestionsBuilder;
import net.minecraft.ChatFormatting;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.event.RegisterCommandsEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import com.github.b4ndithelps.tensuraskilldisabled.tensuraskilldisabled;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 技能黑名单管理命令
 * 提供添加、移除、列出禁用技能的命令
 */
@Mod.EventBusSubscriber(modid = tensuraskilldisabled.MODID, bus = Mod.EventBusSubscriber.Bus.FORGE)
public class SkillBlacklistCommand {
    
    @SubscribeEvent
    public static void onRegisterCommands(RegisterCommandsEvent event) {
        CommandDispatcher<CommandSourceStack> dispatcher = event.getDispatcher();
        
        dispatcher.register(
            Commands.literal("skillblacklist")
                .requires(source -> source.hasPermission(2)) // 需要OP权限
                .then(Commands.literal("add")
                    .then(Commands.argument("skill_id", StringArgumentType.greedyString())
                        .suggests(SkillIdSuggestionProvider.INSTANCE)
                        .executes(SkillBlacklistCommand::addSkill)))
                .then(Commands.literal("remove")
                    .then(Commands.argument("skill_id", StringArgumentType.greedyString())
                        .suggests(SkillIdSuggestionProvider.INSTANCE)
                        .executes(SkillBlacklistCommand::removeSkill)))
                .then(Commands.literal("addmod")
                    .then(Commands.argument("mod_id", StringArgumentType.string())
                        .suggests(SkillBlacklistCommand::suggestModIds)
                        .executes(SkillBlacklistCommand::addModSkills)))
                .then(Commands.literal("removemod")
                    .then(Commands.argument("mod_id", StringArgumentType.string())
                        .suggests(SkillBlacklistCommand::suggestModIds)
                        .executes(SkillBlacklistCommand::removeModSkills)))
                .then(Commands.literal("list")
                    .executes(SkillBlacklistCommand::listSkills))
                .then(Commands.literal("listmods")
                    .executes(SkillBlacklistCommand::listModsWithSkills))
                .then(Commands.literal("reload")
                    .executes(SkillBlacklistCommand::reloadConfig))
                .executes(SkillBlacklistCommand::showHelp)
        );
    }
    
    /**
     * 添加技能到黑名单
     */
    private static int addSkill(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        String skillId = StringArgumentType.getString(context, "skill_id");
        
        // 验证技能ID格式
        if (!isValidSkillId(skillId)) {
            context.getSource().sendFailure(
                Component.literal("§c无效的技能ID格式。请使用 'namespace:path' 格式，例如: tensura:gluttony")
            );
            return 0;
        }
        
        SkillBlacklistConfig config = SkillBlacklistConfig.getInstance();
        
        if (config.isSkillDisabled(skillId)) {
            context.getSource().sendFailure(
                Component.literal("§e技能 '" + skillId + "' 已经在黑名单中")
            );
            return 0;
        }

        config.addDisabledSkill(skillId);
        context.getSource().sendSuccess(
            Component.literal("§a已将技能 '" + skillId + "' 添加到黑名单"),
            true
        );
        
        return 1;
    }
    
    /**
     * 从黑名单移除技能
     */
    private static int removeSkill(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        String skillId = StringArgumentType.getString(context, "skill_id");
        
        SkillBlacklistConfig config = SkillBlacklistConfig.getInstance();
        
        if (!config.isSkillDisabled(skillId)) {
            context.getSource().sendFailure(
                Component.literal("§e技能 '" + skillId + "' 不在黑名单中")
            );
            return 0;
        }

        config.removeDisabledSkill(skillId);
        context.getSource().sendSuccess(
            Component.literal("§a已从黑名单中移除技能 '" + skillId + "'"),
            true
        );
        
        return 1;
    }
    
    /**
     * 列出所有禁用的技能
     */
    private static int listSkills(CommandContext<CommandSourceStack> context) {
        SkillBlacklistConfig config = SkillBlacklistConfig.getInstance();
        var disabledSkills = config.getDisabledSkills();
        
        if (disabledSkills.isEmpty()) {
            context.getSource().sendSuccess(
                Component.literal("§e当前没有技能被列入黑名单"),
                false
            );
            return 1;
        }

        context.getSource().sendSuccess(
            Component.literal("§b黑名单技能 (" + disabledSkills.size() + "个):"),
            false
        );

        for (String skillId : disabledSkills) {
            context.getSource().sendSuccess(
                Component.literal("  §7- §f" + skillId),
                false
            );
        }
        
        return 1;
    }
    
    /**
     * 重新加载配置
     */
    private static int reloadConfig(CommandContext<CommandSourceStack> context) {
        SkillBlacklistConfig.getInstance().reload();
        context.getSource().sendSuccess(
            Component.literal("§a技能黑名单配置已重新加载"),
            true
        );
        return 1;
    }
    
    /**
     * 显示帮助信息
     */
    private static int showHelp(CommandContext<CommandSourceStack> context) {
        context.getSource().sendSuccess(
            Component.literal("§6技能黑名单命令:"),
            false
        );
        context.getSource().sendSuccess(
            Component.literal("§e单个技能管理:"),
            false
        );
        context.getSource().sendSuccess(
            Component.literal("  §f/skillblacklist add <技能ID> §7- 添加技能到黑名单"),
            false
        );
        context.getSource().sendSuccess(
            Component.literal("  §f/skillblacklist remove <技能ID> §7- 从黑名单移除技能"),
            false
        );
        context.getSource().sendSuccess(
            Component.literal("§e批量模组管理:"),
            false
        );
        context.getSource().sendSuccess(
            Component.literal("  §f/skillblacklist addmod <模组ID> §7- 禁用整个模组的所有技能"),
            false
        );
        context.getSource().sendSuccess(
            Component.literal("  §f/skillblacklist removemod <模组ID> §7- 解禁整个模组的所有技能"),
            false
        );
        context.getSource().sendSuccess(
            Component.literal("§e查看和管理:"),
            false
        );
        context.getSource().sendSuccess(
            Component.literal("  §f/skillblacklist list §7- 列出所有黑名单技能"),
            false
        );
        context.getSource().sendSuccess(
            Component.literal("  §f/skillblacklist listmods §7- 列出所有有技能的模组"),
            false
        );
        context.getSource().sendSuccess(
            Component.literal("  §f/skillblacklist reload §7- 重新加载配置"),
            false
        );
        context.getSource().sendSuccess(
            Component.literal("§e示例:"),
            false
        );
        context.getSource().sendSuccess(
            Component.literal("  §f/skillblacklist add tensura:gluttony §7- 禁用单个技能"),
            false
        );
        context.getSource().sendSuccess(
            Component.literal("  §f/skillblacklist addmod tensura §7- 禁用整个Tensura模组的技能"),
            false
        );

        return 1;
    }
    
    /**
     * 按模组ID添加所有技能到黑名单
     */
    private static int addModSkills(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        String modId = StringArgumentType.getString(context, "mod_id");

        SkillBlacklistConfig config = SkillBlacklistConfig.getInstance();

        // 获取技能注册表
        try {
            var skillRegistry = SkillAPI.getSkillRegistry();
            List<String> addedSkills = new ArrayList<>();

            // 遍历所有技能，找到属于指定模组的技能
            for (var skill : skillRegistry.getValues()) {
                ResourceLocation skillId = skill.getRegistryName();
                if (skillId != null && skillId.getNamespace().equals(modId)) {
                    String skillIdString = skillId.toString();
                    if (!config.isSkillDisabled(skillIdString)) {
                        config.addDisabledSkill(skillIdString);
                        addedSkills.add(skillIdString);
                    }
                }
            }

            if (addedSkills.isEmpty()) {
                context.getSource().sendFailure(
                    Component.literal("§e模组 '" + modId + "' 没有找到技能，或所有技能已在黑名单中")
                );
                return 0;
            }

            context.getSource().sendSuccess(
                Component.literal("§a已将模组 '" + modId + "' 的 " + addedSkills.size() + " 个技能添加到黑名单"),
                true
            );

            // 显示添加的技能列表
            for (String skillId : addedSkills) {
                context.getSource().sendSuccess(
                    Component.literal("  §7+ §f" + skillId),
                    false
                );
            }

            return 1;

        } catch (Exception e) {
            context.getSource().sendFailure(
                Component.literal("§c获取技能注册表时出错")
            );
            return 0;
        }
    }

    /**
     * 按模组ID从黑名单移除所有技能
     */
    private static int removeModSkills(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        String modId = StringArgumentType.getString(context, "mod_id");

        SkillBlacklistConfig config = SkillBlacklistConfig.getInstance();
        List<String> removedSkills = new ArrayList<>();

        // 遍历当前黑名单，找到属于指定模组的技能
        for (String skillId : new ArrayList<>(config.getDisabledSkills())) {
            try {
                ResourceLocation resourceLocation = new ResourceLocation(skillId);
                if (resourceLocation.getNamespace().equals(modId)) {
                    config.removeDisabledSkill(skillId);
                    removedSkills.add(skillId);
                }
            } catch (Exception e) {
                // 忽略无效的技能ID
            }
        }

        if (removedSkills.isEmpty()) {
            context.getSource().sendFailure(
                Component.literal("§e模组 '" + modId + "' 的技能不在黑名单中")
            );
            return 0;
        }

        context.getSource().sendSuccess(
            Component.literal("§a已从黑名单中移除模组 '" + modId + "' 的 " + removedSkills.size() + " 个技能"),
            true
        );

        // 显示移除的技能列表
        for (String skillId : removedSkills) {
            context.getSource().sendSuccess(
                Component.literal("  §7- §f" + skillId),
                false
            );
        }

        return 1;
    }

    /**
     * 列出所有有技能的模组
     */
    private static int listModsWithSkills(CommandContext<CommandSourceStack> context) {
        try {
            var skillRegistry = SkillAPI.getSkillRegistry();
            Map<String, Integer> modSkillCount = new HashMap<>();

            // 统计每个模组的技能数量
            for (var skill : skillRegistry.getValues()) {
                ResourceLocation skillId = skill.getRegistryName();
                if (skillId != null) {
                    String modId = skillId.getNamespace();
                    modSkillCount.put(modId, modSkillCount.getOrDefault(modId, 0) + 1);
                }
            }

            if (modSkillCount.isEmpty()) {
                context.getSource().sendSuccess(
                    Component.literal("§e没有找到任何模组的技能"),
                    false
                );
                return 1;
            }

            context.getSource().sendSuccess(
                Component.literal("§b有技能的模组列表:"),
                false
            );

            // 按模组ID排序显示
            modSkillCount.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    String modId = entry.getKey();
                    int skillCount = entry.getValue();
                    context.getSource().sendSuccess(
                        Component.literal("  §f" + modId + " §7(" + skillCount + "个技能)"),
                        false
                    );
                });

            return 1;

        } catch (Exception e) {
            context.getSource().sendFailure(
                Component.literal("§c获取技能注册表时出错")
            );
            return 0;
        }
    }

    /**
     * 为模组ID提供建议
     */
    private static CompletableFuture<Suggestions> suggestModIds(CommandContext<CommandSourceStack> context, SuggestionsBuilder builder) {
        try {
            var skillRegistry = SkillAPI.getSkillRegistry();
            Set<String> modIds = new HashSet<>();

            // 收集所有有技能的模组ID
            for (var skill : skillRegistry.getValues()) {
                ResourceLocation skillId = skill.getRegistryName();
                if (skillId != null) {
                    modIds.add(skillId.getNamespace());
                }
            }

            // 添加到建议列表
            String input = builder.getRemaining().toLowerCase();
            for (String modId : modIds) {
                if (modId.toLowerCase().contains(input)) {
                    builder.suggest(modId);
                }
            }

        } catch (Exception e) {
            // 如果出错，提供一些常见的模组ID
            String[] commonMods = {"tensura", "minecraft", "forge"};
            String input = builder.getRemaining().toLowerCase();
            for (String modId : commonMods) {
                if (modId.toLowerCase().contains(input)) {
                    builder.suggest(modId);
                }
            }
        }

        return builder.buildFuture();
    }

    /**
     * 验证技能ID格式
     */
    private static boolean isValidSkillId(String skillId) {
        try {
            ResourceLocation.tryParse(skillId);
            return skillId.contains(":");
        } catch (Exception e) {
            return false;
        }
    }
}
