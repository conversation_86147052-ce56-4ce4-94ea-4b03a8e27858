package com.github.b4ndithelps.tensuraskilldisabled;

import com.github.b4ndithelps.tensuraskilldisabled.config.SkillBlacklistConfig;
import com.mojang.logging.LogUtils;
import net.minecraft.client.Minecraft;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.event.server.ServerStartingEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import org.slf4j.Logger;

// The value here should match an entry in the META-INF/mods.toml file
@Mod(tensuraskilldisabled.MODID)
public class tensuraskilldisabled {

    // Define mod id in a common place for everything to reference
    public static final String MODID = "tensuraskilldisabled";
    // Directly reference a slf4j logger
    private static final Logger LOGGER = LogUtils.getLogger();

    public tensuraskilldisabled() {
        IEventBus modEventBus = FMLJavaModLoadingContext.get().getModEventBus();
        modEventBus.addListener(this::commonSetup);

        // Register the skills, items, etc. with the mod bus
        // AllSkills.register(modEventBus); // Removed - no longer needed for skill disabling system

        MinecraftForge.EVENT_BUS.register(this);
        LOGGER.info("Tensura Skill Disabled Mod has been loaded!");
    }

    private void commonSetup(final FMLCommonSetupEvent event) {
        // 初始化技能黑名单配置
        event.enqueueWork(() -> {
            SkillBlacklistConfig.getInstance();
            LOGGER.info("Skill blacklist system initialized");
        });
    }

    // You can use SubscribeEvent and let the Event Bus discover methods to call
    @SubscribeEvent
    public void onServerStarting(ServerStartingEvent event) {

    }

    // You can use EventBusSubscriber to automatically register all static methods in the class annotated with @SubscribeEvent
    @Mod.EventBusSubscriber(modid = MODID, bus = Mod.EventBusSubscriber.Bus.MOD, value = Dist.CLIENT)
    public static class ClientModEvents {

        @SubscribeEvent
        public static void onClientSetup(FMLClientSetupEvent event) {
            // Some client setup code
            LOGGER.info("HELLO FROM CLIENT SETUP");
            LOGGER.info("MINECRAFT NAME >> {}", Minecraft.getInstance().getUser().getName());
        }
    }
}
