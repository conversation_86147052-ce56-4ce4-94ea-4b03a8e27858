# 无敌生物模组 (Invulnerable Entities Mod)

## 概述
这个模组为指定的6个生物添加了完全无敌效果，模仿了`HurtDummyEntity`的无敌机制。

## 无敌生物列表
以下生物现在具有完全无敌效果：

1. **trbeyond:dryad** (树精)
2. **trbeyond:kaijin** (魁人)
3. **trbeyond:gozul** (哥泽尔)
4. **trbeyond:guild_lady** (公会女士)
5. **trbeyond:koby** (科比)
6. **trbeyond:kurobe** (黑部)

## 无敌效果特性

### 完全伤害免疫
- 所有类型的伤害都会被完全阻止
- `hurt`方法直接返回`false`，表示未受到伤害
- 生命值始终保持在最大值
- 即使生命值被强制设为0，也会立即恢复满血

### 视觉效果保护
- 受伤动画被完全禁用
- `hurtTime`和`hurtDuration`始终为0
- 不会显示任何受伤的红色闪烁效果
- 在每个tick和baseTick中持续重置受伤状态

### 死亡保护（骗过死亡机制）
- `isDeadOrDying`方法始终返回`false`
- `die`方法被完全拦截和取消
- 生物永远不会进入死亡状态
- 即使生命值降到0以下也不会死亡
- 强制维持存活状态，完全"骗过"游戏的死亡检测

### 无敌状态维护
- 在每个tick中强制设置`setInvulnerable(true)`
- 持续监控和恢复生命值
- 如果检测到生物处于死亡状态，立即恢复满血
- 完全模仿`HurtDummyEntity`的构造函数行为

### 生存保障
- 防止任何形式的实体移除
- 确保生物在任何情况下都能存活
- 提供多层保护机制，确保无敌效果的可靠性

## 技术实现

### Mixin技术
使用SpongePowered Mixin框架来修改`LivingEntity`类的行为：

```java
@Mixin(LivingEntity.class)
public class InvulnerableEntitiesMixin {
    // 拦截hurt方法阻止伤害
    @Inject(method = "hurt", at = @At("HEAD"), cancellable = true)
    
    // 重写无敌检查方法
    @Inject(method = "isInvulnerable", at = @At("HEAD"), cancellable = true)
    @Inject(method = "isInvulnerableTo", at = @At("HEAD"), cancellable = true)
    
    // 防止死亡
    @Inject(method = "isDeadOrDying", at = @At("HEAD"), cancellable = true)
    
    // 持续重置受伤值
    @Inject(method = "tick", at = @At("HEAD"))
    @Inject(method = "baseTick", at = @At("TAIL"))
}
```

### 核心机制
1. **实体类型检查**: 通过`entity.getType().toString()`获取实体类型ID
2. **字符串匹配**: 与预定义的无敌生物列表进行精确匹配
3. **方法拦截**: 在关键方法执行前拦截并修改返回值
4. **持续维护**: 在每个tick中重置受伤状态和维持满血
5. **死亡拦截**: 完全阻止`die`方法的执行
6. **强制无敌**: 持续设置无敌标志和满血状态
7. **多重保护**: 在tick、baseTick、hurt、die等多个关键点进行保护

### 骗过死亡的关键实现
```java
// 1. 拦截hurt方法，阻止任何伤害
@Inject(method = "hurt", at = @At("HEAD"), cancellable = true)

// 2. 拦截die方法，完全取消死亡
@Inject(method = "die", at = @At("HEAD"), cancellable = true)

// 3. 重写isDeadOrDying，永远返回false
@Inject(method = "isDeadOrDying", at = @At("HEAD"), cancellable = true)

// 4. 在tick中持续监控和恢复
- entity.setInvulnerable(true)
- entity.setHealth(entity.getMaxHealth())
- 检测死亡状态并立即恢复

// 5. 在baseTick中额外保护
- 重置受伤动画
- 确保生命值不为0
```

## 文件结构

```
src/main/java/com/github/b4ndithelps/beta1/
├── beta1.java                           # 主模组类
└── mixin/
    └── InvulnerableEntitiesMixin.java   # 无敌效果Mixin

src/main/resources/
├── beta1.mixins.json                    # Mixin配置文件
└── META-INF/
    └── mods.toml                        # 模组元数据
```

## 兼容性

### 模组兼容性
- 与Tensura模组完全兼容
- 不影响其他生物的正常行为
- 只对指定的6个生物生效

### 版本兼容性
- Minecraft 1.19.2
- Forge 43.3.0+
- 需要Tensura模组作为依赖

## 安装说明

1. 确保已安装Minecraft 1.19.2和对应版本的Forge
2. 确保已安装Tensura模组
3. 将生成的jar文件放入`mods`文件夹
4. 启动游戏

## 构建说明

```bash
# 编译代码
./gradlew compileJava

# 构建完整模组
./gradlew build
```

生成的jar文件位于`build/libs/`目录中。

## 注意事项

### 性能影响
- Mixin在每个tick中都会检查实体类型
- 对于大量生物的服务器可能有轻微性能影响
- 建议在生产环境中监控性能

### 调试信息
- 编译时会有Mixin方法描述符警告，这是正常的
- 这些警告不影响模组功能

### 扩展性
如需添加更多无敌生物，只需在`INVULNERABLE_ENTITIES`数组中添加对应的实体ID：

```java
private static final String[] INVULNERABLE_ENTITIES = {
    "trbeyond:dryad",
    "trbeyond:kaijin", 
    "trbeyond:gozul",
    "trbeyond:guild_lady",
    "trbeyond:koby",
    "trbeyond:kurobe",
    // 在这里添加新的实体ID
    "modid:new_entity"
};
```

## 致谢
本模组的无敌机制参考了`HurtDummyEntity`的实现，确保了与原有系统的完美兼容性。
