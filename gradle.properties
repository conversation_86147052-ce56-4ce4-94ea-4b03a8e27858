org.gradle.jvmargs=-Xmx3G
org.gradle.daemon=false
# The Minecraft version must agree with the Forge version to get a valid artifact
minecraft_version=1.19.2
# The Minecraft version range can use any release version of Minecraft as bounds.
# Snapshots, pre-releases, and release candidates are not guaranteed to sort properly
# as they do not follow standard versioning conventions.
minecraft_version_range=[1.19.2,1.20)
# The Forge version must agree with the Minecraft version to get a valid artifact
forge_version=43.4.6
# The Forge version range can use any version of Forge as bounds or match the loader version range
forge_version_range=[43,)
# The loader version range can only use the major version of Forge/FML as bounds
loader_version_range=[43,)
# The mapping channel to use for mappings.
# The default set of supported mapping channels are ["official", "snapshot", "snapshot_nodoc", "stable", "stable_nodoc"].
# Additional mapping channels can be registered through the "channelProviders" extension in a Gradle plugin.
#
# | Channel   | Version              |                                                                                |
# |-----------|----------------------|--------------------------------------------------------------------------------|
# | official  | MCVersion            | Official field/method names from Mojang mapping files                          |
# | parchment | YYYY.MM.DD-MCVersion | Open community-sourced parameter names and javadocs layered on top of official |
#
# You must be aware of the Mojang license when using the 'official' or 'parchment' mappings.
# See more information here: https://github.com/MinecraftForge/MCPConfig/blob/master/Mojang.md
#
# Parchment is an unofficial project maintained by ParchmentMC, separate from Minecraft Forge.
# Additional setup is needed to use their mappings, see https://parchmentmc.org/docs/getting-started
mapping_channel=parchment
# The mapping version to query from the mapping channel.
# This must match the format required by the mapping channel.
mapping_version=2022.08.14-1.19.2
# The unique mod identifier for the mod. Must be lowercase in English locale. Must fit the regex [a-z][a-z0-9_]{1,63}
# Must match the String constant located in the main mod class annotated with @Mod.
mod_id=beta1
# The human-readable display name for the mod.
mod_name=tensuraskilldisabled
# The license of the mod. Review your options at https://choosealicense.com/. All Rights Reserved is the default.
mod_license=MIT
# The mod version. See https://semver.org/
mod_version=1.0-SNAPSHOT
# The group ID for the mod. It is only important when publishing as an artifact to a Maven repository.
# This should match the base package used for the mod sources.
# See https://maven.apache.org/guides/mini/guide-naming-conventions.html
mod_group_id=com.github.b4ndithelps
# The authors of the mod. This is a simple text string that is used for display purposes in the mod list.
mod_authors=AblazeAQZL
# The description of the mod. This is a simple multiline text string that is used for display purposes in the mod list.
mod_description=Disable skills to prevent players from acquiring them
manasCoreVersion=*******
tensuraVersion=*******
geckoVersion=388172:4407241
terrablenderVersion=563928:4618490
systemProp.http.proxyHost=
systemProp.http.proxyPort=
systemProp.https.proxyHost=
systemProp.https.proxyPort=
systemProp.http.nonProxyHosts=*
systemProp.https.nonProxyHosts=*